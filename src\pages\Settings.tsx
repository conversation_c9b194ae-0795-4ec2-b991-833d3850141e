import React, { useState } from 'react';
import {
  Sun, Moon, ZoomIn, ZoomOut, Bell, Lock, User, Globe, HelpCircle,
  Building2, Smartphone, Edit
} from 'lucide-react';
import { Button } from '../components/ui/button';
import BackButton from '../components/ui/BackButton';

interface SettingsProps {
  isDarkMode: boolean;
  toggleDarkMode: () => void;
}

const Settings: React.FC<SettingsProps> = ({ isDarkMode, toggleDarkMode }) => {
  const [zoom, setZoom] = useState(100);
  const [language, setLanguage] = useState('pt');
  const [notifications, setNotifications] = useState({
    email: true,
    push: true,
    sms: false
  });



  const increaseZoom = () => {
    if (zoom < 150) {
      setZoom(zoom + 10);
      document.documentElement.style.fontSize = `${zoom + 10}%`;
    }
  };

  const decreaseZoom = () => {
    if (zoom > 80) {
      setZoom(zoom - 10);
      document.documentElement.style.fontSize = `${zoom - 10}%`;
    }
  };



  return (
    <div className="min-h-screen max-w-full overflow-x-hidden">
      <div className={`container mx-auto px-4 py-8 pt-24 max-w-6xl ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
        <div className="flex items-center space-x-4 mb-8">
          <BackButton isDarkMode={isDarkMode} />
          <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Configurações</h1>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="col-span-1 md:col-span-2">
            {/* Contas de Recebimento */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-6 mb-8`}>
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-full" style={{backgroundColor: '#3569b0'}}>
                    <Building2 className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h2 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      Contas de Recebimento
                    </h2>
                    <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Configure suas contas para receber pagamentos
                    </p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  className="flex items-center space-x-2"
                  style={{borderColor: '#3569b0', color: '#3569b0'}}
                >
                  <Building2 className="w-4 h-4" />
                  <span>Adicionar Conta</span>
                </Button>
              </div>

              {/* Contas Bancárias */}
              <div className="space-y-4">
                <div className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Building2 className="w-8 h-8" style={{color: '#3569b0'}} />
                      <div>
                        <p className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Banco BAI</p>
                        <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>**** **** **** 1234</p>
                        <p className={`text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-500'}`}>João Silva</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full" style={{backgroundColor: '#3569b0', color: 'white'}}>
                        Principal
                      </span>
                      <Button variant="ghost" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                <div className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Smartphone className="w-8 h-8" style={{color: '#3569b0'}} />
                      <div>
                        <p className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Multicaixa Express</p>
                        <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>+244 923 456 789</p>
                        <p className={`text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-500'}`}>João Silva</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Aparência */}
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-6 mb-8`}>
            <h2 className={`text-xl font-semibold mb-4 flex items-center ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              <Sun className="w-5 h-5 mr-2" style={{color: '#3569b0'}} />
              Aparência
            </h2>

            <div className="space-y-6">
              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Modo de Exibição
                </label>
                <div className="flex space-x-4">
                  <Button
                    variant={!isDarkMode ? "default" : "outline"}
                    className="flex items-center"
                    onClick={toggleDarkMode}
                  >
                    <Sun className="w-4 h-4 mr-2" />
                    Claro
                  </Button>
                  <Button
                    variant={isDarkMode ? "default" : "outline"}
                    className="flex items-center"
                    onClick={toggleDarkMode}
                  >
                    <Moon className="w-4 h-4 mr-2" />
                    Escuro
                  </Button>
                </div>
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Tamanho da Fonte ({zoom}%)
                </label>
                <div className="flex items-center space-x-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={decreaseZoom}
                    disabled={zoom <= 80}
                  >
                    <ZoomOut className="w-4 h-4" />
                  </Button>

                  <div className={`w-full ${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'} rounded-full h-2.5`}>
                    <div
                      className="h-2.5 rounded-full"
                      style={{
                        backgroundColor: '#3569b0',
                        width: `${((zoom - 80) / 70) * 100}%`
                      }}
                    ></div>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={increaseZoom}
                    disabled={zoom >= 150}
                  >
                    <ZoomIn className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Idioma
                </label>
                <select
                  value={language}
                  onChange={(e) => setLanguage(e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'}`}
                  style={{focusRingColor: '#3569b0'}}
                >
                  <option value="pt">Português</option>
                  <option value="en">English</option>
                  <option value="es">Español</option>
                  <option value="fr">Français</option>
                </select>
              </div>
            </div>
          </div>

          {/* Notificações */}
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-6 mb-8`}>
            <h2 className={`text-xl font-semibold mb-4 flex items-center ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              <Bell className="w-5 h-5 mr-2" style={{color: '#3569b0'}} />
              Notificações
            </h2>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Notificações por Email</h3>
                  <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Receba atualizações sobre seus aluguéis</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={notifications.email}
                    onChange={() => setNotifications({...notifications, email: !notifications.email})}
                    className="sr-only peer"
                  />
                  <div className={`w-11 h-6 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 ${notifications.email ? '' : 'bg-gray-200'}`} style={notifications.email ? {backgroundColor: '#3569b0'} : {}}></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Notificações Push</h3>
                  <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Receba alertas em tempo real</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={notifications.push}
                    onChange={() => setNotifications({...notifications, push: !notifications.push})}
                    className="sr-only peer"
                  />
                  <div className={`w-11 h-6 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 ${notifications.push ? '' : 'bg-gray-200'}`} style={notifications.push ? {backgroundColor: '#3569b0'} : {}}></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Notificações SMS</h3>
                  <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Receba mensagens de texto</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={notifications.sms}
                    onChange={() => setNotifications({...notifications, sms: !notifications.sms})}
                    className="sr-only peer"
                  />
                  <div className={`w-11 h-6 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 ${notifications.sms ? '' : 'bg-gray-200'}`} style={notifications.sms ? {backgroundColor: '#3569b0'} : {}}></div>
                </label>
              </div>
            </div>
          </div>

          {/* Privacidade */}
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-6`}>
            <h2 className={`text-xl font-semibold mb-4 flex items-center ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              <Lock className="w-5 h-5 mr-2" style={{color: '#3569b0'}} />
              Privacidade e Segurança
            </h2>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Autenticação de Dois Fatores</h3>
                  <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Adicione uma camada extra de segurança</p>
                </div>
                <Button variant="outline" size="sm">
                  Configurar
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Gerenciar Dispositivos Conectados</h3>
                  <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Veja e controle onde sua conta está conectada</p>
                </div>
                <Button variant="outline" size="sm">
                  Visualizar
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Alterar Senha</h3>
                  <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Atualize sua senha regularmente</p>
                </div>
                <Button variant="outline" size="sm">
                  Alterar
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="col-span-1">
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-6 sticky top-24`}>
            <h2 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Configurações Rápidas</h2>

            <div className="space-y-4">
              <a href="#" className={`flex items-center space-x-3 p-3 rounded-md ${isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-700'}`}>
                <User className="w-5 h-5" style={{color: '#3569b0'}} />
                <span>Perfil</span>
              </a>
              <a href="#" className={`flex items-center space-x-3 p-3 rounded-md ${isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-700'}`}>
                <Bell className="w-5 h-5" style={{color: '#3569b0'}} />
                <span>Notificações</span>
              </a>
              <a href="#" className={`flex items-center space-x-3 p-3 rounded-md ${isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-700'}`}>
                <Lock className="w-5 h-5" style={{color: '#3569b0'}} />
                <span>Privacidade</span>
              </a>
              <a href="#" className={`flex items-center space-x-3 p-3 rounded-md ${isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-700'}`}>
                <Globe className="w-5 h-5" style={{color: '#3569b0'}} />
                <span>Idioma</span>
              </a>
              <a href="#" className={`flex items-center space-x-3 p-3 rounded-md ${isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-700'}`}>
                <HelpCircle className="w-5 h-5" style={{color: '#3569b0'}} />
                <span>Ajuda</span>
              </a>
            </div>

            <div className={`mt-6 pt-6 border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <Button variant="outline" className="w-full">
                Salvar Alterações
              </Button>
            </div>
          </div>
        </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
