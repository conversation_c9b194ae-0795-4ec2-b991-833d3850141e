import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface User {
  id: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  userType: 'TENANT' | 'LANDLORD';
  role?: 'USER' | 'MODERATOR' | 'MODERATOR_MANAGER' | 'ADMIN';
  accountStatus?: 'PENDING' | 'APPROVED' | 'REJECTED' | 'SUSPENDED';
  isCompany?: boolean;
  profilePicture?: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  nif?: string;
  dateOfBirth: string;
  createdAt: string;
  updatedAt: string;
  companyName?: string;
  companyAddress?: string;
  companyType?: string;
  companyDocuments?: string[];
  bio?: string;
  occupation?: string;
  location?: string;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => Promise<void>;
  sendEmailVerification: () => Promise<void>;
}

interface RegisterData {
  fullName: string;
  email: string;
  password: string;
  phoneNumber: string;
  dateOfBirth: string;
  userType: 'TENANT' | 'LANDLORD';
  isCompany?: boolean;
  nif?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const API_BASE_URL = 'http://localhost:3000';

  // Função para migrar dados do localStorage para o banco de dados
  const migrateLocalStorageData = async (token: string) => {
    try {
      // Migrar favoritos
      const localFavorites = localStorage.getItem('equipmentFavorites');
      if (localFavorites) {
        const favoriteIds = JSON.parse(localFavorites);
        if (favoriteIds.length > 0) {
          for (const equipmentId of favoriteIds) {
            try {
              await fetch(`${API_BASE_URL}/favorites`, {
                method: 'POST',
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ equipmentId }),
              });
            } catch (error) {
              console.log('Erro ao migrar favorito:', equipmentId, error);
            }
          }
          // Limpar localStorage após migração bem-sucedida
          localStorage.removeItem('equipmentFavorites');
        }
      }

      // Migrar carrinho (se implementado no backend)
      const localCart = localStorage.getItem('cart');
      if (localCart) {
        const cartItems = JSON.parse(localCart);
        if (cartItems.length > 0) {
          // Aqui você pode implementar a migração do carrinho se necessário
          // Por enquanto, vamos manter no localStorage para compatibilidade
          console.log('Carrinho mantido no localStorage para compatibilidade');
        }
      }
    } catch (error) {
      console.error('Erro na migração de dados:', error);
    }
  };

  // Verificar token no localStorage ao inicializar
  useEffect(() => {
    const checkAuthState = async () => {
      const storedToken = localStorage.getItem('authToken');
      const storedUser = localStorage.getItem('authUser');

      if (storedToken && storedUser) {
        try {
          // Verificar se o token ainda é válido fazendo uma requisição para o perfil
          const response = await fetch(`${API_BASE_URL}/users/profile`, {
            headers: {
              'Authorization': `Bearer ${storedToken}`,
              'Content-Type': 'application/json',
            },
          });

          if (response.ok) {
            // Token válido, manter usuário logado
            setToken(storedToken);
            setUser(JSON.parse(storedUser));
          } else if (response.status === 401) {
            // Token expirado ou inválido
            console.log('Token expirado, fazendo logout...');
            localStorage.removeItem('authToken');
            localStorage.removeItem('authUser');
          } else if (response.status === 404) {
            // Endpoint não encontrado, mas token pode estar válido
            // Manter usuário logado mas não validar token
            console.log('Endpoint de perfil não encontrado, mantendo sessão...');
            setToken(storedToken);
            setUser(JSON.parse(storedUser));
          } else {
            // Outros erros, limpar dados
            localStorage.removeItem('authToken');
            localStorage.removeItem('authUser');
          }
        } catch (error) {
          // Erro de rede, manter usuário logado se os dados existem
          console.log('Erro de rede ao verificar token, mantendo sessão...');
          setToken(storedToken);
          setUser(JSON.parse(storedUser));
        }
      }
      setIsLoading(false);
    };

    checkAuthState();
  }, []);

  const login = async (email: string, password: string): Promise<void> => {
    setIsLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/users/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erro no login');
      }

      const data = await response.json();

      // O backend agora retorna os dados do usuário junto com o token
      if (data.user && data.accessToken) {
        const { accessToken, user: userData } = data;

        // Remover senha dos dados do usuário se existir
        delete userData.password;

        setToken(accessToken);
        setUser(userData);

        // Salvar no localStorage
        localStorage.setItem('authToken', accessToken);
        localStorage.setItem('authUser', JSON.stringify(userData));
      } else {
        // Fallback: buscar dados do usuário se não vieram no login
        const { accessToken } = data;

        const userResponse = await fetch(`${API_BASE_URL}/users/profile`, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
          },
        });

        if (!userResponse.ok) {
          throw new Error('Erro ao buscar dados do usuário');
        }

        const userData = await userResponse.json();

        // Remover senha dos dados do usuário
        delete userData.password;

        setToken(accessToken);
        setUser(userData);

        // Salvar no localStorage
        localStorage.setItem('authToken', accessToken);
        localStorage.setItem('authUser', JSON.stringify(userData));
      }

      // Migrar dados do localStorage para o banco de dados
      await migrateLocalStorageData(accessToken);
    } catch (error) {
      console.error('Erro no login:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: RegisterData): Promise<void> => {
    setIsLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/users`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erro no registro');
      }

      // Após registro bem-sucedido, fazer login automaticamente
      await login(userData.email, userData.password);
    } catch (error) {
      console.error('Erro no registro:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('authToken');
    localStorage.removeItem('authUser');
  };

  const updateUser = async (userData: any): Promise<void> => {
    if (!token || !user) {
      throw new Error('Usuário não autenticado');
    }

    try {
      // Filtrar apenas campos válidos para evitar erro de validação
      const allowedFields = [
        'fullName', 'email', 'phoneNumber', 'dateOfBirth', 'password',
        'profilePicture', 'companyDocuments', 'nif', 'isCompany',
        'companyName', 'companyAddress', 'companyType', 'occupation',
        'location', 'bio', 'biDocument', 'companyCoverImage'
      ];

      const filteredData = Object.keys(userData)
        .filter(key => allowedFields.includes(key) && userData[key] !== undefined && userData[key] !== '')
        .reduce((obj: any, key) => {
          obj[key] = userData[key];
          return obj;
        }, {});

      const response = await fetch(`${API_BASE_URL}/users/${user.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(filteredData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erro ao atualizar usuário');
      }

      const updatedUser = await response.json();
      setUser(updatedUser);
      localStorage.setItem('authUser', JSON.stringify(updatedUser));
    } catch (error) {
      console.error('Erro ao atualizar usuário:', error);
      throw error;
    }
  };

  const sendEmailVerification = async (): Promise<void> => {
    if (!token || !user) {
      throw new Error('Usuário não autenticado');
    }

    try {
      const response = await fetch(`${API_BASE_URL}/users/${user.id}/verify-email`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erro ao enviar email de verificação');
      }
    } catch (error) {
      console.error('Erro ao enviar email de verificação:', error);
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    token,
    isLoading,
    isAuthenticated: !!user && !!token,
    login,
    register,
    logout,
    updateUser,
    sendEmailVerification,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
