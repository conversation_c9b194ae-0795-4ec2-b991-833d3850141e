import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Heart, Share2, Star, ShoppingCart, MapPin, Eye } from 'lucide-react';
import { Button } from '../ui/button';

interface Equipment {
  id: string;
  title: string;
  classCode: string;
  image: string;
  price: {
    daily: number;
    weekly: number;
    monthly: number;
    annual?: number;
  };
  specifications: string[];
  description: string;
}

interface EquipmentGridProps {
  isDarkMode: boolean;
  viewType: 'list' | 'grid' | 'card';
  filteredEquipments: Equipment[];
  favorites: string[];
  toggleFavorite: (id: string) => void;
  quickViewEquipmentId: string | null;
  setQuickViewEquipmentId: (id: string | null) => void;
  setSelectedEquipmentForMap: (equipment: Equipment) => void;
  setIsMapModalOpen: (open: boolean) => void;
  generateRating: (id: string) => number;
  generateDistance: (id: string) => string;
  renderStars: (rating: number) => JSX.Element[];
  formatPrice: (price: number) => string;
}

/**
 * Componente que renderiza a grade de equipamentos
 * Suporta três tipos de visualização: lista, grade e cartões
 */
const EquipmentGrid: React.FC<EquipmentGridProps> = ({
  isDarkMode,
  viewType,
  filteredEquipments,
  favorites,
  toggleFavorite,
  quickViewEquipmentId,
  setQuickViewEquipmentId,
  setSelectedEquipmentForMap,
  setIsMapModalOpen,
  generateRating,
  generateDistance,
  renderStars,
  formatPrice
}) => {
  const navigate = useNavigate();

  if (filteredEquipments.length === 0) {
    return (
      <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-8 text-center`}>
        <p className="text-lg mb-4">Nenhum equipamento encontrado</p>
        <p className={`mb-6 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          Tente ajustar seus filtros ou pesquisar por outro termo.
        </p>
        <Button variant="outline">
          Limpar Todos os Filtros
        </Button>
      </div>
    );
  }

  // Visualização em Lista
  if (viewType === 'list') {
    return (
      <div className="space-y-4">
        {filteredEquipments.map((item) => (
          <div
            key={item.id}
            className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md overflow-hidden`}
          >
            <div className="flex flex-col md:flex-row">
              <div className="w-full md:w-1/3 relative">
                <img
                  src={item.image}
                  alt={item.title}
                  className="w-full h-48 md:h-full object-cover"
                />
                <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                  Disponível
                </div>

                {/* Botões mobile - apenas mobile */}
                <div className="md:hidden absolute bottom-2 left-2 right-2 grid grid-cols-2 gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSelectedEquipmentForMap(item);
                      setIsMapModalOpen(true);
                    }}
                    className="text-xs p-1 h-8 flex-1 flex justify-center items-center md:w-24"
                  >
                    <MapPin className="w-3 h-3 mr-1" />
                    <span>Mapa</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setQuickViewEquipmentId(quickViewEquipmentId === item.id ? null : item.id)}
                    className="text-xs p-1 h-8 flex-1 flex justify-center items-center md:w-24"
                  >
                    <Eye className="w-3 h-3 mr-1" />
                    <span>Ver</span>
                  </Button>
                </div>
              </div>
              <div className="w-full md:w-2/3 p-4">
                <div className="flex items-center mb-1">
                  <h2 className="text-lg font-semibold truncate">{item.title}</h2>
                  <div className="flex ml-2 space-x-1">
                    <button
                      className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                      onClick={() => toggleFavorite(item.id)}
                    >
                      <Heart
                        className={`w-4 h-4 ${favorites.includes(item.id) ? 'fill-red-500 text-red-500' : 'text-gray-400 hover:text-red-500'}`}
                      />
                    </button>
                    <button className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                      <Share2 className="w-4 h-4 text-gray-400 hover:text-blue-500" />
                    </button>
                  </div>
                </div>
                <p className={`text-sm mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  Código de Classe: {item.classCode}
                </p>

                {/* Rating e Distância */}
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-1">
                    {renderStars(generateRating(item.id))}
                    <span className="text-sm text-gray-500">({generateRating(item.id).toFixed(1)})</span>
                  </div>
                  <span className="text-sm font-medium" style={{color: '#3569b0'}}>
                    {generateDistance(item.id)}
                  </span>
                </div>

                {/* Proprietário */}
                <div className="flex items-center space-x-2 mb-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700">
                  <img
                    src={`https://images.unsplash.com/photo-150${parseInt(item.id) % 10}003211169-0a1dd7228f2d?auto=format&fit=facearea&facepad=2&w=64&h=64&q=80`}
                    alt="Proprietário"
                    className="w-8 h-8 rounded-full"
                  />
                  <div className="flex-1">
                    <button
                      onClick={() => navigate(`/user/${parseInt(item.id) % 5 + 1}`)}
                      className="text-sm font-medium hover:text-blue-600 dark:hover:text-blue-400"
                    >
                      {['Carlos Mendes', 'Ana Silva', 'João Santos', 'Maria Costa', 'Pedro Lima'][parseInt(item.id) % 5]}
                    </button>
                    <p className="text-xs text-gray-500">Proprietário</p>
                  </div>
                  <div className="flex items-center space-x-1">
                    {renderStars(4.8).slice(0, 5).map((star, index) =>
                      React.cloneElement(star, { key: index, className: "w-3 h-3 text-yellow-400 fill-current" })
                    )}
                  </div>
                </div>

                {/* Descrição como lista */}
                <ul className={`text-sm mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'} list-disc pl-5`}>
                  {item.description.split('. ').map((point, idx) => (
                    point.trim() && <li key={idx}>{point.trim().replace(/\.$/, '')}</li>
                  ))}
                </ul>

                {/* Botões abaixo da descrição - apenas mobile */}
                <div className="grid grid-cols-2 gap-1 w-full mt-3 mb-1 md:hidden">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigate(`/equipment/${item.id}`)}
                    className="text-xs p-1 h-8 flex-1 flex justify-center items-center md:w-24"
                  >
                    <span>Detalhes</span>
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => navigate('/cart')}
                    className="text-xs p-1 h-8 flex-1 flex justify-center items-center bg-blue-600 hover:bg-blue-700 md:w-24"
                  >
                    <ShoppingCart className="w-3 h-3 mr-1" />
                    <span>Carrinho</span>
                  </Button>
                </div>

                {/* Botões abaixo da descrição - apenas desktop */}
                <div className="hidden md:flex flex-row flex-wrap gap-2 w-full justify-end mt-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSelectedEquipmentForMap(item);
                      setIsMapModalOpen(true);
                    }}
                    className="text-sm w-32"
                  >
                    <MapPin className="w-4 h-4 mr-1" />
                    <span> Localização</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setQuickViewEquipmentId(quickViewEquipmentId === item.id ? null : item.id)}
                    className="text-sm w-32"
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    <span>{quickViewEquipmentId === item.id ? 'Ocultar Detalhes' : 'Vista Rápida'}</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigate(`/equipment/${item.id}`)}
                    className="text-sm w-32"
                  >
                    <span>Ver Detalhes</span>
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => navigate('/cart')}
                    className="text-sm bg-blue-600 hover:bg-blue-700 w-32"
                  >
                    <ShoppingCart className="w-4 h-4 mr-1" />
                    <span >Adicionar ao Carrinho</span>
                  </Button>
                </div>
              </div>
            </div>

            {/* Especificações e preços - visíveis apenas na vista rápida */}
            {quickViewEquipmentId === item.id && (
              <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                <div className="mb-4">
                  <h3 className="text-sm font-medium mb-1">Especificações:</h3>
                  <ul className="text-sm grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-1">
                    {item.specifications.map((spec, i) => (
                      <li key={i} className="flex items-center">
                        <div className={`w-1.5 h-1.5 rounded-full mr-2 ${isDarkMode ? 'bg-blue-400' : 'bg-blue-600'}`}></div>
                        {spec}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="grid grid-cols-3 gap-2 mb-2 w-full sm:w-auto">
                  <div className={`text-center p-2 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                    <p className="text-xs text-gray-500">Diária</p>
                    <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {formatPrice(item.price.daily)}
                    </p>
                  </div>
                  <div className={`text-center p-2 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                    <p className="text-xs text-gray-500">Semanal</p>
                    <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {formatPrice(item.price.weekly)}
                    </p>
                  </div>
                  <div className={`text-center p-2 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                    <p className="text-xs text-gray-500">Mensal</p>
                    <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {formatPrice(item.price.monthly)}
                    </p>
                  </div>
                  {item.price.annual && (
                    <div className={`text-center p-2 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'} col-span-3 mt-2`}>
                      <p className="text-xs text-gray-500">Anual</p>
                      <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {formatPrice(item.price.annual)}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  }

  // Visualização em Grade
  if (viewType === 'grid') {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredEquipments.map((item) => (
          <div
            key={item.id}
            className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md overflow-hidden`}
          >
            <div className="relative">
              <img
                src={item.image}
                alt={item.title}
                className="w-full h-48 object-cover"
              />
              <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                Disponível
              </div>
            </div>
            <div className="p-4">
              <div className="flex items-center justify-between mb-2">
                <h2 className="text-lg font-semibold truncate">{item.title}</h2>
                <div className="flex space-x-1">
                  <button
                    className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                    onClick={() => toggleFavorite(item.id)}
                  >
                    <Heart
                      className={`w-4 h-4 ${favorites.includes(item.id) ? 'fill-red-500 text-red-500' : 'text-gray-400 hover:text-red-500'}`}
                    />
                  </button>
                  <button className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                    <Share2 className="w-4 h-4 text-gray-400 hover:text-blue-500" />
                  </button>
                </div>
              </div>
              <p className={`text-sm mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Código: {item.classCode}
              </p>

              {/* Rating e Distância */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-1">
                  {renderStars(generateRating(item.id))}
                  <span className="text-sm text-gray-500">({generateRating(item.id).toFixed(1)})</span>
                </div>
                <span className="text-sm font-medium" style={{color: '#3569b0'}}>
                  {generateDistance(item.id)}
                </span>
              </div>

              {/* Proprietário */}
              <div className="flex items-center space-x-2 mb-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700">
                <img
                  src={`https://images.unsplash.com/photo-150${parseInt(item.id) % 10}003211169-0a1dd7228f2d?auto=format&fit=facearea&facepad=2&w=64&h=64&q=80`}
                  alt="Proprietário"
                  className="w-8 h-8 rounded-full"
                />
                <div className="flex-1">
                  <button
                    onClick={() => navigate(`/user/${parseInt(item.id) % 5 + 1}`)}
                    className="text-sm font-medium hover:text-blue-600 dark:hover:text-blue-400"
                  >
                    {['Carlos Mendes', 'Ana Silva', 'João Santos', 'Maria Costa', 'Pedro Lima'][parseInt(item.id) % 5]}
                  </button>
                  <p className="text-xs text-gray-500">Proprietário</p>
                </div>
                <div className="flex items-center space-x-1">
                  {renderStars(4.8).slice(0, 5).map((star, index) =>
                    React.cloneElement(star, { key: index, className: "w-3 h-3 text-yellow-400 fill-current" })
                  )}
                </div>
              </div>

              {/* Especificações e preços - visíveis apenas na vista rápida */}
              {quickViewEquipmentId === item.id && (
                <>
                  <p className={`text-sm mb-3 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    {item.description}
                  </p>
                  <div className="mb-3">
                    <h3 className="text-sm font-medium mb-1">Especificações:</h3>
                    <ul className="text-sm space-y-1">
                      {item.specifications.slice(0, 3).map((spec, i) => (
                        <li key={i} className="flex items-center">
                          <div className={`w-1.5 h-1.5 rounded-full mr-2 ${isDarkMode ? 'bg-blue-400' : 'bg-blue-600'}`}></div>
                          {spec}
                        </li>
                      ))}
                      {item.specifications.length > 3 && (
                        <li className="text-sm text-blue-500">+ {item.specifications.length - 3} mais</li>
                      )}
                    </ul>
                  </div>

                  <div className="grid grid-cols-3 gap-2 mb-4">
                    <div className={`text-center p-2 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                      <p className="text-xs text-gray-500">Diária</p>
                      <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {formatPrice(item.price.daily)}
                      </p>
                    </div>
                    <div className={`text-center p-2 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                      <p className="text-xs text-gray-500">Semanal</p>
                      <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {formatPrice(item.price.weekly)}
                      </p>
                    </div>
                    <div className={`text-center p-2 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                      <p className="text-xs text-gray-500">Mensal</p>
                      <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {formatPrice(item.price.monthly)}
                      </p>
                    </div>
                  </div>
                </>
              )}

              <div className="flex flex-wrap gap-2 mt-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedEquipmentForMap(item);
                    setIsMapModalOpen(true);
                  }}
                  className="flex-1"
                >
                  <MapPin className="w-4 h-4 mr-1" />
                  Mapa
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setQuickViewEquipmentId(quickViewEquipmentId === item.id ? null : item.id)}
                  className="flex-1"
                >
                  <Eye className="w-4 h-4 mr-1" />
                  {quickViewEquipmentId === item.id ? 'Ocultar' : 'Vista'}
                </Button>
                <Button
                  size="sm"
                  onClick={() => navigate('/cart')}
                  className="flex-1 bg-blue-600 hover:bg-blue-700"
                >
                  <ShoppingCart className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Visualização em Cartões
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4">
      {filteredEquipments.map((item) => (
        <div
          key={item.id}
          className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md overflow-hidden flex flex-col`}
        >
          <div className="relative">
            <img
              src={item.image}
              alt={item.title}
              className="w-full h-40 object-cover"
            />
            <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded">
              Disponível
            </div>
          </div>
          <div className="p-3 flex-1 flex flex-col">
            <div className="flex justify-between items-start mb-1">
              <h2 className="text-base font-semibold line-clamp-2 pr-2">{item.title}</h2>
              <div className="flex space-x-1 flex-shrink-0">
                <button
                  className="p-0.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                  onClick={() => toggleFavorite(item.id)}
                >
                  <Heart
                    className={`w-3.5 h-3.5 ${favorites.includes(item.id) ? 'fill-red-500 text-red-500' : 'text-gray-400 hover:text-red-500'}`}
                  />
                </button>
                <button className="p-0.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                  <Share2 className="w-3.5 h-3.5 text-gray-400 hover:text-blue-500" />
                </button>
              </div>
            </div>
            <p className={`text-xs mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Código: {item.classCode}
            </p>

            {/* Rating e Distância */}
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-1">
                {renderStars(generateRating(item.id)).slice(0, 5).map((star, index) =>
                  React.cloneElement(star, { key: index, className: "w-3 h-3 text-yellow-400 fill-current" })
                )}
                <span className="text-xs text-gray-500">({generateRating(item.id).toFixed(1)})</span>
              </div>
              <span className="text-xs font-medium" style={{color: '#3569b0'}}>
                {generateDistance(item.id)}
              </span>
            </div>

            {/* Proprietário */}
            <div className="flex items-center space-x-2 mb-2 p-2 rounded-lg bg-gray-50 dark:bg-gray-700">
              <img
                src={`https://images.unsplash.com/photo-150${parseInt(item.id) % 10}003211169-0a1dd7228f2d?auto=format&fit=facearea&facepad=2&w=32&h=32&q=80`}
                alt="Proprietário"
                className="w-6 h-6 rounded-full"
              />
              <div className="flex-1 min-w-0">
                <button
                  onClick={() => navigate(`/user/${parseInt(item.id) % 5 + 1}`)}
                  className="text-xs font-medium hover:text-blue-600 dark:hover:text-blue-400 truncate block"
                >
                  {['Carlos M.', 'Ana S.', 'João S.', 'Maria C.', 'Pedro L.'][parseInt(item.id) % 5]}
                </button>
              </div>
              <div className="flex items-center space-x-1">
                {renderStars(4.8).slice(0, 3).map((star, index) =>
                  React.cloneElement(star, { key: index, className: "w-2.5 h-2.5 text-yellow-400 fill-current" })
                )}
              </div>
            </div>

            {/* Especificações e preços - visíveis apenas na vista rápida */}
            {quickViewEquipmentId === item.id && (
              <>
                <p className={`text-xs mb-2 line-clamp-3 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  {item.description}
                </p>

                <div className="grid grid-cols-3 gap-1 mb-2 mt-auto">
                  <div className={`text-center p-1 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                    <p className="text-[10px] text-gray-500">Diária</p>
                    <p className={`text-xs font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {formatPrice(item.price.daily)}
                    </p>
                  </div>
                  <div className={`text-center p-1 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                    <p className="text-[10px] text-gray-500">Semanal</p>
                    <p className={`text-xs font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {formatPrice(item.price.weekly)}
                    </p>
                  </div>
                  <div className={`text-center p-1 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                    <p className="text-[10px] text-gray-500">Mensal</p>
                    <p className={`text-xs font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {formatPrice(item.price.monthly)}
                    </p>
                  </div>
                </div>
              </>
            )}

            <div className="flex gap-1 mt-auto">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSelectedEquipmentForMap(item);
                  setIsMapModalOpen(true);
                }}
                className="flex-1 h-8 text-xs px-1"
              >
                <MapPin className="w-3 h-3 mr-1" />
                Mapa
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setQuickViewEquipmentId(quickViewEquipmentId === item.id ? null : item.id)}
                className="flex-1 h-8 text-xs px-1"
              >
                <Eye className="w-3 h-3 mr-1" />
                {quickViewEquipmentId === item.id ? 'Ocultar' : 'Vista'}
              </Button>
              <Button
                size="sm"
                onClick={() => navigate('/cart')}
                className="h-8 px-2 bg-blue-600 hover:bg-blue-700"
              >
                <ShoppingCart className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default EquipmentGrid;
