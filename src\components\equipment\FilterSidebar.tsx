import React from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';

interface Category {
  id: string;
  name: string;
  subcategories: { id: string; name: string }[];
}

interface FilterSidebarProps {
  isDarkMode: boolean;
  categories: Category[];
  selectedCategory: string | null;
  setSelectedCategory: (category: string | null) => void;
  selectedSubcategory: string | null;
  setSelectedSubcategory: (subcategory: string | null) => void;
  expandedCategories: Record<string, boolean>;
  toggleCategory: (categoryId: string) => void;
  priceFilter: string | null;
  setPriceFilter: (filter: string | null) => void;
  periodFilter: string | null;
  setPeriodFilter: (filter: string | null) => void;
}

/**
 * Componente da sidebar de filtros para desktop
 * Inclui categorias, subcategorias, filtros de preço e período
 */
const FilterSidebar: React.FC<FilterSidebarProps> = ({
  isDarkMode,
  categories,
  selectedCategory,
  setSelectedCategory,
  selectedSubcategory,
  setSelectedSubcategory,
  expandedCategories,
  toggleCategory,
  priceFilter,
  setPriceFilter,
  periodFilter,
  setPeriodFilter
}) => {
  return (
    <div className={`hidden md:block w-64 flex-shrink-0 ${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md overflow-hidden`}>
      {/* Cabeçalho da categoria */}
      <div className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'} p-4 border-b ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
        <h2 className="font-semibold">Categoria</h2>
      </div>

      <div className="p-4">
        <ul className="space-y-2">
          {categories.map(category => (
            <li key={category.id} className={`${isDarkMode ? 'border-gray-700' : 'border-gray-200'} ${selectedCategory === category.id ? 'border-l-4 border-l-blue-600 pl-2 -ml-3' : ''}`}>
              <div className="flex items-center justify-between">
                <button
                  className={`text-left flex-1 ${selectedCategory === category.id
                      ? isDarkMode ? 'text-blue-400 font-medium' : 'text-blue-600 font-medium'
                      : ''}`}
                  onClick={() => {
                    setSelectedCategory(selectedCategory === category.id ? null : category.id);
                    setSelectedSubcategory(null);
                    toggleCategory(category.id);
                  }}
                >
                  {category.name}
                </button>
                <button
                  onClick={() => toggleCategory(category.id)}
                  className={`${isDarkMode ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700'}`}
                >
                  {expandedCategories[category.id] ? (
                    <ChevronDown className="w-4 h-4" />
                  ) : (
                    <ChevronRight className="w-4 h-4" />
                  )}
                </button>
              </div>

              {expandedCategories[category.id] && (
                <ul className="ml-4 mt-2 space-y-1">
                  {category.subcategories.map(subcategory => (
                    <li key={subcategory.id}>
                      <button
                        className={`text-left w-full py-1 px-2 rounded-md ${selectedSubcategory === subcategory.id
                            ? isDarkMode ? 'bg-blue-900/30 text-blue-400' : 'bg-blue-50 text-blue-600'
                            : isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
                        onClick={() => {
                          setSelectedCategory(category.id);
                          setSelectedSubcategory(
                            selectedSubcategory === subcategory.id ? null : subcategory.id
                          );
                        }}
                      >
                        {subcategory.name}
                      </button>
                    </li>
                  ))}
                </ul>
              )}
            </li>
          ))}
        </ul>
      </div>

      {/* Filtros adicionais */}
      <div className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'} p-4 border-b ${isDarkMode ? 'border-gray-600' : 'border-gray-200'} mt-4`}>
        <h2 className="font-semibold">Filtros</h2>
      </div>

      <div className="p-4">
        <div className="mb-4">
          <h3 className="text-sm font-medium mb-2">Preço</h3>
          <div className="space-y-1">
            <label className="flex items-center">
              <input
                type="radio"
                name="priceFilter"
                value="under100k"
                checked={priceFilter === 'under100k'}
                onChange={() => setPriceFilter('under100k')}
                className="mr-2"
              />
              <span className="text-sm">Até 100.000 Kz</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="priceFilter"
                value="100k-500k"
                checked={priceFilter === '100k-500k'}
                onChange={() => setPriceFilter('100k-500k')}
                className="mr-2"
              />
              <span className="text-sm">100.000 - 500.000 Kz</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="priceFilter"
                value="500k-1m"
                checked={priceFilter === '500k-1m'}
                onChange={() => setPriceFilter('500k-1m')}
                className="mr-2"
              />
              <span className="text-sm">500.000 - 1.000.000 Kz</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="priceFilter"
                value="above1m"
                checked={priceFilter === 'above1m'}
                onChange={() => setPriceFilter('above1m')}
                className="mr-2"
              />
              <span className="text-sm">Acima de 1.000.000 Kz</span>
            </label>
            {priceFilter && (
              <button
                onClick={() => setPriceFilter(null)}
                className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mt-1"
              >
                Limpar filtro
              </button>
            )}
          </div>
        </div>

        <div className="mb-4">
          <h3 className="text-sm font-medium mb-2">Período</h3>
          <div className="space-y-1">
            <label className="flex items-center">
              <input
                type="radio"
                name="periodFilter"
                value="daily"
                checked={periodFilter === 'daily'}
                onChange={() => setPeriodFilter('daily')}
                className="mr-2"
              />
              <span className="text-sm">Diário</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="periodFilter"
                value="weekly"
                checked={periodFilter === 'weekly'}
                onChange={() => setPeriodFilter('weekly')}
                className="mr-2"
              />
              <span className="text-sm">Semanal</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="periodFilter"
                value="monthly"
                checked={periodFilter === 'monthly'}
                onChange={() => setPeriodFilter('monthly')}
                className="mr-2"
              />
              <span className="text-sm">Mensal</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="periodFilter"
                value="annual"
                checked={periodFilter === 'annual'}
                onChange={() => setPeriodFilter('annual')}
                className="mr-2"
              />
              <span className="text-sm">Anual</span>
            </label>
            {periodFilter && (
              <button
                onClick={() => setPeriodFilter(null)}
                className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mt-1"
              >
                Limpar filtro
              </button>
            )}
          </div>
        </div>

        <div className="mb-4">
          <h3 className="text-sm font-medium mb-2">Disponibilidade</h3>
          <div className="space-y-1">
            <label className="flex items-center">
              <input type="checkbox" className="mr-2" />
              <span className="text-sm">Disponível para entrega</span>
            </label>
            <label className="flex items-center">
              <input type="checkbox" className="mr-2" />
              <span className="text-sm">Disponível para retirada</span>
            </label>
          </div>
        </div>

        <div className="border-t my-4 pt-4">
          <h3 className="text-sm font-medium mb-2">Pesquisas Recentes</h3>
          <div className="space-y-2">
            <button
              className={`flex items-center text-sm ${isDarkMode ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-700'}`}
              onClick={() => {
                setSelectedCategory('power');
                setSelectedSubcategory('lighting');
                toggleCategory('power');
              }}
            >
              <ChevronRight className="w-4 h-4 mr-1" />
              Torres de Iluminação
            </button>
            <button
              className={`flex items-center text-sm ${isDarkMode ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-700'}`}
              onClick={() => {
                setSelectedCategory('construction');
                setSelectedSubcategory('excavators');
                toggleCategory('construction');
              }}
            >
              <ChevronRight className="w-4 h-4 mr-1" />
              Escavadeiras
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterSidebar;
