import React, { useState, useRef, useEffect } from 'react';
import {
  Send,
  Search,
  MoreVertical,
  Phone,
  Video,
  Info,
  ArrowLeft,
  Menu,
  MessageCircle,
  Shield,
  Flag,
  AlertTriangle
} from 'lucide-react';
import { Button } from '../components/ui/button';
import BackButton from '../components/ui/BackButton';

interface Contact {
  id: string;
  name: string;
  avatar: string;
  lastMessage: string;
  timestamp: string;
  unreadCount: number;
  isOnline: boolean;
}

interface Message {
  id: string;
  senderId: string;
  content: string;
  timestamp: string;
  isBlocked?: boolean;
  blockedReason?: string;
}

interface MessagesProps {
  isDarkMode: boolean;
}

const Messages: React.FC<MessagesProps> = ({ isDarkMode }) => {
  const [selectedContact, setSelectedContact] = useState<string | null>('1');
  const [newMessage, setNewMessage] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [showWarning, setShowWarning] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Detectar mudanças de tamanho da tela
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const [contacts] = useState<Contact[]>([
    {
      id: '1',
      name: 'João Silva',
      avatar: '/api/placeholder/40/40',
      lastMessage: 'Olá! Tenho interesse no seu equipamento...',
      timestamp: '10:30',
      unreadCount: 2,
      isOnline: true
    },
    {
      id: '2',
      name: 'Maria Santos',
      avatar: '/api/placeholder/40/40',
      lastMessage: 'Quando posso retirar a escavadeira?',
      timestamp: 'Ontem',
      unreadCount: 0,
      isOnline: false
    },
    {
      id: '3',
      name: 'Pedro Costa',
      avatar: '/api/placeholder/40/40',
      lastMessage: 'Obrigado pelo excelente serviço!',
      timestamp: '2 dias',
      unreadCount: 1,
      isOnline: true
    },
    {
      id: '4',
      name: 'Ana Ferreira',
      avatar: '/api/placeholder/40/40',
      lastMessage: 'Preciso de um gerador para amanhã',
      timestamp: '3 dias',
      unreadCount: 0,
      isOnline: false
    }
  ]);

  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      senderId: '1',
      content: 'Olá! Tenho interesse no seu equipamento. Está disponível para esta semana?',
      timestamp: '2024-01-15T10:30:00'
    },
    {
      id: '2',
      senderId: 'current-user',
      content: 'Sim, está disponível! Qual seria o período exato que precisa?',
      timestamp: '2024-01-15T10:35:00'
    },
    {
      id: '3',
      senderId: '1',
      content: 'Preciso de segunda a sexta-feira. Qual o valor total?',
      timestamp: '2024-01-15T10:40:00'
    },
    {
      id: '4',
      senderId: 'current-user',
      content: 'Para 5 dias seria 1.250.000 Kz. Inclui transporte na região de Luanda.',
      timestamp: '2024-01-15T10:45:00'
    }
  ]);

  // Palavras e padrões proibidos
  const blockedPatterns = [
    /\b\d{3}[\s-]?\d{3}[\s-]?\d{3}\b/g,
    /\b\+244[\s-]?\d{3}[\s-]?\d{3}[\s-]?\d{3}\b/g,
    /\b9\d{8}\b/g,
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    /\b(whatsapp|telegram|facebook|instagram|twitter)\b/gi,
    /\b(zap|wpp|face|insta|tt)\b/gi,
    /\b(contacto|contato|telefone|celular|número|email|mail)\b/gi,
    /https?:\/\/[^\s]+/g,
    /www\.[^\s]+/g
  ];

  // Função para verificar conteúdo bloqueado
  const checkBlockedContent = (content: string): { isBlocked: boolean; reason: string } => {
    for (const pattern of blockedPatterns) {
      if (pattern.test(content)) {
        if (pattern.source.includes('\\d')) {
          return { isBlocked: true, reason: 'Números de telefone não são permitidos' };
        }
        if (pattern.source.includes('@')) {
          return { isBlocked: true, reason: 'Endereços de email não são permitidos' };
        }
        if (pattern.source.includes('whatsapp|telegram')) {
          return { isBlocked: true, reason: 'Referências a redes sociais não são permitidas' };
        }
        if (pattern.source.includes('https?')) {
          return { isBlocked: true, reason: 'Links externos não são permitidos' };
        }
        return { isBlocked: true, reason: 'Tentativa de comunicação fora da plataforma detectada' };
      }
    }
    return { isBlocked: false, reason: '' };
  };

  // Função para enviar mensagem
  const sendMessage = () => {
    if (!newMessage.trim() || !selectedContact) return;

    const blockCheck = checkBlockedContent(newMessage);

    if (blockCheck.isBlocked) {
      const blockedMessage: Message = {
        id: Date.now().toString(),
        senderId: 'current-user',
        content: newMessage,
        timestamp: new Date().toISOString(),
        isBlocked: true,
        blockedReason: blockCheck.reason
      };

      setMessages(prev => [...prev, blockedMessage]);
      setShowWarning(true);
      setTimeout(() => setShowWarning(false), 5000);
    } else {
      const message: Message = {
        id: Date.now().toString(),
        senderId: 'current-user',
        content: newMessage,
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, message]);
    }

    setNewMessage('');
  };

  // Scroll para a última mensagem
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Função para formatar timestamp
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('pt-AO', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Filtrar contatos
  const filteredContacts = contacts.filter(contact =>
    contact.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const selectedContactData = contacts.find(c => c.id === selectedContact);

  return (
    <div className={`h-screen flex ${isDarkMode ? 'bg-gray-900' : 'bg-gray-100'} pt-16 max-w-full overflow-x-hidden`}>

      {/* Sidebar de Contatos */}
      <div className={`${
        isMobile
          ? selectedContact
            ? 'hidden'
            : 'w-full'
          : sidebarCollapsed
            ? 'w-16 min-w-[4rem]'
            : 'w-80 min-w-[20rem]'
      } ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} ${!isMobile ? 'border-r' : ''} flex flex-col transition-all duration-300 flex-shrink-0`}>

        {/* Header da Sidebar */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            {!sidebarCollapsed && (
              <div className="flex items-center space-x-3">
                {isMobile && <BackButton isDarkMode={isDarkMode} />}
                <h1 className="text-xl font-semibold">Mensagens</h1>
              </div>
            )}
            {sidebarCollapsed && !isMobile && <BackButton isDarkMode={isDarkMode} />}
            <button
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className={`p-2 rounded-lg ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'} ${isMobile ? 'hidden' : ''}`}
            >
              <Menu className="w-5 h-5" />
            </button>
          </div>

          {!sidebarCollapsed && (
            <div className="mt-4 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Buscar conversas..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full pl-10 pr-4 py-2 rounded-lg border ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                    : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500'
                }`}
              />
            </div>
          )}
        </div>

        {/* Lista de Contatos */}
        <div className="flex-1 overflow-y-auto">
          {filteredContacts.map((contact) => (
            <div
              key={contact.id}
              onClick={() => setSelectedContact(contact.id)}
              className={`p-4 cursor-pointer border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 ${
                selectedContact === contact.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className={`w-12 h-12 rounded-full ${isDarkMode ? 'bg-gray-600' : 'bg-gray-300'} flex items-center justify-center`}>
                    <MessageCircle className="w-6 h-6" />
                  </div>
                  {contact.isOnline && (
                    <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800"></div>
                  )}
                </div>

                {!sidebarCollapsed && (
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium truncate">{contact.name}</h3>
                      <span className="text-xs text-gray-500">{contact.timestamp}</span>
                    </div>
                    <p className="text-sm text-gray-500 truncate mt-1">{contact.lastMessage}</p>
                  </div>
                )}

                {contact.unreadCount > 0 && (
                  <div
                    className="w-5 h-5 rounded-full text-white text-xs flex items-center justify-center"
                    style={{backgroundColor: '#3569b0'}}
                  >
                    {contact.unreadCount}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Área de Chat */}
      <div className="flex-1 flex flex-col min-w-0 overflow-hidden">
        {selectedContactData ? (
          <>
            {/* Header do Chat */}
            <div className={`p-4 border-b ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} flex items-center justify-between`}>
              <div className="flex items-center space-x-3">
                {isMobile && (
                  <button
                    onClick={() => setSelectedContact(null)}
                    className={`p-2 rounded-lg ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
                  >
                    <ArrowLeft className="w-5 h-5" />
                  </button>
                )}
                <div className={`w-10 h-10 rounded-full ${isDarkMode ? 'bg-gray-600' : 'bg-gray-300'} flex items-center justify-center`}>
                  <MessageCircle className="w-5 h-5" />
                </div>
                <div>
                  <h2 className="font-semibold">{selectedContactData.name}</h2>
                  <p className="text-sm text-gray-500">
                    {selectedContactData.isOnline ? 'Online' : 'Offline'}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <button className={`p-2 rounded-lg ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}>
                  <Phone className="w-5 h-5" />
                </button>
                <button className={`p-2 rounded-lg ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}>
                  <Video className="w-5 h-5" />
                </button>
                <button className={`p-2 rounded-lg ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}>
                  <MoreVertical className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Aviso de Segurança */}
            <div className={`p-3 ${isDarkMode ? 'bg-blue-900/20' : 'bg-blue-50'} border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <div className="flex items-start space-x-2">
                <Shield className="w-4 h-4 text-blue-600 mt-0.5" style={{color: '#3569b0'}} />
                <div className="text-xs">
                  <p className="font-medium text-blue-600" style={{color: '#3569b0'}}>Chat Seguro</p>
                  <p className="text-gray-600 dark:text-gray-300">
                    Para sua segurança, não compartilhe informações de contato. Mantenha toda comunicação na plataforma.
                  </p>
                </div>
              </div>
            </div>

            {/* Warning Banner */}
            {showWarning && (
              <div className={`p-3 ${isDarkMode ? 'bg-red-900/20' : 'bg-red-50'} border-b border-red-200`}>
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="w-4 h-4 text-red-600 mt-0.5" />
                  <div className="text-xs">
                    <p className="font-medium text-red-600">Mensagem Bloqueada</p>
                    <p className="text-red-600">
                      Sua mensagem foi bloqueada por conter informações de contato. Use apenas o chat da plataforma.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Mensagens */}
            <div className={`flex-1 overflow-y-auto p-4 space-y-4 ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.senderId === 'current-user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                      message.senderId === 'current-user'
                        ? message.isBlocked
                          ? 'bg-red-500 text-white'
                          : 'text-white'
                        : isDarkMode
                        ? 'bg-gray-700 text-white'
                        : 'bg-gray-200 text-gray-900'
                    }`}
                    style={{
                      backgroundColor: message.senderId === 'current-user' && !message.isBlocked ? '#3569b0' : undefined
                    }}
                  >
                    <p className="text-sm">{message.content}</p>
                    <div className="flex items-center justify-between mt-1">
                      <p className="text-xs opacity-70">{formatTime(message.timestamp)}</p>
                      {message.isBlocked && (
                        <div className="flex items-center space-x-1">
                          <AlertTriangle className="w-3 h-3" />
                          <span className="text-xs">Bloqueada</span>
                        </div>
                      )}
                    </div>
                    {message.isBlocked && message.blockedReason && (
                      <p className="text-xs mt-1 opacity-80">{message.blockedReason}</p>
                    )}
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>

            {/* Input de Mensagem */}
            <div className={`p-4 border-t ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && sendMessage()}
                  placeholder="Digite sua mensagem..."
                  className={`flex-1 px-4 py-2 border rounded-lg ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  }`}
                />
                <Button
                  onClick={sendMessage}
                  disabled={!newMessage.trim()}
                  className="px-4"
                  style={{backgroundColor: '#3569b0'}}
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>

              {/* Footer com botão de denúncia */}
              <div className="mt-2 flex flex-col sm:flex-row sm:justify-between sm:items-center text-xs text-gray-500 space-y-1 sm:space-y-0">
                <div className="flex items-center">
                  <span className="truncate">Não compartilhe: telefone, email, redes sociais</span>
                </div>
                <button className="flex items-center space-x-1 hover:text-red-500 self-start sm:self-auto">
                  <Flag className="w-3 h-3" />
                  <span>Denunciar</span>
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <MessageCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2">Selecione uma conversa</h2>
              <p className="text-gray-500">Escolha um contato para começar a conversar</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Messages;
