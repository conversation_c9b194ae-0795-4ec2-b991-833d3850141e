import React from 'react';
import { X } from 'lucide-react';
import { Button } from '../ui/button';
import MapModal from '../MapModal';

interface Equipment {
  id: string;
  title: string;
  description: string;
  classCode: string;
  price: {
    daily: number;
    weekly: number;
    monthly: number;
    annual?: number;
  };
  image: string;
  category: string;
  subcategory: string;
  specifications: string[];
}

interface MapViewModalProps {
  isDarkMode: boolean;
  isOpen: boolean;
  onClose: () => void;
  equipment: Equipment | null;
}

/**
 * Componente de modal para visualização do equipamento no mapa
 * Exibe a localização do equipamento em um mapa interativo
 */
const MapViewModal: React.FC<MapViewModalProps> = ({
  isDarkMode,
  isOpen,
  onClose,
  equipment
}) => {
  if (!isOpen || !equipment) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className={`relative w-full max-w-5xl h-[80vh] rounded-lg shadow-lg overflow-hidden ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'}`}>
        <div className="absolute top-4 right-4 z-10">
          <Button
            variant="outline"
            size="icon"
            onClick={onClose}
            className="bg-white hover:bg-gray-100 rounded-full"
          >
            <X className="w-5 h-5 text-gray-500" />
          </Button>
        </div>

        <div className="p-4 border-b">
          <h2 className="text-xl font-bold">{equipment.title}</h2>
          <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Localização do equipamento
          </p>
        </div>

        <div className="h-full p-4">
          <MapModal isDarkMode={isDarkMode} />
        </div>
      </div>
    </div>
  );
};

export default MapViewModal;
