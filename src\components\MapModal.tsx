import React from 'react';
import { X } from 'lucide-react';
import { Button } from './ui/button';
import MapEmbed from './MapEmbed';

interface MapModalProps {
  isOpen: boolean;
  onClose: () => void;
  equipmentId: string;
  equipmentTitle: string;
  isDarkMode: boolean;
}

const MapModal: React.FC<MapModalProps> = ({ 
  isOpen, 
  onClose, 
  equipmentId, 
  equipmentTitle,
  isDarkMode 
}) => {
  if (!isOpen) return null;

  // Coordenadas fictícias para demonstração
  const coordinates = {
    lat: -8.838333 + (Number(equipmentId) * 0.01), // Variação baseada no ID para demonstração
    lng: 13.234444 + (Number(equipmentId) * 0.01)  // Coordenadas base de Luanda
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Overlay */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50" 
        onClick={onClose}
      />
      
      {/* Modal */}
      <div 
        className={`relative w-full max-w-4xl max-h-[90vh] rounded-lg shadow-lg overflow-hidden ${
          isDarkMode ? 'bg-gray-800' : 'bg-white'
        }`}
      >
        {/* Header */}
        <div className={`flex items-center justify-between p-4 border-b ${
          isDarkMode ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <h2 className="text-xl font-semibold">
            Localização: {equipmentTitle}
          </h2>
          <button 
            onClick={onClose}
            className={`p-1 rounded-full ${
              isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
            }`}
          >
            <X className="w-6 h-6" />
          </button>
        </div>
        
        {/* Body */}
        <div className="p-4">
          <div className="h-[60vh] w-full">
            <MapEmbed 
              latitude={coordinates.lat} 
              longitude={coordinates.lng} 
              zoom={14}
              title={equipmentTitle}
            />
          </div>
        </div>
        
        {/* Footer */}
        <div className={`flex justify-end p-4 border-t ${
          isDarkMode ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <Button onClick={onClose}>Fechar</Button>
        </div>
      </div>
    </div>
  );
};

export default MapModal;
