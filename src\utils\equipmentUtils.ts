import { Equipment, PeriodFilter, PriceFilter } from '../components/equipment/types';

/**
 * Filtra equipamentos com base em vários critérios
 * 
 * @param equipments Lista de equipamentos para filtrar
 * @param searchTerm Termo de pesquisa para filtrar por título ou descrição
 * @param selectedCategory Categoria selecionada
 * @param selectedSubcategory Subcategoria selecionada
 * @param priceFilter Filtro de preço
 * @param periodFilter Filtro de período
 * @returns Lista de equipamentos filtrados
 */
export const filterEquipments = (
  equipments: Equipment[],
  searchTerm: string,
  selectedCategory: string | null,
  selectedSubcategory: string | null,
  priceFilter: PriceFilter,
  periodFilter: PeriodFilter
): Equipment[] => {
  return equipments.filter(item => {
    // Filtrar por termo de pesquisa
    const matchesSearch = searchTerm === '' ||
      item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase());

    // Filtrar por categoria
    const matchesCategory = selectedCategory === null ||
      item.category === selectedCategory;

    // Filtrar por subcategoria
    const matchesSubcategory = selectedSubcategory === null ||
      item.subcategory === selectedSubcategory;

    // Filtrar por preço
    let matchesPrice = true;
    if (priceFilter) {
      // Determinar qual preço usar com base no filtro de período
      let priceToCheck = item.price.daily; // padrão para diário

      if (periodFilter === 'weekly') {
        priceToCheck = item.price.weekly;
      } else if (periodFilter === 'monthly') {
        priceToCheck = item.price.monthly;
      } else if (periodFilter === 'annual') {
        // Se não tiver preço anual, usar mensal * 12 como aproximação
        priceToCheck = item.price.annual || (item.price.monthly * 12);
      }

      switch (priceFilter) {
        case 'under100k':
          matchesPrice = priceToCheck < 100000;
          break;
        case '100k-500k':
          matchesPrice = priceToCheck >= 100000 && priceToCheck < 500000;
          break;
        case '500k-1m':
          matchesPrice = priceToCheck >= 500000 && priceToCheck < 1000000;
          break;
        case 'above1m':
          matchesPrice = priceToCheck >= 1000000;
          break;
      }
    }

    // Filtrar por período (se o equipamento tem o período selecionado disponível)
    let matchesPeriod = true;
    if (periodFilter) {
      // Verificar se o equipamento tem o período selecionado disponível
      if (periodFilter === 'annual' && !item.price.annual && !item.price.monthly) {
        matchesPeriod = false;
      } else if (periodFilter === 'monthly' && !item.price.monthly) {
        matchesPeriod = false;
      } else if (periodFilter === 'weekly' && !item.price.weekly) {
        matchesPeriod = false;
      } else if (periodFilter === 'daily' && !item.price.daily) {
        matchesPeriod = false;
      }
    }

    return matchesSearch && matchesCategory && matchesSubcategory && matchesPrice && matchesPeriod;
  });
};

/**
 * Ordena equipamentos com base no critério de ordenação
 * 
 * @param equipments Lista de equipamentos para ordenar
 * @param sortOrder Critério de ordenação
 * @returns Lista de equipamentos ordenados
 */
export const sortEquipments = (
  equipments: Equipment[],
  sortOrder: 'relevance' | 'price_asc' | 'price_desc' | 'newest'
): Equipment[] => {
  return [...equipments].sort((a, b) => {
    // Ordenar por relevância (padrão - mantém a ordem original)
    if (sortOrder === 'relevance') {
      return 0;
    }
    // Ordenar por preço (crescente)
    else if (sortOrder === 'price_asc') {
      return a.price.daily - b.price.daily;
    }
    // Ordenar por preço (decrescente)
    else if (sortOrder === 'price_desc') {
      return b.price.daily - a.price.daily;
    }
    // Ordenar por mais recentes (usando o ID como substituto para data de criação)
    else if (sortOrder === 'newest') {
      return parseInt(b.id) - parseInt(a.id);
    }
    return 0;
  });
};
