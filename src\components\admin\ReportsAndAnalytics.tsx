import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import adminApi from '../../services/adminApi';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Package, 
  DollarSign,
  Calendar,
  Download,
  Filter,
  Loader2,
  AlertCircle,
  Pie<PERSON>hart,
  Activity
} from 'lucide-react';

interface ReportsAndAnalyticsProps {
  isDarkMode: boolean;
}

interface StatsData {
  userStats: {
    total: number;
    byType: Array<{ userType: string; _count: { userType: number } }>;
    byStatus: Array<{ accountStatus: string; _count: { accountStatus: number } }>;
    thisMonth: number;
  };
  equipmentStats: {
    total: number;
    byStatus: Array<{ moderationStatus: string; _count: { moderationStatus: number } }>;
    byCategory: Array<{ category: string; _count: { category: number } }>;
    thisMonth: number;
  };
  rentalStats: {
    total: number;
    byStatus: Array<{ status: string; _count: { status: number } }>;
    revenue: number;
    thisMonth: number;
  };
  categoryStats: Array<{
    id: string;
    name: string;
    _count: { Equipment: number };
  }>;
  monthlyGrowth: Array<{
    month: string;
    users: number;
    equipment: number;
    rentals: number;
  }>;
}

const ReportsAndAnalytics: React.FC<ReportsAndAnalyticsProps> = ({ isDarkMode }) => {
  const { user } = useAuth();
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('6months');

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);
      const data = await adminApi.getStats();
      setStats(data);
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 0,
    }).format(value);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
      case 'active':
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'rejected':
      case 'cancelled':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const exportReport = () => {
    // Implementar exportação de relatório
    console.log('Exportando relatório...');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="text-center py-12">
        <AlertCircle className={`w-12 h-12 mx-auto mb-4 ${isDarkMode ? 'text-gray-600' : 'text-gray-400'}`} />
        <h3 className={`text-lg font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Erro ao carregar estatísticas
        </h3>
        <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          Não foi possível carregar os dados de relatórios
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            {user?.role === 'ADMIN' ? 'Relatórios Completos' : 'Relatórios Avançados'}
          </h2>
          <p className={`mt-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Análise detalhada das métricas da plataforma
          </p>
        </div>
        <div className="flex space-x-3">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className={`px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              isDarkMode 
                ? 'bg-gray-700 border-gray-600 text-white' 
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value="1month">Último mês</option>
            <option value="3months">Últimos 3 meses</option>
            <option value="6months">Últimos 6 meses</option>
            <option value="1year">Último ano</option>
          </select>
          <button
            onClick={exportReport}
            className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Download className="w-4 h-4" />
            <span>Exportar</span>
          </button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className={`p-6 rounded-lg shadow-sm ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Total de Usuários
              </p>
              <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {stats.userStats.total}
              </p>
              <p className="text-sm text-green-600">
                +{stats.userStats.thisMonth} este mês
              </p>
            </div>
            <div className="p-3 rounded-full bg-blue-100">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className={`p-6 rounded-lg shadow-sm ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Total de Equipamentos
              </p>
              <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {stats.equipmentStats.total}
              </p>
              <p className="text-sm text-green-600">
                +{stats.equipmentStats.thisMonth} este mês
              </p>
            </div>
            <div className="p-3 rounded-full bg-green-100">
              <Package className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className={`p-6 rounded-lg shadow-sm ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Total de Aluguéis
              </p>
              <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {stats.rentalStats.total}
              </p>
              <p className="text-sm text-green-600">
                +{stats.rentalStats.thisMonth} este mês
              </p>
            </div>
            <div className="p-3 rounded-full bg-purple-100">
              <Activity className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className={`p-6 rounded-lg shadow-sm ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Receita Total
              </p>
              <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {formatCurrency(stats.rentalStats.revenue)}
              </p>
              <p className="text-sm text-green-600">
                Aluguéis concluídos
              </p>
            </div>
            <div className="p-3 rounded-full bg-yellow-100">
              <DollarSign className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Distribution */}
        <div className={`p-6 rounded-lg shadow-sm ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Distribuição de Usuários
          </h3>
          <div className="space-y-3">
            {stats.userStats.byType.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  {item.userType === 'TENANT' ? 'Inquilinos' : 'Locadores'}
                </span>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ 
                        width: `${(item._count.userType / stats.userStats.total) * 100}%` 
                      }}
                    ></div>
                  </div>
                  <span className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {item._count.userType}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Equipment Status */}
        <div className={`p-6 rounded-lg shadow-sm ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Status dos Equipamentos
          </h3>
          <div className="space-y-3">
            {stats.equipmentStats.byStatus.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(item.moderationStatus)}`}>
                  {item.moderationStatus === 'APPROVED' ? 'Aprovados' : 
                   item.moderationStatus === 'PENDING' ? 'Pendentes' : 'Rejeitados'}
                </span>
                <span className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {item._count.moderationStatus}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Categories Performance */}
      <div className={`p-6 rounded-lg shadow-sm ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
        <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Performance por Categoria
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {stats.categoryStats.map((category) => (
            <div key={category.id} className={`p-4 border rounded-lg ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <h4 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {category.name}
              </h4>
              <p className={`text-2xl font-bold mt-2 ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`}>
                {category._count.Equipment}
              </p>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                equipamentos
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Monthly Growth */}
      <div className={`p-6 rounded-lg shadow-sm ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
        <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Crescimento Mensal
        </h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className={`border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                <th className={`text-left py-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Mês</th>
                <th className={`text-left py-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Usuários</th>
                <th className={`text-left py-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Equipamentos</th>
                <th className={`text-left py-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Aluguéis</th>
              </tr>
            </thead>
            <tbody>
              {stats.monthlyGrowth.map((month, index) => (
                <tr key={index} className={`border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                  <td className={`py-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{month.month}</td>
                  <td className={`py-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{month.users}</td>
                  <td className={`py-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{month.equipment}</td>
                  <td className={`py-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{month.rentals}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ReportsAndAnalytics;
