import React from 'react';
import { Shield, Lock, Eye, UserCheck, Database, Globe } from 'lucide-react';

interface PrivacyPolicyProps {
  isDarkMode: boolean;
}

const PrivacyPolicy: React.FC<PrivacyPolicyProps> = ({ isDarkMode }) => {
  return (
    <div className={`min-h-screen pt-16 ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-4">
            <div className={`p-4 rounded-full ${isDarkMode ? 'bg-blue-900' : 'bg-blue-100'}`}>
              <Shield className="w-12 h-12 text-blue-600" style={{color: '#3569b0'}} />
            </div>
          </div>
          <h1 className="text-4xl font-bold mb-4">Política de Privacidade</h1>
          <p className={`text-lg ${isDarkMode ? 'text-gray-400' : 'text-gray-600'} max-w-3xl mx-auto`}>
            Na YMRentals, protegemos seus dados pessoais com o mais alto nível de segurança e transparência.
          </p>
          <p className="text-sm text-gray-500 mt-2">
            Última atualização: Janeiro de 2024
          </p>
        </div>

        {/* Conteúdo */}
        <div className="max-w-4xl mx-auto">
          <div className="grid gap-8">
            
            {/* Seção 1: Coleta de Dados */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-8 rounded-lg shadow-md`}>
              <div className="flex items-center mb-6">
                <Database className="w-6 h-6 text-blue-600 mr-3" style={{color: '#3569b0'}} />
                <h2 className="text-2xl font-bold">1. Coleta de Dados</h2>
              </div>
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Dados que coletamos:</h3>
                <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span><strong>Informações pessoais:</strong> Nome, email, telefone, endereço, data de nascimento, NIF</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span><strong>Dados de conta:</strong> Credenciais de login, preferências, histórico de atividades</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span><strong>Informações de pagamento:</strong> Dados bancários, histórico de transações</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span><strong>Dados de uso:</strong> Logs de acesso, localização, dispositivo utilizado</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Seção 2: Uso dos Dados */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-8 rounded-lg shadow-md`}>
              <div className="flex items-center mb-6">
                <Eye className="w-6 h-6 text-blue-600 mr-3" style={{color: '#3569b0'}} />
                <h2 className="text-2xl font-bold">2. Uso dos Dados</h2>
              </div>
              <div className="space-y-4">
                <p className="text-gray-600 dark:text-gray-300">
                  Utilizamos seus dados pessoais para:
                </p>
                <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span>Processar e gerenciar aluguéis de equipamentos</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span>Verificar identidade e prevenir fraudes</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span>Comunicar sobre serviços e atualizações</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span>Melhorar nossos serviços e experiência do usuário</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span>Cumprir obrigações legais e regulamentares</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Seção 3: Proteção de Dados */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-8 rounded-lg shadow-md`}>
              <div className="flex items-center mb-6">
                <Lock className="w-6 h-6 text-blue-600 mr-3" style={{color: '#3569b0'}} />
                <h2 className="text-2xl font-bold">3. Proteção de Dados</h2>
              </div>
              <div className="space-y-4">
                <p className="text-gray-600 dark:text-gray-300">
                  Implementamos medidas de segurança rigorosas:
                </p>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                    <h4 className="font-semibold mb-2">Criptografia</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Todos os dados são criptografados em trânsito e em repouso usando padrões AES-256
                    </p>
                  </div>
                  <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                    <h4 className="font-semibold mb-2">Acesso Restrito</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Apenas funcionários autorizados têm acesso aos dados pessoais
                    </p>
                  </div>
                  <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                    <h4 className="font-semibold mb-2">Monitoramento</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Sistemas de monitoramento 24/7 para detectar atividades suspeitas
                    </p>
                  </div>
                  <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                    <h4 className="font-semibold mb-2">Backups Seguros</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Backups regulares em servidores seguros e geograficamente distribuídos
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Seção 4: Compartilhamento */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-8 rounded-lg shadow-md`}>
              <div className="flex items-center mb-6">
                <Globe className="w-6 h-6 text-blue-600 mr-3" style={{color: '#3569b0'}} />
                <h2 className="text-2xl font-bold">4. Compartilhamento de Dados</h2>
              </div>
              <div className="space-y-4">
                <p className="text-gray-600 dark:text-gray-300">
                  Não vendemos seus dados pessoais. Compartilhamos apenas quando:
                </p>
                <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span>Necessário para processar transações (processadores de pagamento)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span>Exigido por lei ou autoridades competentes</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span>Com seu consentimento explícito</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span>Para proteger nossos direitos legais</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Seção 5: Seus Direitos */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-8 rounded-lg shadow-md`}>
              <div className="flex items-center mb-6">
                <UserCheck className="w-6 h-6 text-blue-600 mr-3" style={{color: '#3569b0'}} />
                <h2 className="text-2xl font-bold">5. Seus Direitos</h2>
              </div>
              <div className="space-y-4">
                <p className="text-gray-600 dark:text-gray-300">
                  Você tem os seguintes direitos sobre seus dados:
                </p>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className={`p-4 rounded-lg border-l-4 border-blue-600`} style={{borderColor: '#3569b0'}}>
                    <h4 className="font-semibold mb-2">Acesso</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Solicitar uma cópia dos seus dados pessoais
                    </p>
                  </div>
                  <div className={`p-4 rounded-lg border-l-4 border-blue-600`} style={{borderColor: '#3569b0'}}>
                    <h4 className="font-semibold mb-2">Correção</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Corrigir dados incorretos ou incompletos
                    </p>
                  </div>
                  <div className={`p-4 rounded-lg border-l-4 border-blue-600`} style={{borderColor: '#3569b0'}}>
                    <h4 className="font-semibold mb-2">Exclusão</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Solicitar a exclusão dos seus dados pessoais
                    </p>
                  </div>
                  <div className={`p-4 rounded-lg border-l-4 border-blue-600`} style={{borderColor: '#3569b0'}}>
                    <h4 className="font-semibold mb-2">Portabilidade</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Transferir seus dados para outro serviço
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Seção 6: Cookies */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-8 rounded-lg shadow-md`}>
              <h2 className="text-2xl font-bold mb-6">6. Cookies e Tecnologias Similares</h2>
              <div className="space-y-4">
                <p className="text-gray-600 dark:text-gray-300">
                  Utilizamos cookies para melhorar sua experiência:
                </p>
                <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span><strong>Cookies essenciais:</strong> Necessários para o funcionamento do site</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span><strong>Cookies de performance:</strong> Para análise e melhoria do site</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span><strong>Cookies de funcionalidade:</strong> Para lembrar suas preferências</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Seção 7: Contato */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-8 rounded-lg shadow-md`}>
              <h2 className="text-2xl font-bold mb-6">7. Contato</h2>
              <div className="space-y-4">
                <p className="text-gray-600 dark:text-gray-300">
                  Para questões sobre privacidade ou exercer seus direitos, entre em contato:
                </p>
                <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Telefone:</strong> +244 999 999 999</p>
                  <p><strong>Endereço:</strong> Luanda, Kilamba, Bloco X, Edifício 35, 3º andar, porta 36</p>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  );
};

export default PrivacyPolicy;
