import React, { useState } from 'react';
import { Search, MapPin } from 'lucide-react';
import { Button } from './ui/button';
import { useNavigate } from 'react-router-dom';

interface SearchFormProps {
  isDarkMode?: boolean;
  className?: string;
}

const SearchForm: React.FC<SearchFormProps> = ({
  isDarkMode = false,
  className = '',
}) => {
  const navigate = useNavigate();
  const [equipment, setEquipment] = useState('');
  const [location, setLocation] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Build query parameters
    const params = new URLSearchParams();
    if (equipment) params.append('equipment', equipment);
    if (location) params.append('location', location);

    // Navigate to equipment page with search params
    navigate(`/equipment?${params.toString()}`);
  };

  return (
    <div className={`${className}`}>
      <div className={`${isDarkMode ? 'bg-neutral-800/95' : 'bg-white'} rounded-xl p-6 shadow-2xl border ${isDarkMode ? 'border-gray-700' : 'border-gray-100'}`}>
        <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
          Encontre o equipamento ideal para seu projeto
        </h3>
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex flex-col space-y-2">
              <label className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Equipamento
              </label>
              <div className={`flex items-center space-x-2 rounded-lg px-3 py-2 ${
                isDarkMode ? 'bg-gray-700' : 'bg-gray-100'
              }`}>
                <Search className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                <input
                  type="text"
                  placeholder="Tipo de equipamento"
                  value={equipment}
                  onChange={(e) => setEquipment(e.target.value)}
                  className={`w-full bg-transparent focus:outline-none ${
                    isDarkMode
                      ? 'text-white placeholder:text-gray-500'
                      : 'text-gray-800 placeholder:text-gray-400'
                  }`}
                />
              </div>
            </div>
            <div className="flex flex-col space-y-2">
              <label className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Localização
              </label>
              <div className={`flex items-center space-x-2 rounded-lg px-3 py-2 ${
                isDarkMode ? 'bg-gray-700' : 'bg-gray-100'
              }`}>
                <MapPin className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                <input
                  type="text"
                  placeholder="Cidade ou região"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  className={`w-full bg-transparent focus:outline-none ${
                    isDarkMode
                      ? 'text-white placeholder:text-gray-500'
                      : 'text-gray-800 placeholder:text-gray-400'
                  }`}
                />
              </div>
            </div>
            <div className="flex flex-col space-y-2 justify-end">
              <label className="opacity-0 text-sm">Buscar</label>
              <Button
                type="submit"
                className="w-full text-white py-2 h-[42px] shadow-lg transition-all hover:translate-y-[-2px]"
                style={{backgroundColor: '#3569b0'}}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2a5490'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3569b0'}
              >
                Pesquisar Agora
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SearchForm;
