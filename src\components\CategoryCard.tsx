import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from './ui/button';

interface CategoryCardProps {
  id: string;
  name: string;
  image: string;
  description?: string;
  isDarkMode?: boolean;
}

const CategoryCard: React.FC<CategoryCardProps> = ({
  id,
  name,
  image,
  description,
  isDarkMode = false,
}) => {
  return (
    <Link to={`/equipment?category=${id}`}>
      <div className={`h-full hover:shadow-lg transition-all border rounded-lg overflow-hidden mx-auto ${
        isDarkMode
          ? 'border-gray-700 bg-neutral-800 hover:border-blue-500'
          : 'border-gray-200 bg-white hover:border-blue-400'
      }`}
      style={{'--hover-border-color': '#3569b0'} as any}
      onMouseEnter={(e) => e.currentTarget.style.borderColor = '#3569b0'}
      onMouseLeave={(e) => e.currentTarget.style.borderColor = isDarkMode ? '#374151' : '#e5e7eb'}
      >
        <div className="relative h-44 overflow-hidden">
          <img
            src={image}
            alt={name}
            className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end">
            <h3 className="text-base font-semibold text-white p-3">{name}</h3>
          </div>
        </div>
        <div className="px-3 py-2">
          <Button
            variant="outline"
            className="w-full justify-between group text-xs py-1 border-blue-200 text-blue-600 hover:bg-blue-50 hover:border-blue-300"
            style={{
              borderColor: '#3569b0',
              color: '#3569b0'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#eff6ff';
              e.currentTarget.style.borderColor = '#2a5490';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.borderColor = '#3569b0';
            }}
          >
            <span>Ver equipamentos</span>
            <span className="group-hover:translate-x-1 transition-transform">→</span>
          </Button>
        </div>
      </div>
    </Link>
  );
};

export default CategoryCard;
