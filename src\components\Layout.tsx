import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useNotifications } from '../contexts/NotificationContext';
import { useChat } from '../contexts/ChatContext';
import {
  ShoppingCart,
  Bell,
  Settings,
  Menu,
  Heart,
  MessageSquare,
  Package,
  History,
  LogOut,
  User,
  Phone,
  Mail,
  MapPin,
  PlusCircle,
  Home,
  Grid,
  HelpCircle,
  Info,
  X,
  Shield
} from 'lucide-react';
import { Button } from './ui/button';
import apiService from '../services/api';

interface LayoutProps {
  children: React.ReactNode;
  isDarkMode: boolean;
  toggleDarkMode: () => void;
}

const Layout: React.FC<LayoutProps> = ({ children, isDarkMode,  }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [zoom, setZoom] = useState(80);
  const [categories, setCategories] = useState<any[]>([]);
  const [stats, setStats] = useState({
    cities: 0,
    suppliers: 0,
    equipment: 0
  });

  // Check if current page is home
  const isHomePage = location.pathname === '/';

  // Check if current page is admin dashboard
  const isAdminDashboard = location.pathname === '/admin-dashboard';

  // Function to check if a path is active
  const isActivePath = (path: string) => {
    return location.pathname === path;
  };

  // Function to get active link classes
  const getActiveLinkClasses = (path: string) => {
    const isActive = isActivePath(path);
    return `flex items-center space-x-2 w-full px-4 py-1 rounded-md ${
      isActive
        ? isDarkMode
          ? 'bg-blue-900/30 text-blue-400'
          : 'bg-blue-50 text-blue-600'
        : isDarkMode
          ? 'hover:bg-gray-700'
          : 'hover:bg-gray-100'
    }`;
  };

  // Ref for the menu
  const menuRef = useRef<HTMLDivElement>(null);
  const menuButtonRef = useRef<HTMLButtonElement>(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isMenuOpen &&
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        menuButtonRef.current &&
        !menuButtonRef.current.contains(event.target as Node)
      ) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMenuOpen]);

  // Scroll to top when location changes
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);

  // Buscar categorias e estatísticas para o rodapé
  useEffect(() => {
    const fetchFooterData = async () => {
      try {
        // Buscar categorias
        const categoriesResponse = await apiService.get('/categories');
        if (categoriesResponse.data) {
          setCategories(categoriesResponse.data.slice(0, 6)); // Máximo 6 categorias
        }

        // Buscar estatísticas
        const statsResponse = await apiService.get('/stats');
        if (statsResponse.data) {
          setStats({
            cities: statsResponse.data.cities || 12,
            suppliers: statsResponse.data.suppliers || 83,
            equipment: statsResponse.data.equipment || 325
          });
        }
      } catch (error) {
        console.error('Erro ao buscar dados do rodapé:', error);
        // Manter valores padrão em caso de erro
        setStats({
          cities: 12,
          suppliers: 83,
          equipment: 325
        });
      }
    };

    fetchFooterData();
  }, []);

  // Auth state management
  const { user, isAuthenticated, logout, token } = useAuth();
  const { unreadCount } = useNotifications();
  const { unreadCount: messageUnreadCount } = useChat();

  // Navigation state

  // Se for dashboard administrativo, renderizar apenas o children
  if (isAdminDashboard) {
    return <>{children}</>;
  }

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-neutral-900 text-white' : 'bg-white text-neutral-900'}`}>


      {/* Header */}
      <header className={`absolute top-0 left-0 right-0 z-50 w-full ${!isHomePage && isDarkMode ? 'bg-gray-900' : !isHomePage ? 'bg-white shadow-sm' : ''}`}>
        <div className="w-full px-4 py-3 md:container md:mx-auto">
          <nav className="flex items-center justify-between">
            {/* Left side */}
            <div className="flex items-center space-x-6">
              <Link to="/" className="flex items-center space-x-2">
                <img src="/img/ymr.png" alt="YMRentals" className="h-8" />
                <span
                  className={`text-xl font-bold ${isHomePage || isDarkMode ? 'text-white' : ''}`}
                  style={!isHomePage && !isDarkMode ? {color: '#3569b0'} : {}}
                >
                  YMRentals
                </span>
              </Link>
              <div className="hidden lg:flex items-center space-x-6">
                {/* Barra de navegação limpa  */}
              </div>
            </div>

            {/* Right side */}
            <div className="flex items-center space-x-4">
              {/* Mostrar apenas para usuários autenticados */}
              {isAuthenticated && (
                <>
                  {/* Create Listing Button */}
                  <Button
                    variant="outline"
                    disabled={user?.userType === 'TENANT'}
                    className={`hidden md:flex ${isHomePage || isDarkMode ? 'text-white border-white hover:bg-white/20' : 'hover:bg-blue-50'} ${user?.userType === 'TENANT' ? 'opacity-50 cursor-not-allowed' : ''}`}
                    style={!isHomePage && !isDarkMode ? {
                      color: user?.userType === 'TENANT' ? '#6b7280' : '#3569b0',
                      borderColor: user?.userType === 'TENANT' ? '#6b7280' : '#3569b0'
                    } : {}}
                    onClick={() => {
                      if (user?.userType === 'TENANT') {
                        alert('Locatários não podem criar anúncios. Apenas locadores podem criar anúncios.');
                        return;
                      }
                      navigate('/create-listing');
                    }}
                  >
                    <PlusCircle className="w-5 h-5 mr-2" />
                    {user?.userType === 'TENANT' ? 'Apenas Locadores' : 'Criar Anúncio'}
                  </Button>

                  {/* Notifications */}
                  <Button
                    variant="ghost"
                    className={`relative ${isHomePage || isDarkMode ? 'text-white hover:bg-white/20' : 'text-gray-700 hover:bg-gray-100'}`}
                    onClick={() => navigate('/notifications')}
                  >
                    <Bell className="w-5 h-5" />
                    {unreadCount > 0 && (
                      <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                        {unreadCount > 99 ? '99+' : unreadCount}
                      </span>
                    )}
                  </Button>

                  {/* Messages */}
                  <Button
                    variant="ghost"
                    className={`relative ${isHomePage || isDarkMode ? 'text-white hover:bg-white/20' : 'text-gray-700 hover:bg-gray-100'}`}
                    onClick={() => navigate('/messages')}
                  >
                    <MessageSquare className="w-5 h-5" />
                    {messageUnreadCount > 0 && (
                      <span className="absolute -top-1 -right-1 bg-green-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                        {messageUnreadCount > 99 ? '99+' : messageUnreadCount}
                      </span>
                    )}
                  </Button>
                </>
              )}

              {/* Botão de Login para usuários não autenticados */}
              {!isAuthenticated && (
                <Button
                  variant="outline"
                  className={`${isHomePage || isDarkMode ? 'text-white border-white hover:bg-white hover:text-gray-900' : 'text-gray-700 border-gray-300 hover:bg-gray-100'}`}
                  onClick={() => navigate('/auth')}
                >
                  Entrar
                </Button>
              )}

               {/* Menu Hamburguer */}
               <button
                ref={menuButtonRef}
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className={`${isHomePage || isDarkMode ? 'text-white' : 'text-gray-700'}`}
              >
                <Menu className="w-6 h-6" />
              </button>
            </div>
          </nav>

          {/* Menu */}
          {isMenuOpen && (
            <div ref={menuRef} className={`${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-neutral-900'} shadow-lg p-3 absolute z-50 md:mt-2 md:rounded-lg md:right-4 md:min-w-[500px] lg:min-w-[600px] md:top-auto top-0 right-0 w-full md:w-auto md:max-w-md h-screen md:h-auto flex flex-col md:rounded-l-lg`}>
              {/* Botão de fechar (apenas mobile) */}
              <div className="flex justify-end mb-2 md:hidden">
                <button
                  onClick={() => setIsMenuOpen(false)}
                  className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
              <div className="flex-1 flex flex-col md:grid md:grid-cols-2 md:gap-6 overflow-y-auto">
                {/* Coluna 1: Navegação e Minha Conta */}
                <div className="space-y-0.5 md:mb-0">
                  {/* Navegação */}
                  <h3 className={`font-semibold px-4 py-1 text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Navegação</h3>
                  <Link to="/" className={getActiveLinkClasses('/')} onClick={() => setIsMenuOpen(false)}>
                    <Home className="w-5 h-5" style={{color: isActivePath('/') ? '#3569b0' : '#3569b0'}} />
                    <span>Home</span>
                  </Link>
                  <Link to="/equipment" className={getActiveLinkClasses('/equipment')} onClick={() => setIsMenuOpen(false)}>
                    <Package className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Todos Equipamentos</span>
                  </Link>
                  <Link to="/categories" className={getActiveLinkClasses('/categories')} onClick={() => setIsMenuOpen(false)}>
                    <Grid className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Categorias</span>
                  </Link>
                  {/* Itens que requerem autenticação */}
                  {isAuthenticated && (
                    <>
                      {/* Criar Anúncio - apenas para locadores */}
                      {user?.userType === 'LANDLORD' && (
                        <Link to="/create-listing" className={getActiveLinkClasses('/create-listing')} onClick={() => setIsMenuOpen(false)}>
                          <PlusCircle className="w-5 h-5" style={{color: '#3569b0'}} />
                          <span>Criar Anúncio</span>
                        </Link>
                      )}

                      {/* Carrinho - apenas para locatários */}
                      {user?.userType === 'TENANT' && (
                        <Link to="/cart" className={`${getActiveLinkClasses('/cart')} justify-between`} onClick={() => setIsMenuOpen(false)}>
                          <div className="flex items-center space-x-2">
                            <ShoppingCart className="w-5 h-5" style={{color: '#3569b0'}} />
                            <span>Carrinho</span>
                          </div>
                          <span className="bg-blue-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center" style={{backgroundColor: '#3569b0'}}>2</span>
                        </Link>
                      )}
                    </>
                  )}

                  {/* Botão de login para usuários não autenticados */}
                  {!isAuthenticated && (
                    <Link to="/auth" className={getActiveLinkClasses('/auth')} onClick={() => setIsMenuOpen(false)}>
                      <User className="w-5 h-5" style={{color: '#3569b0'}} />
                      <span>Entrar / Registrar</span>
                    </Link>
                  )}

                  {/* Minha Conta - apenas para usuários autenticados */}
                  {isAuthenticated && (
                    <>
                      <h3 className={`font-semibold px-4 py-1 text-sm mt-6 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Minha Conta</h3>
                      <Link to="/profile" className={getActiveLinkClasses('/profile')} onClick={() => setIsMenuOpen(false)}>
                        <User className="w-5 h-5" style={{color: '#3569b0'}} />
                        <span>Meu Perfil</span>
                      </Link>
                      <Link to="/notifications" className={`${getActiveLinkClasses('/notifications')} justify-between`} onClick={() => setIsMenuOpen(false)}>
                        <div className="flex items-center space-x-2">
                          <Bell className="w-5 h-5" style={{color: '#3569b0'}} />
                          <span>Notificações</span>
                        </div>
                        {unreadCount > 0 && (
                          <span className="bg-blue-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center" style={{backgroundColor: '#3569b0'}}>
                            {unreadCount > 99 ? '99+' : unreadCount}
                          </span>
                        )}
                      </Link>
                      <Link to="/messages" className={`${getActiveLinkClasses('/messages')} justify-between`} onClick={() => setIsMenuOpen(false)}>
                        <div className="flex items-center space-x-2">
                          <MessageSquare className="w-5 h-5" style={{color: '#3569b0'}} />
                          <span>Mensagens</span>
                        </div>
                        {messageUnreadCount > 0 && (
                          <span className="bg-blue-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center" style={{backgroundColor: '#3569b0'}}>
                            {messageUnreadCount > 99 ? '99+' : messageUnreadCount}
                          </span>
                        )}
                      </Link>

                      {/* Favoritos - apenas para locatários */}
                      {user?.userType === 'TENANT' && (
                        <Link to="/favorites" className={getActiveLinkClasses('/favorites')} onClick={() => setIsMenuOpen(false)}>
                          <Heart className="w-5 h-5" style={{color: '#3569b0'}} />
                          <span>Favoritos</span>
                        </Link>
                      )}
                    </>
                  )}
                </div>

                {/* Coluna 2: Atividades e Sobre */}
                <div className="space-y-0.5 mt-6 md:mt-0">
                  {/* Atividades - apenas para usuários autenticados */}
                  {isAuthenticated && (
                    <>
                      <h3 className={`font-semibold px-4 py-1 text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Atividades</h3>
                      <Link to="/history" className={getActiveLinkClasses('/history')} onClick={() => setIsMenuOpen(false)}>
                        <History className="w-5 h-5" style={{color: '#3569b0'}} />
                        <span>Histórico</span>
                      </Link>
                      <Link to="/my-rentals" className={getActiveLinkClasses('/my-rentals')} onClick={() => setIsMenuOpen(false)}>
                        <Package className="w-5 h-5" style={{color: '#3569b0'}} />
                        <span>Minhas Rendas</span>
                      </Link>
                      <Link to="/settings" className={getActiveLinkClasses('/settings')} onClick={() => setIsMenuOpen(false)}>
                        <Settings className="w-5 h-5" style={{color: '#3569b0'}} />
                        <span>Configurações</span>
                      </Link>
                      {(user?.role === 'ADMIN' || user?.role === 'MODERATOR' || user?.role === 'MANAGER') && (
                        <Link to="/admin" className={getActiveLinkClasses('/admin')} onClick={() => setIsMenuOpen(false)}>
                          <Shield className="w-5 h-5" style={{color: '#3569b0'}} />
                          <span>Admin</span>
                        </Link>
                      )}
                    </>
                  )}
                  <Link to="/help" className={getActiveLinkClasses('/help')} onClick={() => setIsMenuOpen(false)}>
                    <HelpCircle className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Ajuda</span>
                  </Link>
                  <Link to="/privacy" className={getActiveLinkClasses('/privacy')} onClick={() => setIsMenuOpen(false)}>
                    <Shield className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Privacidade</span>
                  </Link>
                  <Link to="/terms" className={getActiveLinkClasses('/terms')} onClick={() => setIsMenuOpen(false)}>
                    <Info className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Termos de Uso</span>
                  </Link>

                  {/* Sobre */}
                  <h3 className={`font-semibold px-4 py-1 text-sm mt-6 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Sobre</h3>
                  <Link to="/about" className={getActiveLinkClasses('/about')} onClick={() => setIsMenuOpen(false)}>
                    <Info className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Sobre</span>
                  </Link>
                  <Link to="/contact" className={getActiveLinkClasses('/contact')} onClick={() => setIsMenuOpen(false)}>
                    <Phone className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Contato</span>
                  </Link>

                  {/* Botão de logout apenas para usuários autenticados */}
                  {isAuthenticated && (
                    <div className="mt-4">
                      <button
                        onClick={() => {
                          logout();
                          setIsMenuOpen(false);
                          navigate('/');
                        }}
                        className={`flex items-center space-x-2 w-full px-4 py-1 rounded-md text-red-600 ${isDarkMode ? 'hover:bg-red-900/10' : 'hover:bg-red-50'}`}
                      >
                        <LogOut className="w-5 h-5" />
                        <span>Sair</span>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-grow">
        {children}
      </div>

      {/* Footer */}
      <footer className={`${isDarkMode ? 'bg-black' : 'bg-neutral-900'} text-white py-12`}>
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">YMRentals</h3>
              <p className="text-gray-400 mb-4">
                Sua plataforma de aluguel de equipamentos de confiança
              </p>
              <div className="space-y-2">
                <a href="tel:+551199999999" className="flex items-center text-gray-400 hover:text-white transition-colors">
                  <Phone className="h-4 w-4 mr-2" />
                  (11) 9999-9999
                </a>
                <a href="mailto:<EMAIL>" className="flex items-center text-gray-400 hover:text-white transition-colors">
                  <Mail className="h-4 w-4 mr-2" />
                  <EMAIL>
                </a>
                <div className="flex items-start text-gray-400">
                  <MapPin className="h-4 w-4 mr-2 mt-1 flex-shrink-0" />
                  <span>Em Angola, Luanda, centralidade do Kilamba, Bloco X, Edificio 35, 3º andar, porta 36</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Equipamentos</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/equipment" className="hover:text-white transition-colors">Todos Equipamentos</Link></li>
                {categories.map((category) => (
                  <li key={category.id}>
                    <Link
                      to={`/equipment?categoryId=${category.id}`}
                      className="hover:text-white transition-colors"
                    >
                      {category.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Empresa</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/about" className="hover:text-white transition-colors">Sobre nós</Link></li>
                <li><Link to="/contact" className="hover:text-white transition-colors">Contato</Link></li>
                <li><Link to="/help" className="hover:text-white transition-colors">Ajuda</Link></li>
                <li><Link to="/categories" className="hover:text-white transition-colors">Categorias</Link></li>
                {isAuthenticated && (
                  <li><Link to="/create-listing" className="hover:text-white transition-colors">Criar Anúncio</Link></li>
                )}
                <li><Link to="/equipment" className="hover:text-white transition-colors">Equipamentos</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Legal</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/terms" className="hover:text-white transition-colors">Termos de Uso</Link></li>
                <li><Link to="/privacy" className="hover:text-white transition-colors">Privacidade</Link></li>
                <li><Link to="/cookies" className="hover:text-white transition-colors">Cookies</Link></li>
              </ul>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t border-gray-800 text-center md:text-left">
            <p className="text-gray-400">
              &copy; {new Date().getFullYear()} YMRentals. Todos os direitos reservados.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
