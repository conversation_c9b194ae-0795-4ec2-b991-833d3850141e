import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  ShoppingCart,
  Bell,
  Settings,
  Menu,
  Heart,
  MessageSquare,
  Package,
  History,
  LogOut,
  User,
  Phone,
  Mail,
  MapPin,
  PlusCircle,
  Home,
  Grid,
  HelpCircle,
  Info,
  X,
  Shield
} from 'lucide-react';
import { Button } from './ui/button';

interface LayoutProps {
  children: React.ReactNode;
  isDarkMode: boolean;
  toggleDarkMode: () => void;
}

const Layout: React.FC<LayoutProps> = ({ children, isDarkMode,  }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [zoom, setZoom] = useState(80);

  // Check if current page is home
  const isHomePage = location.pathname === '/';

  // Function to check if a path is active
  const isActivePath = (path: string) => {
    return location.pathname === path;
  };

  // Function to get active link classes
  const getActiveLinkClasses = (path: string) => {
    const isActive = isActivePath(path);
    return `flex items-center space-x-2 w-full px-4 py-1 rounded-md ${
      isActive
        ? isDarkMode
          ? 'bg-blue-900/30 text-blue-400'
          : 'bg-blue-50 text-blue-600'
        : isDarkMode
          ? 'hover:bg-gray-700'
          : 'hover:bg-gray-100'
    }`;
  };

  // Ref for the menu
  const menuRef = useRef<HTMLDivElement>(null);
  const menuButtonRef = useRef<HTMLButtonElement>(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isMenuOpen &&
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        menuButtonRef.current &&
        !menuButtonRef.current.contains(event.target as Node)
      ) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMenuOpen]);

  // Scroll to top when location changes
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);

  // Mock user state - replace with actual auth state management
  const [isLoggedIn] = useState(true);
  const user = {
    name: "João Silva",
    email: "<EMAIL>",
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
  };

  // Navigation state

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-neutral-900 text-white' : 'bg-white text-neutral-900'}`}>


      {/* Header */}
      <header className={`absolute top-0 left-0 right-0 z-50 w-full ${!isHomePage && isDarkMode ? 'bg-gray-900' : !isHomePage ? 'bg-white shadow-sm' : ''}`}>
        <div className="w-full px-4 py-3 md:container md:mx-auto">
          <nav className="flex items-center justify-between">
            {/* Left side */}
            <div className="flex items-center space-x-6">
              <Link to="/" className="flex items-center space-x-2">
                <img src="/img/ymr.png" alt="YMRentals" className="h-8" />
                <span
                  className={`text-xl font-bold ${isHomePage || isDarkMode ? 'text-white' : ''}`}
                  style={!isHomePage && !isDarkMode ? {color: '#3569b0'} : {}}
                >
                  YMRentals
                </span>
              </Link>
              <div className="hidden lg:flex items-center space-x-6">
                {/* Barra de navegação limpa  */}
              </div>
            </div>

            {/* Right side */}
            <div className="flex items-center space-x-4">
              {/* Create Listing Button */}
              <Button
                variant="outline"
                className={`hidden md:flex ${isHomePage || isDarkMode ? 'text-white border-white hover:bg-white/20' : 'hover:bg-blue-50'}`}
                style={!isHomePage && !isDarkMode ? {
                  color: '#3569b0',
                  borderColor: '#3569b0'
                } : {}}
                onClick={() => navigate('/create-listing')}
              >
                <PlusCircle className="w-5 h-5 mr-2" />
                Criar Anúncio
              </Button>

              {/* Notifications */}
              <Button
                variant="ghost"
                className={`relative ${isHomePage || isDarkMode ? 'text-white hover:bg-white/20' : 'text-gray-700 hover:bg-gray-100'}`}
                onClick={() => navigate('/notifications')}
              >
                <Bell className="w-5 h-5" />
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                  3
                </span>
              </Button>

              {/* Unknown button, kkkk */}
              <Button
                variant="ghost"
                className={`relative ${isHomePage || isDarkMode ? 'text-white hover:bg-white/20' : 'text-gray-700 hover:bg-gray-100'}`}
                onClick={() => navigate('/messages')}
              >
                <MessageSquare className="w-5 h-5" />
                <span className="absolute -top-1 -right-1 bg-green-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                  5
                </span>
              </Button>

               {/* Menu Hamburguer */}
               <button
                ref={menuButtonRef}
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className={`${isHomePage || isDarkMode ? 'text-white' : 'text-gray-700'}`}
              >
                <Menu className="w-6 h-6" />
              </button>
            </div>
          </nav>

          {/* Menu */}
          {isMenuOpen && (
            <div ref={menuRef} className={`${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-neutral-900'} shadow-lg p-3 absolute z-50 md:mt-2 md:rounded-lg md:right-4 md:min-w-[500px] lg:min-w-[600px] md:top-auto top-0 right-0 w-full md:w-auto md:max-w-md h-screen md:h-auto flex flex-col md:rounded-l-lg`}>
              {/* Botão de fechar (apenas mobile) */}
              <div className="flex justify-end mb-2 md:hidden">
                <button
                  onClick={() => setIsMenuOpen(false)}
                  className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
              <div className="flex-1 flex flex-col md:grid md:grid-cols-2 md:gap-6 overflow-y-auto">
                {/* Coluna 1: Navegação e Minha Conta */}
                <div className="space-y-0.5 md:mb-0">
                  {/* Navegação */}
                  <h3 className={`font-semibold px-4 py-1 text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Navegação</h3>
                  <Link to="/" className={getActiveLinkClasses('/')} onClick={() => setIsMenuOpen(false)}>
                    <Home className="w-5 h-5" style={{color: isActivePath('/') ? '#3569b0' : '#3569b0'}} />
                    <span>Home</span>
                  </Link>
                  <Link to="/equipment" className={getActiveLinkClasses('/equipment')} onClick={() => setIsMenuOpen(false)}>
                    <Package className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Todos Equipamentos</span>
                  </Link>
                  <Link to="/categories" className={getActiveLinkClasses('/categories')} onClick={() => setIsMenuOpen(false)}>
                    <Grid className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Categorias</span>
                  </Link>
                  <Link to="/create-listing" className={getActiveLinkClasses('/create-listing')} onClick={() => setIsMenuOpen(false)}>
                    <PlusCircle className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Criar Anúncio</span>
                  </Link>
                  <Link to="/cart" className={`${getActiveLinkClasses('/cart')} justify-between`} onClick={() => setIsMenuOpen(false)}>
                    <div className="flex items-center space-x-2">
                      <ShoppingCart className="w-5 h-5" style={{color: '#3569b0'}} />
                      <span>Carrinho</span>
                    </div>
                    <span className="bg-blue-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center" style={{backgroundColor: '#3569b0'}}>2</span>
                  </Link>

                  {/* Minha Conta */}
                  <h3 className={`font-semibold px-4 py-1 text-sm mt-6 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Minha Conta</h3>
                  <Link to="/profile" className={getActiveLinkClasses('/profile')} onClick={() => setIsMenuOpen(false)}>
                    <User className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Meu Perfil</span>
                  </Link>
                  <Link to="/notifications" className={`${getActiveLinkClasses('/notifications')} justify-between`} onClick={() => setIsMenuOpen(false)}>
                    <div className="flex items-center space-x-2">
                      <Bell className="w-5 h-5" style={{color: '#3569b0'}} />
                      <span>Notificações</span>
                    </div>
                    <span className="bg-blue-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center" style={{backgroundColor: '#3569b0'}}>3</span>
                  </Link>
                  <Link to="/messages" className={`${getActiveLinkClasses('/messages')} justify-between`} onClick={() => setIsMenuOpen(false)}>
                    <div className="flex items-center space-x-2">
                      <MessageSquare className="w-5 h-5" style={{color: '#3569b0'}} />
                      <span>Mensagens</span>
                    </div>
                    <span className="bg-blue-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center" style={{backgroundColor: '#3569b0'}}>5</span>
                  </Link>
                  <Link to="/favorites" className={getActiveLinkClasses('/favorites')} onClick={() => setIsMenuOpen(false)}>
                    <Heart className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Favoritos</span>
                  </Link>
                </div>

                {/* Coluna 2: Atividades e Sobre */}
                <div className="space-y-0.5 mt-6 md:mt-0">
                  {/* Atividades */}
                  <h3 className={`font-semibold px-4 py-1 text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Atividades</h3>
                  <Link to="/history" className={getActiveLinkClasses('/history')} onClick={() => setIsMenuOpen(false)}>
                    <History className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Histórico</span>
                  </Link>
                  <Link to="/my-rentals" className={getActiveLinkClasses('/my-rentals')} onClick={() => setIsMenuOpen(false)}>
                    <Package className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Minhas Rendas</span>
                  </Link>
                  <Link to="/settings" className={getActiveLinkClasses('/settings')} onClick={() => setIsMenuOpen(false)}>
                    <Settings className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Configurações</span>
                  </Link>
                  <Link to="/admin" className={getActiveLinkClasses('/admin')} onClick={() => setIsMenuOpen(false)}>
                    <Shield className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Admin</span>
                  </Link>
                  <Link to="/help" className={getActiveLinkClasses('/help')} onClick={() => setIsMenuOpen(false)}>
                    <HelpCircle className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Ajuda</span>
                  </Link>
                  <Link to="/privacy" className={getActiveLinkClasses('/privacy')} onClick={() => setIsMenuOpen(false)}>
                    <Shield className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Privacidade</span>
                  </Link>
                  <Link to="/terms" className={getActiveLinkClasses('/terms')} onClick={() => setIsMenuOpen(false)}>
                    <Info className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Termos de Uso</span>
                  </Link>

                  {/* Sobre */}
                  <h3 className={`font-semibold px-4 py-1 text-sm mt-6 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Sobre</h3>
                  <Link to="/about" className={getActiveLinkClasses('/about')} onClick={() => setIsMenuOpen(false)}>
                    <Info className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Sobre</span>
                  </Link>
                  <Link to="/contact" className={getActiveLinkClasses('/contact')} onClick={() => setIsMenuOpen(false)}>
                    <Phone className="w-5 h-5" style={{color: '#3569b0'}} />
                    <span>Contato</span>
                  </Link>
                  <div className="mt-4">
                    <button className={`flex items-center space-x-2 w-full px-4 py-1 rounded-md text-red-600 ${isDarkMode ? 'hover:bg-red-900/10' : 'hover:bg-red-50'}`}>
                      <LogOut className="w-5 h-5" />
                      <span>Sair</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-grow">
        {children}
      </div>

      {/* Footer */}
      <footer className={`${isDarkMode ? 'bg-black' : 'bg-neutral-900'} text-white py-12`}>
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">YMRentals</h3>
              <p className="text-gray-400 mb-4">
                Sua plataforma de aluguel de equipamentos de confiança
              </p>
              <div className="space-y-2">
                <a href="tel:+551199999999" className="flex items-center text-gray-400 hover:text-white transition-colors">
                  <Phone className="h-4 w-4 mr-2" />
                  (11) 9999-9999
                </a>
                <a href="mailto:<EMAIL>" className="flex items-center text-gray-400 hover:text-white transition-colors">
                  <Mail className="h-4 w-4 mr-2" />
                  <EMAIL>
                </a>
                <div className="flex items-start text-gray-400">
                  <MapPin className="h-4 w-4 mr-2 mt-1 flex-shrink-0" />
                  <span>Em Angola, Luanda, centralidade do Kilamba, Bloco X, Edificio 35, 3º andar, porta 36</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Equipamentos</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/equipment" className="hover:text-white transition-colors">Todos Equipamentos</Link></li>
                <li><Link to="/equipment?category=construction" className="hover:text-white transition-colors">Construção</Link></li>
                <li><Link to="/equipment?category=mining" className="hover:text-white transition-colors">Mineração</Link></li>
                <li><Link to="/equipment?category=agriculture" className="hover:text-white transition-colors">Agricultura</Link></li>
                <li><Link to="/equipment?category=industrial" className="hover:text-white transition-colors">Industrial</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Empresa</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/about" className="hover:text-white transition-colors">Sobre nós</Link></li>
                <li><Link to="/contact" className="hover:text-white transition-colors">Contato</Link></li>
                <li><Link to="/blog" className="hover:text-white transition-colors">Blog</Link></li>
                <li><Link to="/careers" className="hover:text-white transition-colors">Carreiras</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Legal</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/terms" className="hover:text-white transition-colors">Termos de Uso</Link></li>
                <li><Link to="/privacy" className="hover:text-white transition-colors">Privacidade</Link></li>
                <li><Link to="/cookies" className="hover:text-white transition-colors">Cookies</Link></li>
              </ul>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t border-gray-800 text-center md:text-left">
            <p className="text-gray-400">
              &copy; {new Date().getFullYear()} YMRentals. Todos os direitos reservados.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
