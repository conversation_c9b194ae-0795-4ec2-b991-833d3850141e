import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ChevronLeft } from 'lucide-react';
import { Button } from '../components/ui/button';
import CategoryCard from '../components/CategoryCard';

// Dados mockados
import { categoria as categoriasMock } from '../../public/data/ymrentalMockData';

interface CategoriesProps {
  isDarkMode: boolean;
}

const Categories: React.FC<CategoriesProps> = ({ isDarkMode }) => {
  // Transformar categorias do mock para o formato esperado pelo componente
  const allCategories = categoriasMock.map(cat => ({
    id: cat.nome.toLowerCase().replace(/\s+/g, '-'),
    name: cat.nome,
    image: cat.imagem || 'https://via.placeholder.com/600',
    description: cat.descricao || cat.escricao || ''
  }));

  return (
    <div className="container mx-auto px-4 py-8 pt-24">
      <div className="flex items-center mb-8">
        <Link to="/">
          <Button variant="ghost" className="mr-4">
            <ChevronLeft className="w-5 h-5 mr-1" />
            Voltar
          </Button>
        </Link>
        <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
          Todas as Categorias
        </h1>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {allCategories.map((category) => (
          <CategoryCard
            key={category.id}
            id={category.id}
            name={category.name}
            image={category.image}
            description={category.description}
            isDarkMode={isDarkMode}
          />
        ))}
      </div>
    </div>
  );
};

export default Categories;
