import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ChevronLeft, Loader2 } from 'lucide-react';
import { Button } from '../components/ui/button';
import CategoryCard from '../components/CategoryCard';
import apiService from '../services/api';

interface CategoriesProps {
  isDarkMode: boolean;
}

const Categories: React.FC<CategoriesProps> = ({ isDarkMode }) => {
  const [categories, setCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadCategories = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await apiService.getCategories();
        setCategories(response || []);
      } catch (err) {
        console.error('Erro ao carregar categorias:', err);
        setError('Não foi possível carregar as categorias. Tente novamente mais tarde.');
        setCategories([]);
      } finally {
        setLoading(false);
      }
    };

    loadCategories();
  }, []);

  return (
    <div className="container mx-auto px-4 py-8 pt-24">
      <div className="flex items-center mb-8">
        <Link to="/">
          <Button variant="ghost" className="mr-4">
            <ChevronLeft className="w-5 h-5 mr-1" />
            Voltar
          </Button>
        </Link>
        <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
          Todas as Categorias
        </h1>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center items-center py-20">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
          <span className={`ml-2 ${isDarkMode ? 'text-white' : 'text-gray-700'}`}>
            Carregando categorias...
          </span>
        </div>
      )}

      {/* Error State */}
      {error && !loading && (
        <div className="text-center py-20">
          <div className={`text-lg font-medium mb-2 ${isDarkMode ? 'text-red-400' : 'text-red-600'}`}>
            Ops! Algo deu errado
          </div>
          <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
            {error}
          </p>
          <Button
            onClick={() => window.location.reload()}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            Tentar Novamente
          </Button>
        </div>
      )}

      {/* Empty State */}
      {!loading && !error && categories.length === 0 && (
        <div className="text-center py-20">
          <div className={`text-lg font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
            Nenhuma categoria encontrada
          </div>
          <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            As categorias ainda não foram configuradas. Volte mais tarde!
          </p>
        </div>
      )}

      {/* Categories Grid */}
      {!loading && !error && categories.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {categories.map((category) => (
            <CategoryCard
              key={category.id}
              id={category.id}
              name={category.name}
              image={category.image || 'https://via.placeholder.com/400x300?text=' + encodeURIComponent(category.name)}
              description={category.description || `Equipamentos da categoria ${category.name}`}
              isDarkMode={isDarkMode}
              count={category._count?.Equipment || 0}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default Categories;
