import React, { useState, useRef, useEffect } from 'react';
import { 
  Send, 
  AlertTriangle, 
  Shield, 
  Phone, 
  Mail, 
  MessageCircle,
  X,
  Flag,
  Info
} from 'lucide-react';
import { Button } from './ui/button';

interface Message {
  id: string;
  senderId: string;
  senderName: string;
  content: string;
  timestamp: string;
  isBlocked?: boolean;
  blockedReason?: string;
}

interface ChatSystemProps {
  isDarkMode: boolean;
  recipientId: string;
  recipientName: string;
  onClose: () => void;
}

const ChatSystem: React.FC<ChatSystemProps> = ({ 
  isDarkMode, 
  recipientId, 
  recipientName, 
  onClose 
}) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      senderId: recipientId,
      senderName: recipientName,
      content: 'Olá! Tenho interesse no seu equipamento. Está disponível para esta semana?',
      timestamp: '2024-01-15T10:30:00'
    },
    {
      id: '2',
      senderId: 'current-user',
      senderName: 'Você',
      content: 'Sim, está disponível! Qual seria o período exato que precisa?',
      timestamp: '2024-01-15T10:35:00'
    }
  ]);

  const [newMessage, setNewMessage] = useState('');
  const [showWarning, setShowWarning] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Palavras e padrões proibidos
  const blockedPatterns = [
    // Números de telefone
    /\b\d{3}[\s-]?\d{3}[\s-]?\d{3}\b/g,
    /\b\+244[\s-]?\d{3}[\s-]?\d{3}[\s-]?\d{3}\b/g,
    /\b9\d{8}\b/g,
    
    // Emails
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    
    // Redes sociais
    /\b(whatsapp|telegram|facebook|instagram|twitter)\b/gi,
    /\b(zap|wpp|face|insta|tt)\b/gi,
    
    // Palavras relacionadas a contato externo
    /\b(contacto|contato|telefone|celular|número|email|mail)\b/gi,
    /\b(fora da plataforma|fora do site|direto|diretamente)\b/gi,
    
    // URLs
    /https?:\/\/[^\s]+/g,
    /www\.[^\s]+/g
  ];

  // Função para verificar se a mensagem contém conteúdo bloqueado
  const checkBlockedContent = (content: string): { isBlocked: boolean; reason: string } => {
    for (const pattern of blockedPatterns) {
      if (pattern.test(content)) {
        if (pattern.source.includes('\\d')) {
          return { isBlocked: true, reason: 'Números de telefone não são permitidos' };
        }
        if (pattern.source.includes('@')) {
          return { isBlocked: true, reason: 'Endereços de email não são permitidos' };
        }
        if (pattern.source.includes('whatsapp|telegram')) {
          return { isBlocked: true, reason: 'Referências a redes sociais não são permitidas' };
        }
        if (pattern.source.includes('https?')) {
          return { isBlocked: true, reason: 'Links externos não são permitidos' };
        }
        return { isBlocked: true, reason: 'Tentativa de comunicação fora da plataforma detectada' };
      }
    }
    return { isBlocked: false, reason: '' };
  };

  // Função para enviar mensagem
  const sendMessage = () => {
    if (!newMessage.trim()) return;

    const blockCheck = checkBlockedContent(newMessage);
    
    if (blockCheck.isBlocked) {
      const blockedMessage: Message = {
        id: Date.now().toString(),
        senderId: 'current-user',
        senderName: 'Você',
        content: newMessage,
        timestamp: new Date().toISOString(),
        isBlocked: true,
        blockedReason: blockCheck.reason
      };
      
      setMessages(prev => [...prev, blockedMessage]);
      setShowWarning(true);
      setTimeout(() => setShowWarning(false), 5000);
    } else {
      const message: Message = {
        id: Date.now().toString(),
        senderId: 'current-user',
        senderName: 'Você',
        content: newMessage,
        timestamp: new Date().toISOString()
      };
      
      setMessages(prev => [...prev, message]);
    }
    
    setNewMessage('');
  };

  // Scroll para a última mensagem
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Função para formatar timestamp
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('pt-AO', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50`}>
      <div className={`w-full max-w-md h-[600px] ${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-xl flex flex-col`}>
        
        {/* Header */}
        <div className={`p-4 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'} flex items-center justify-between`}>
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 rounded-full ${isDarkMode ? 'bg-gray-600' : 'bg-gray-300'} flex items-center justify-center`}>
              <MessageCircle className="w-5 h-5" />
            </div>
            <div>
              <h3 className="font-semibold">{recipientName}</h3>
              <p className="text-sm text-gray-500">Online</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className={`p-2 rounded-full ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Aviso de Segurança */}
        <div className={`p-3 ${isDarkMode ? 'bg-blue-900/20' : 'bg-blue-50'} border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex items-start space-x-2">
            <Shield className="w-4 h-4 text-blue-600 mt-0.5" style={{color: '#3569b0'}} />
            <div className="text-xs">
              <p className="font-medium text-blue-600" style={{color: '#3569b0'}}>Chat Seguro</p>
              <p className="text-gray-600 dark:text-gray-300">
                Para sua segurança, não compartilhe informações de contato. 
                Mantenha toda comunicação na plataforma.
              </p>
            </div>
          </div>
        </div>

        {/* Warning Banner */}
        {showWarning && (
          <div className={`p-3 ${isDarkMode ? 'bg-red-900/20' : 'bg-red-50'} border-b border-red-200`}>
            <div className="flex items-start space-x-2">
              <AlertTriangle className="w-4 h-4 text-red-600 mt-0.5" />
              <div className="text-xs">
                <p className="font-medium text-red-600">Mensagem Bloqueada</p>
                <p className="text-red-600">
                  Sua mensagem foi bloqueada por conter informações de contato. 
                  Use apenas o chat da plataforma.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.senderId === 'current-user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs px-3 py-2 rounded-lg ${
                  message.senderId === 'current-user'
                    ? message.isBlocked
                      ? 'bg-red-500 text-white'
                      : 'bg-blue-600 text-white'
                    : isDarkMode
                    ? 'bg-gray-700 text-white'
                    : 'bg-gray-200 text-gray-900'
                }`}
                style={{
                  backgroundColor: message.senderId === 'current-user' && !message.isBlocked ? '#3569b0' : undefined
                }}
              >
                <p className="text-sm">{message.content}</p>
                <div className="flex items-center justify-between mt-1">
                  <p className="text-xs opacity-70">{formatTime(message.timestamp)}</p>
                  {message.isBlocked && (
                    <div className="flex items-center space-x-1">
                      <AlertTriangle className="w-3 h-3" />
                      <span className="text-xs">Bloqueada</span>
                    </div>
                  )}
                </div>
                {message.isBlocked && message.blockedReason && (
                  <p className="text-xs mt-1 opacity-80">{message.blockedReason}</p>
                )}
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        <div className={`p-4 border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex space-x-2">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
              placeholder="Digite sua mensagem..."
              className={`flex-1 px-3 py-2 border rounded-md ${
                isDarkMode 
                  ? 'bg-gray-700 border-gray-600 text-white' 
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            />
            <Button
              onClick={sendMessage}
              disabled={!newMessage.trim()}
              className="px-3"
              style={{backgroundColor: '#3569b0'}}
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
          
          {/* Dicas de Segurança */}
          <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
            <div className="flex items-center space-x-1">
              <Info className="w-3 h-3" />
              <span>Não compartilhe:</span>
            </div>
            <div className="flex items-center space-x-1">
              <Phone className="w-3 h-3" />
              <span>Telefone</span>
            </div>
            <div className="flex items-center space-x-1">
              <Mail className="w-3 h-3" />
              <span>Email</span>
            </div>
            <div className="flex items-center space-x-1">
              <MessageCircle className="w-3 h-3" />
              <span>Redes Sociais</span>
            </div>
          </div>
        </div>

        {/* Footer com botão de denúncia */}
        <div className={`p-2 border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-200'} text-center`}>
          <button className="flex items-center space-x-1 text-xs text-gray-500 hover:text-red-500 mx-auto">
            <Flag className="w-3 h-3" />
            <span>Denunciar conversa</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatSystem;
