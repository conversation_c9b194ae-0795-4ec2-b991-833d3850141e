const API_BASE_URL = 'http://localhost:3000';

interface ApiResponse<T> {
  data?: T;
  message?: string;
  error?: string;
}

class ApiService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('authToken');
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
    };
  }

  private getToken(): string | null {
    return localStorage.getItem('authToken');
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      // Se for erro 401 (Unauthorized), limpar token e redirecionar para login
      if (response.status === 401) {
        localStorage.removeItem('authToken');
        localStorage.removeItem('authUser');
        // Recarregar a página para forçar logout
        window.location.href = '/auth';
        throw new Error('Sessão expirada. Faça login novamente.');
      }

      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  // Generic GET method
  async get(endpoint: string, params?: any) {
    const queryString = params ? new URLSearchParams(params).toString() : '';
    const url = `${API_BASE_URL}${endpoint}${queryString ? `?${queryString}` : ''}`;
    const response = await fetch(url, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // Auth endpoints
  async login(email: string, password: string) {
    const response = await fetch(`${API_BASE_URL}/users/login`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ email, password }),
    });
    return this.handleResponse(response);
  }

  async register(userData: any) {
    const response = await fetch(`${API_BASE_URL}/users`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(userData),
    });
    return this.handleResponse(response);
  }

  async getUserProfile() {
    const response = await fetch(`${API_BASE_URL}/users/profile`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async updateUser(userId: string, userData: any) {
    const response = await fetch(`${API_BASE_URL}/users/${userId}`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(userData),
    });
    return this.handleResponse(response);
  }

  // Equipment endpoints
  async getEquipments(params?: any) {
    const queryString = params ? new URLSearchParams(params).toString() : '';
    const response = await fetch(`${API_BASE_URL}/equipment?${queryString}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getEquipmentById(id: string) {
    const response = await fetch(`${API_BASE_URL}/equipment/${id}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async createEquipment(equipmentData: any) {
    const response = await fetch(`${API_BASE_URL}/equipment`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(equipmentData),
    });
    return this.handleResponse(response);
  }

  async updateEquipment(id: string, equipmentData: any) {
    const response = await fetch(`${API_BASE_URL}/equipment/${id}`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(equipmentData),
    });
    return this.handleResponse(response);
  }

  async deleteEquipment(id: string) {
    const response = await fetch(`${API_BASE_URL}/equipment/${id}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // Favorites endpoints
  async getFavorites() {
    const response = await fetch(`${API_BASE_URL}/favorites`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async addToFavorites(equipmentId: string) {
    const response = await fetch(`${API_BASE_URL}/favorites/${equipmentId}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async removeFromFavorites(equipmentId: string) {
    const response = await fetch(`${API_BASE_URL}/favorites/${equipmentId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async toggleFavorite(equipmentId: string) {
    const response = await fetch(`${API_BASE_URL}/favorites/toggle/${equipmentId}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async isFavorite(equipmentId: string) {
    const response = await fetch(`${API_BASE_URL}/favorites/check/${equipmentId}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // Chat endpoints
  async getConversations() {
    const response = await fetch(`${API_BASE_URL}/chat`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getMessages(conversationId: string) {
    const response = await fetch(`${API_BASE_URL}/chat/${conversationId}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async sendMessage(conversationId: string, content: string) {
    const response = await fetch(`${API_BASE_URL}/chat/${conversationId}/message`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ content }),
    });
    return this.handleResponse(response);
  }

  async createConversation(participantId: string) {
    const response = await fetch(`${API_BASE_URL}/chat/${participantId}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async markMessagesAsRead(conversationId: string) {
    // Por enquanto, não há endpoint específico para marcar como lida
    // Retornar sucesso para não quebrar a funcionalidade
    return Promise.resolve({ success: true });
  }

  // Admin endpoints
  async getAdminDashboard() {
    const response = await fetch(`${API_BASE_URL}/admin/dashboard`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getAdminAnalytics(params?: any) {
    const queryString = params ? new URLSearchParams(params).toString() : '';
    const response = await fetch(`${API_BASE_URL}/admin/analytics?${queryString}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getAdminUsers(params?: any) {
    const queryString = params ? new URLSearchParams(params).toString() : '';
    const response = await fetch(`${API_BASE_URL}/admin/users?${queryString}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async updateUserStatus(userId: string, status: string, reason?: string) {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}/status`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ status, reason }),
    });
    return this.handleResponse(response);
  }

  async getAdminEquipment(params?: any) {
    const queryString = params ? new URLSearchParams(params).toString() : '';
    const response = await fetch(`${API_BASE_URL}/admin/equipment?${queryString}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }



  // Upload endpoints
  async uploadImage(file: File) {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${API_BASE_URL}/upload/image`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.getToken()}`,
      },
      body: formData,
    });
    return this.handleResponse(response);
  }

  async uploadImages(files: File[]) {
    const formData = new FormData();
    files.forEach(file => formData.append('files', file));

    const response = await fetch(`${API_BASE_URL}/upload/images`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.getToken()}`,
      },
      body: formData,
    });
    return this.handleResponse(response);
  }

  async uploadDocument(file: File) {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${API_BASE_URL}/upload/document`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.getToken()}`,
      },
      body: formData,
    });
    return this.handleResponse(response);
  }

  async uploadProfilePicture(file: File) {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${API_BASE_URL}/upload/profile-picture`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.getToken()}`,
      },
      body: formData,
    });
    return this.handleResponse(response);
  }

  // Moderation endpoints
  async getPendingEquipment(params?: any) {
    const queryString = params ? new URLSearchParams(params).toString() : '';
    const response = await fetch(`${API_BASE_URL}/moderation/equipment/pending?${queryString}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getEquipmentForModeration(id: string) {
    const response = await fetch(`${API_BASE_URL}/moderation/equipment/${id}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async approveEquipment(id: string) {
    const response = await fetch(`${API_BASE_URL}/moderation/equipment/${id}/approve`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async rejectEquipment(id: string, reason: string) {
    const response = await fetch(`${API_BASE_URL}/moderation/equipment/${id}/reject`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ reason }),
    });
    return this.handleResponse(response);
  }

  async getModerationHistory(params?: any) {
    const queryString = params ? new URLSearchParams(params).toString() : '';
    const response = await fetch(`${API_BASE_URL}/moderation/history?${queryString}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getModerationStats() {
    const response = await fetch(`${API_BASE_URL}/moderation/stats`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // User management endpoints



  // Métodos para gerenciamento de usuários

  async blockUser(userId: string) {
    const response = await fetch(`${API_BASE_URL}/users/${userId}/block`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async unblockUser(userId: string) {
    const response = await fetch(`${API_BASE_URL}/users/${userId}/unblock`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async changeUserRole(userId: string, newRole: string) {
    const response = await fetch(`${API_BASE_URL}/users/${userId}/role`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ role: newRole }),
    });
    return this.handleResponse(response);
  }

  // Métodos para gerenciamento de moderadores

  async createModerator(moderatorData: {
    email: string;
    password: string;
    fullName: string;
    phoneNumber: string;
    dateOfBirth: string;
    role: 'MODERATOR' | 'MODERATOR_MANAGER';
  }) {
    const response = await fetch(`${API_BASE_URL}/users/moderators`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(moderatorData),
    });
    return this.handleResponse(response);
  }

  // Método para equipamentos pendentes (mantém a versão da linha 309)

  async rejectLandlord(id: string, reason: string) {
    const response = await fetch(`${API_BASE_URL}/users/landlords/${id}/reject`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ reason }),
    });
    return this.handleResponse(response);
  }

  // Rental endpoints
  async createRental(rentalData: any) {
    const response = await fetch(`${API_BASE_URL}/rentals`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(rentalData),
    });
    return this.handleResponse(response);
  }

  async getMyRentals(type?: 'renter' | 'owner') {
    const url = type ? `${API_BASE_URL}/rentals/my-rentals?type=${type}` : `${API_BASE_URL}/rentals/my-rentals`;
    const response = await fetch(url, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getRentalHistory() {
    const response = await fetch(`${API_BASE_URL}/rentals/history`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getRentalById(id: string) {
    const response = await fetch(`${API_BASE_URL}/rentals/${id}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async updateRentalStatus(id: string, status: string) {
    const response = await fetch(`${API_BASE_URL}/rentals/${id}/status`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ status }),
    });
    return this.handleResponse(response);
  }

  async processRentalPayment(id: string, paymentData: any) {
    const response = await fetch(`${API_BASE_URL}/rentals/${id}/payment`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(paymentData),
    });
    return this.handleResponse(response);
  }

  async cancelRental(id: string) {
    const response = await fetch(`${API_BASE_URL}/rentals/${id}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getUserRentals() {
    const response = await fetch(`${API_BASE_URL}/rental/user`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async uploadPaymentReceipt(rentalId: string, receiptUrl: string) {
    const response = await fetch(`${API_BASE_URL}/rentals/${rentalId}/payment-receipt`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ receiptUrl }),
    });
    return this.handleResponse(response);
  }

  async validatePaymentReceipt(rentalId: string, isApproved: boolean, rejectionReason?: string) {
    const response = await fetch(`${API_BASE_URL}/rentals/${rentalId}/validate-payment`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ isApproved, rejectionReason }),
    });
    return this.handleResponse(response);
  }

  async getPendingPaymentReceipts() {
    const response = await fetch(`${API_BASE_URL}/rentals/pending-receipts`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }



  // Chat endpoints
  async getChats() {
    const response = await fetch(`${API_BASE_URL}/chat`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getChatMessages(chatId: string) {
    const response = await fetch(`${API_BASE_URL}/chat/${chatId}/messages`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }



  // Review endpoints
  async createReview(reviewData: any) {
    const response = await fetch(`${API_BASE_URL}/review`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(reviewData),
    });
    return this.handleResponse(response);
  }

  async getEquipmentReviews(equipmentId: string) {
    const response = await fetch(`${API_BASE_URL}/review/equipment/${equipmentId}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // Address endpoints
  async createAddress(addressData: any) {
    const response = await fetch(`${API_BASE_URL}/address`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(addressData),
    });
    return this.handleResponse(response);
  }

  async getUserAddresses() {
    const response = await fetch(`${API_BASE_URL}/address/user`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // Admin endpoints
  async getDashboard() {
    const response = await fetch(`${API_BASE_URL}/admin/dashboard`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getAdminStats() {
    const response = await fetch(`${API_BASE_URL}/admin/stats`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getRecentActivities() {
    const response = await fetch(`${API_BASE_URL}/admin/recent-activities`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getAllUsers(params?: any) {
    const queryString = params ? new URLSearchParams(params).toString() : '';
    const response = await fetch(`${API_BASE_URL}/admin/users?${queryString}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getUserDetails(userId: string) {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async adminUpdateUser(userId: string, updateData: any) {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(updateData),
    });
    return this.handleResponse(response);
  }

  async toggleUserBlock(userId: string, blocked: boolean, reason?: string) {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}/block`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ blocked, reason }),
    });
    return this.handleResponse(response);
  }

  async deleteUser(userId: string) {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getPendingLandlords() {
    const response = await fetch(`${API_BASE_URL}/admin/pending-landlords`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async validateLandlord(landlordId: string, approved: boolean, reason?: string) {
    const response = await fetch(`${API_BASE_URL}/admin/validate-landlord/${landlordId}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ approved, reason }),
    });
    return this.handleResponse(response);
  }

  async getModerators() {
    const response = await fetch(`${API_BASE_URL}/admin/moderators`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async updateModerator(moderatorId: string, updateData: any) {
    const response = await fetch(`${API_BASE_URL}/admin/moderators/${moderatorId}`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(updateData),
    });
    return this.handleResponse(response);
  }

  async deleteModerator(moderatorId: string) {
    const response = await fetch(`${API_BASE_URL}/admin/moderators/${moderatorId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getAllEquipments(params?: any) {
    const queryString = params ? new URLSearchParams(params).toString() : '';
    const response = await fetch(`${API_BASE_URL}/admin/equipment?${queryString}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async updateEquipmentStatus(equipmentId: string, status: string, reason?: string) {
    const response = await fetch(`${API_BASE_URL}/admin/equipment/${equipmentId}/status`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ status, reason }),
    });
    return this.handleResponse(response);
  }

  async getAllRentals(params?: any) {
    const queryString = params ? new URLSearchParams(params).toString() : '';
    const response = await fetch(`${API_BASE_URL}/admin/rentals?${queryString}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getReports(params?: any) {
    const queryString = params ? new URLSearchParams(params).toString() : '';
    const response = await fetch(`${API_BASE_URL}/admin/reports?${queryString}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async resolveReport(reportId: string, resolution: string) {
    const response = await fetch(`${API_BASE_URL}/admin/reports/${reportId}/resolve`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ resolution }),
    });
    return this.handleResponse(response);
  }

  async getRevenueAnalytics(params?: any) {
    const queryString = params ? new URLSearchParams(params).toString() : '';
    const response = await fetch(`${API_BASE_URL}/admin/revenue?${queryString}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getRealTimeMetrics() {
    const response = await fetch(`${API_BASE_URL}/admin/metrics/real-time`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // ===== DOCUMENT VALIDATION =====
  async getPendingBiValidation() {
    const response = await fetch(`${API_BASE_URL}/admin/pending-bi-validation`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async validateBi(userId: string, approved: boolean, reason?: string) {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}/validate-bi`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ approved, reason }),
    });
    return this.handleResponse(response);
  }

  // ===== PAYMENT VALIDATION =====
  async getAdminPendingPaymentReceipts() {
    const response = await fetch(`${API_BASE_URL}/admin/pending-payment-receipts`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async adminValidatePaymentReceipt(rentalId: string, isApproved: boolean, rejectionReason?: string) {
    const response = await fetch(`${API_BASE_URL}/admin/rentals/${rentalId}/validate-payment`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ isApproved, rejectionReason }),
    });
    return this.handleResponse(response);
  }

  // ===== CATEGORY MANAGEMENT =====
  async uploadCategoryImage(categoryId: string, file: File) {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${API_BASE_URL}/categories/${categoryId}/upload-image`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.getToken()}`,
      },
      body: formData,
    });
    return this.handleResponse(response);
  }

  // ===== MODERATION ENDPOINTS =====
  async getModerationPendingEquipment(params?: any) {
    const queryString = params ? new URLSearchParams(params).toString() : '';
    const response = await fetch(`${API_BASE_URL}/moderation/equipment/pending?${queryString}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }



  // Notification endpoints
  async getNotifications(page = 1, limit = 20) {
    const response = await fetch(`${API_BASE_URL}/notifications?page=${page}&limit=${limit}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getUnreadNotificationsCount() {
    const response = await fetch(`${API_BASE_URL}/notifications/unread-count`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async markNotificationAsRead(notificationId: string) {
    const response = await fetch(`${API_BASE_URL}/notifications/${notificationId}/read`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async markAllNotificationsAsRead() {
    const response = await fetch(`${API_BASE_URL}/notifications/mark-all-read`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async deleteNotification(notificationId: string) {
    const response = await fetch(`${API_BASE_URL}/notifications/${notificationId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // Category endpoints
  async getCategories() {
    const response = await fetch(`${API_BASE_URL}/categories`);
    return this.handleResponse(response);
  }

  async getCategoryById(id: string) {
    const response = await fetch(`${API_BASE_URL}/categories/${id}`);
    return this.handleResponse(response);
  }

  async createCategory(categoryData: any) {
    const response = await fetch(`${API_BASE_URL}/categories`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(categoryData),
    });
    return this.handleResponse(response);
  }

  async updateCategory(id: string, categoryData: any) {
    const response = await fetch(`${API_BASE_URL}/categories/${id}`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(categoryData),
    });
    return this.handleResponse(response);
  }

  async deleteCategory(id: string) {
    const response = await fetch(`${API_BASE_URL}/categories/${id}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // Content endpoints
  async getAboutContent() {
    const response = await fetch(`${API_BASE_URL}/content/about`);
    return this.handleResponse(response);
  }

  async getContactContent() {
    const response = await fetch(`${API_BASE_URL}/content/contact`);
    return this.handleResponse(response);
  }

  async getContentByKey(key: string) {
    const response = await fetch(`${API_BASE_URL}/content/key/${key}`);
    return this.handleResponse(response);
  }

  async updateContentByKey(key: string, contentData: any) {
    const response = await fetch(`${API_BASE_URL}/content/key/${key}`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(contentData),
    });
    return this.handleResponse(response);
  }

  // Stats endpoints
  async getStats() {
    const response = await fetch(`${API_BASE_URL}/stats`);
    return this.handleResponse(response);
  }
}

export const apiService = new ApiService();
export default apiService;
