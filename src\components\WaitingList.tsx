import React, { useState } from 'react';
import { 
  Clock, 
  ArrowUp, 
  ArrowDown, 
  Star, 
  User, 
  Calendar,
  MapPin,
  CreditCard,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react';
import { Button } from './ui/button';

interface WaitingListItem {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  equipmentId: string;
  equipmentName: string;
  requestedStartDate: string;
  requestedEndDate: string;
  location: string;
  totalAmount: number;
  priority: number;
  hasPaidPriority: boolean;
  requestDate: string;
  status: 'waiting' | 'notified' | 'confirmed' | 'expired';
  userPoints: number;
}

interface WaitingListProps {
  isDarkMode: boolean;
}

const WaitingList: React.FC<WaitingListProps> = ({ isDarkMode }) => {
  const [waitingList, setWaitingList] = useState<WaitingListItem[]>([
    {
      id: '1',
      userId: 'user1',
      userName: '<PERSON>',
      userEmail: '<EMAIL>',
      equipmentId: 'eq1',
      equipmentName: 'Escavadeira CAT 320',
      requestedStartDate: '2024-01-25',
      requestedEndDate: '2024-01-30',
      location: 'Luanda, Kilamba',
      totalAmount: 1250000,
      priority: 1,
      hasPaidPriority: true,
      requestDate: '2024-01-15T10:30:00',
      status: 'waiting',
      userPoints: 850
    },
    {
      id: '2',
      userId: 'user2',
      userName: 'Maria Santos',
      userEmail: '<EMAIL>',
      equipmentId: 'eq1',
      equipmentName: 'Escavadeira CAT 320',
      requestedStartDate: '2024-01-26',
      requestedEndDate: '2024-01-31',
      location: 'Luanda, Talatona',
      totalAmount: 1500000,
      priority: 2,
      hasPaidPriority: false,
      requestDate: '2024-01-14T14:20:00',
      status: 'waiting',
      userPoints: 620
    },
    {
      id: '3',
      userId: 'user3',
      userName: 'Pedro Costa',
      userEmail: '<EMAIL>',
      equipmentId: 'eq1',
      equipmentName: 'Escavadeira CAT 320',
      requestedStartDate: '2024-01-28',
      requestedEndDate: '2024-02-02',
      location: 'Luanda, Viana',
      totalAmount: 1750000,
      priority: 3,
      hasPaidPriority: false,
      requestDate: '2024-01-13T09:15:00',
      status: 'waiting',
      userPoints: 420
    },
    {
      id: '4',
      userId: 'user4',
      userName: 'Ana Ferreira',
      userEmail: '<EMAIL>',
      equipmentId: 'eq2',
      equipmentName: 'Gerador 100kVA',
      requestedStartDate: '2024-01-22',
      requestedEndDate: '2024-01-24',
      location: 'Luanda, Maianga',
      totalAmount: 360000,
      priority: 1,
      hasPaidPriority: false,
      requestDate: '2024-01-12T16:45:00',
      status: 'notified',
      userPoints: 1200
    }
  ]);

  // Função para alterar prioridade manualmente
  const changePriority = (itemId: string, direction: 'up' | 'down') => {
    setWaitingList(prev => {
      const items = [...prev];
      const currentIndex = items.findIndex(item => item.id === itemId);
      const currentItem = items[currentIndex];
      
      if (direction === 'up' && currentIndex > 0) {
        // Trocar com o item acima
        const itemAbove = items[currentIndex - 1];
        items[currentIndex - 1] = { ...currentItem, priority: itemAbove.priority };
        items[currentIndex] = { ...itemAbove, priority: currentItem.priority };
      } else if (direction === 'down' && currentIndex < items.length - 1) {
        // Trocar com o item abaixo
        const itemBelow = items[currentIndex + 1];
        items[currentIndex + 1] = { ...currentItem, priority: itemBelow.priority };
        items[currentIndex] = { ...itemBelow, priority: currentItem.priority };
      }
      
      return items.sort((a, b) => a.priority - b.priority);
    });
  };

  // Função para notificar usuário
  const notifyUser = (itemId: string) => {
    setWaitingList(prev => prev.map(item => 
      item.id === itemId ? { ...item, status: 'notified' } : item
    ));
  };

  // Função para confirmar reserva
  const confirmReservation = (itemId: string) => {
    setWaitingList(prev => prev.map(item => 
      item.id === itemId ? { ...item, status: 'confirmed' } : item
    ));
  };

  // Função para remover da lista
  const removeFromList = (itemId: string) => {
    setWaitingList(prev => prev.filter(item => item.id !== itemId));
  };

  // Função para formatar preço
  const formatPrice = (price: number): string => {
    return price.toLocaleString('pt-AO') + ' Kz';
  };

  // Função para obter cor do status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'waiting': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'notified': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'confirmed': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'expired': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  // Função para obter ícone do status
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'waiting': return <Clock className="w-4 h-4" />;
      case 'notified': return <AlertTriangle className="w-4 h-4" />;
      case 'confirmed': return <CheckCircle className="w-4 h-4" />;
      case 'expired': return <XCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  // Função para obter texto do status
  const getStatusText = (status: string) => {
    switch (status) {
      case 'waiting': return 'Aguardando';
      case 'notified': return 'Notificado';
      case 'confirmed': return 'Confirmado';
      case 'expired': return 'Expirado';
      default: return 'Desconhecido';
    }
  };

  // Agrupar por equipamento
  const groupedByEquipment = waitingList.reduce((acc, item) => {
    if (!acc[item.equipmentName]) {
      acc[item.equipmentName] = [];
    }
    acc[item.equipmentName].push(item);
    return acc;
  }, {} as Record<string, WaitingListItem[]>);

  return (
    <div className={`${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'} min-h-screen p-6`}>
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Lista de Espera</h1>
          <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Gerencie a fila de espera e prioridades dos equipamentos
          </p>
        </div>

        {/* Estatísticas */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total na Fila</p>
                <p className="text-2xl font-bold">{waitingList.length}</p>
              </div>
              <Clock className="w-8 h-8 text-blue-500" style={{color: '#3569b0'}} />
            </div>
          </div>
          
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Com Prioridade Paga</p>
                <p className="text-2xl font-bold">{waitingList.filter(item => item.hasPaidPriority).length}</p>
              </div>
              <Star className="w-8 h-8 text-yellow-500" />
            </div>
          </div>
          
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Notificados</p>
                <p className="text-2xl font-bold">{waitingList.filter(item => item.status === 'notified').length}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-orange-500" />
            </div>
          </div>
          
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Confirmados</p>
                <p className="text-2xl font-bold">{waitingList.filter(item => item.status === 'confirmed').length}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </div>
        </div>

        {/* Lista por equipamento */}
        <div className="space-y-8">
          {Object.entries(groupedByEquipment).map(([equipmentName, items]) => (
            <div key={equipmentName} className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md overflow-hidden`}>
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold">{equipmentName}</h3>
                <p className="text-sm text-gray-500">{items.length} pessoas na fila</p>
              </div>
              
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Posição
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Cliente
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Período Solicitado
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Localização
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Valor
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Pontos
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ações
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    {items.sort((a, b) => a.priority - b.priority).map((item, index) => (
                      <tr key={item.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-2">
                            <span className="text-lg font-bold">#{item.priority}</span>
                            {item.hasPaidPriority && (
                              <Star className="w-4 h-4 text-yellow-500 fill-current" />
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-3">
                            <div className={`w-8 h-8 rounded-full ${isDarkMode ? 'bg-gray-600' : 'bg-gray-300'} flex items-center justify-center`}>
                              <User className="w-4 h-4" />
                            </div>
                            <div>
                              <div className="text-sm font-medium">{item.userName}</div>
                              <div className="text-sm text-gray-500">{item.userEmail}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center text-sm">
                            <Calendar className="w-4 h-4 mr-1 text-gray-400" />
                            <div>
                              <div>{new Date(item.requestedStartDate).toLocaleDateString('pt-AO')}</div>
                              <div className="text-gray-500">até {new Date(item.requestedEndDate).toLocaleDateString('pt-AO')}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center text-sm">
                            <MapPin className="w-4 h-4 mr-1 text-gray-400" />
                            {item.location}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center text-sm">
                            <CreditCard className="w-4 h-4 mr-1 text-gray-400" />
                            {formatPrice(item.totalAmount)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium">{item.userPoints} pts</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                            {getStatusIcon(item.status)}
                            <span className="ml-1">{getStatusText(item.status)}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center space-x-1">
                            {/* Botões de prioridade */}
                            <button
                              onClick={() => changePriority(item.id, 'up')}
                              disabled={index === 0}
                              className={`p-1 rounded ${index === 0 ? 'text-gray-300' : 'text-blue-600 hover:bg-blue-50'}`}
                              style={{color: index === 0 ? undefined : '#3569b0'}}
                            >
                              <ArrowUp className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => changePriority(item.id, 'down')}
                              disabled={index === items.length - 1}
                              className={`p-1 rounded ${index === items.length - 1 ? 'text-gray-300' : 'text-blue-600 hover:bg-blue-50'}`}
                              style={{color: index === items.length - 1 ? undefined : '#3569b0'}}
                            >
                              <ArrowDown className="w-4 h-4" />
                            </button>
                            
                            {/* Botões de ação */}
                            {item.status === 'waiting' && (
                              <Button
                                size="sm"
                                onClick={() => notifyUser(item.id)}
                                className="ml-2"
                                style={{backgroundColor: '#3569b0'}}
                              >
                                Notificar
                              </Button>
                            )}
                            {item.status === 'notified' && (
                              <Button
                                size="sm"
                                onClick={() => confirmReservation(item.id)}
                                className="ml-2 bg-green-600 hover:bg-green-700"
                              >
                                Confirmar
                              </Button>
                            )}
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => removeFromList(item.id)}
                              className="ml-1 border-red-300 text-red-600 hover:bg-red-50"
                            >
                              Remover
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default WaitingList;
