import React from 'react';
import { Link } from 'react-router-dom';
import { Star, MapPin, Share2, ShoppingCart, Eye, List, Grid, Heart } from 'lucide-react';
import { Button } from '../ui/button';

interface Equipment {
  id: string;
  title: string;
  description: string;
  classCode: string;
  price: {
    daily: number;
    weekly: number;
    monthly: number;
    annual?: number;
  };
  image: string;
  category: string;
  subcategory: string;
  specifications: string[];
}

interface EquipmentListProps {
  isDarkMode: boolean;
  equipments: Equipment[];
  viewType: 'list' | 'grid' | 'card';
  setViewType: (type: 'list' | 'grid' | 'card') => void;
  sortOrder: 'relevance' | 'price_asc' | 'price_desc' | 'newest';
  setSortOrder: (order: 'relevance' | 'price_asc' | 'price_desc' | 'newest') => void;
  favorites: string[];
  toggleFavorite: (id: string) => void;
  setQuickViewEquipmentId: (id: string | null) => void;
  setSelectedEquipmentForMap: (equipment: Equipment | null) => void;
  setIsMapModalOpen: (isOpen: boolean) => void;
  periodFilter: 'daily' | 'weekly' | 'monthly' | 'annual' | null;
  formatPrice: (price: number) => string;
}

/**
 * Componente que exibe a lista de equipamentos
 * Suporta diferentes tipos de visualização (lista, grade, cartão)
 */
const EquipmentList: React.FC<EquipmentListProps> = ({
  isDarkMode,
  equipments,
  viewType,
  setViewType,
  sortOrder,
  setSortOrder,
  favorites,
  toggleFavorite,
  setQuickViewEquipmentId,
  setSelectedEquipmentForMap,
  setIsMapModalOpen,
  periodFilter,
  formatPrice
}) => {
  // Função para obter o preço com base no filtro de período
  const getPrice = (equipment: Equipment) => {
    if (periodFilter === 'weekly') return equipment.price.weekly;
    if (periodFilter === 'monthly') return equipment.price.monthly;
    if (periodFilter === 'annual') return equipment.price.annual || equipment.price.monthly * 12;
    return equipment.price.daily; // Padrão para diário
  };

  // Função para obter o texto do período
  const getPeriodText = () => {
    if (periodFilter === 'weekly') return '/semana';
    if (periodFilter === 'monthly') return '/mês';
    if (periodFilter === 'annual') return '/ano';
    return '/dia'; // Padrão para diário
  };

  return (
    <div className="flex-1">
      {/* Barra de controles (ordenação e visualização) */}
      <div className={`flex flex-col md:flex-row justify-between items-center mb-6 p-4 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
        <div className="flex items-center mb-4 md:mb-0">
          <span className="mr-3 text-sm">Ordenar por:</span>
          <select
            value={sortOrder}
            onChange={(e) => setSortOrder(e.target.value as any)}
            className={`p-2 rounded border ${isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'}`}
          >
            <option value="relevance">Relevância</option>
            <option value="price_asc">Preço (menor para maior)</option>
            <option value="price_desc">Preço (maior para menor)</option>
            <option value="newest">Mais recentes</option>
          </select>
        </div>

        <div className="flex items-center">
          <span className="mr-3 text-sm hidden md:inline">Visualização:</span>
          <div className="flex space-x-2">
            <Button
              variant={viewType === 'list' ? 'default' : 'outline'}
              size="icon"
              onClick={() => setViewType('list')}
              className="w-9 h-9"
            >
              <List className="w-5 h-5" />
            </Button>
            <Button
              variant={viewType === 'grid' ? 'default' : 'outline'}
              size="icon"
              onClick={() => setViewType('grid')}
              className="w-9 h-9"
            >
              <Grid className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </div>

      {/* Resultados (Desktop) */}
      <div className="hidden md:flex justify-between items-center mb-4">
        <span className="text-sm">Resultados: <strong>{equipments.length}</strong> equipamentos</span>
      </div>

      {/* Lista de equipamentos */}
      {viewType === 'list' ? (
        <div className="space-y-4">
          {equipments.map((equipment) => (
            <div
              key={equipment.id}
              className={`rounded-lg overflow-hidden shadow-md ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}
            >
              <div className="flex flex-col md:flex-row">
                {/* Imagem */}
                <div className="relative md:w-1/3">
                  <img
                    src={equipment.image}
                    alt={equipment.title}
                    className="w-full h-48 md:h-full object-cover"
                  />
                  <button
                    onClick={() => toggleFavorite(equipment.id)}
                    className="absolute top-2 right-2 bg-white rounded-full p-1.5 shadow-md"
                  >
                    <Heart
                      className={`w-5 h-5 ${favorites.includes(equipment.id) ? 'fill-red-500 text-red-500' : 'text-gray-400'}`}
                    />
                  </button>
                </div>

                {/* Conteúdo */}
                <div className="p-4 flex-1 flex flex-col">
                  <div className="mb-2">
                    <span className={`text-xs px-2 py-1 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                      {equipment.classCode}
                    </span>
                  </div>
                  <h3 className="text-lg font-semibold mb-2">{equipment.title}</h3>
                  <p className={`text-sm mb-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    {equipment.description}
                  </p>

                  {/* Especificações */}
                  <div className="mb-4 flex-grow">
                    <h4 className="text-sm font-medium mb-2">Especificações:</h4>
                    <ul className="text-sm grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-1">
                      {equipment.specifications.slice(0, 4).map((spec, index) => (
                        <li key={index} className="flex items-center">
                          <div className={`w-1.5 h-1.5 rounded-full mr-2 ${isDarkMode ? 'bg-blue-400' : 'bg-blue-500'}`}></div>
                          {spec}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Preço e botões */}
                  <div className="mt-auto">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                      <div className="mb-4 md:mb-0">
                        <div className="flex items-center mb-1">
                          <Star className="w-4 h-4 text-yellow-400 mr-1" />
                          <Star className="w-4 h-4 text-yellow-400 mr-1" />
                          <Star className="w-4 h-4 text-yellow-400 mr-1" />
                          <Star className="w-4 h-4 text-yellow-400 mr-1" />
                          <Star className="w-4 h-4 text-gray-300 mr-1" />
                          <span className="text-sm">(4.0)</span>
                        </div>
                        <div className="text-xl font-bold text-blue-600">
                          {formatPrice(getPrice(equipment))}
                          <span className="text-sm font-normal">{getPeriodText()}</span>
                        </div>
                      </div>

                      {/* Botões: alinhados em linha em desktop e grid em mobile */}
                      <div className="grid grid-cols-2 gap-2 md:flex md:space-x-2 md:items-center md:justify-end">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center justify-center md:w-24"
                          onClick={() => {
                            setSelectedEquipmentForMap(equipment);
                            setIsMapModalOpen(true);
                          }}
                        >
                          <MapPin className="w-4 h-4 mr-1" />
                          <span className="text-xs">Mapa</span>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center justify-center md:w-24"
                          onClick={() => setQuickViewEquipmentId(equipment.id)}
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          <span className="text-xs">Ver</span>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center justify-center md:w-24"
                        >
                          <Share2 className="w-4 h-4 mr-1" />
                          <span className="text-xs">Compartilhar</span>
                        </Button>
                        <Button
                          variant="default"
                          size="sm"
                          className="flex items-center justify-center bg-blue-600 hover:bg-blue-700 md:w-24"
                        >
                          <ShoppingCart className="w-4 h-4 mr-1" />
                          <span className="text-xs">Alugar</span>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {equipments.map((equipment) => (
            <div
              key={equipment.id}
              className={`rounded-lg overflow-hidden shadow-md ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}
            >
              {/* Imagem */}
              <div className="relative">
                <img
                  src={equipment.image}
                  alt={equipment.title}
                  className="w-full h-48 object-cover"
                />
                <button
                  onClick={() => toggleFavorite(equipment.id)}
                  className="absolute top-2 right-2 bg-white rounded-full p-1.5 shadow-md"
                >
                  <Heart
                    className={`w-5 h-5 ${favorites.includes(equipment.id) ? 'fill-red-500 text-red-500' : 'text-gray-400'}`}
                  />
                </button>
              </div>

              {/* Conteúdo */}
              <div className="p-4">
                <div className="mb-2">
                  <span className={`text-xs px-2 py-1 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                    {equipment.classCode}
                  </span>
                </div>
                <h3 className="text-lg font-semibold mb-2">{equipment.title}</h3>
                <p className={`text-sm mb-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  {equipment.description}
                </p>

                {/* Preço e avaliação */}
                <div className="mb-4">
                  <div className="flex items-center mb-1">
                    <Star className="w-4 h-4 text-yellow-400 mr-1" />
                    <Star className="w-4 h-4 text-yellow-400 mr-1" />
                    <Star className="w-4 h-4 text-yellow-400 mr-1" />
                    <Star className="w-4 h-4 text-yellow-400 mr-1" />
                    <Star className="w-4 h-4 text-gray-300 mr-1" />
                    <span className="text-sm">(4.0)</span>
                  </div>
                  <div className="text-xl font-bold text-blue-600">
                    {formatPrice(getPrice(equipment))}
                    <span className="text-sm font-normal">{getPeriodText()}</span>
                  </div>
                </div>

                {/* Botões */}
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center justify-center w-full"
                    onClick={() => setQuickViewEquipmentId(equipment.id)}
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    <span className="text-xs">Ver Detalhes</span>
                  </Button>
                  <Button
                    variant="default"
                    size="sm"
                    className="flex items-center justify-center bg-blue-600 hover:bg-blue-700 w-full"
                  >
                    <ShoppingCart className="w-4 h-4 mr-1" />
                    <span className="text-xs">Alugar</span>
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Sem resultados */}
      {equipments.length === 0 && (
        <div className={`p-8 text-center rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
          <h3 className="text-xl font-semibold mb-2">Nenhum equipamento encontrado</h3>
          <p className={`mb-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Tente ajustar seus filtros ou termos de pesquisa.
          </p>
          <Button
            variant="default"
            onClick={() => {
              // Limpar todos os filtros
              window.location.href = '/equipment';
            }}
          >
            Limpar Filtros
          </Button>
        </div>
      )}
    </div>
  );
};

export default EquipmentList;
