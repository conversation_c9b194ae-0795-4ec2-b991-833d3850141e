import React from 'react';
import Slideshow from '../components/Slideshow';
import SearchForm from '../components/SearchForm';
import EquipmentMap from '../components/EquipmentMap';

// Componentes segmentados da página Home
import Stats from '../components/home/<USER>';
import Categories from '../components/home/<USER>';
import Benefits from '../components/home/<USER>';
import FeaturedEquipment from '../components/home/<USER>';
import TrustSignals from '../components/home/<USER>';

// Dados mockados
import { categoria as categoriasMock, equipamentos as equipamentosMock } from '../../public/data/ymrentalMockData';

interface HomeProps {
  isDarkMode: boolean;
}

const Home: React.FC<HomeProps> = ({ isDarkMode }) => {
  // Transformar categorias do mock para o formato esperado pelo componente
  const categories = categoriasMock.slice(0, 6).map(cat => ({
    id: cat.nome.toLowerCase().replace(/\s+/g, '-'),
    name: cat.nome,
    image: cat.imagem || 'https://via.placeholder.com/600',
    description: cat.descricao || cat.escricao || ''
  }));

  // Transformar equipamentos do mock para o formato esperado pelo componente
  const featuredEquipment = equipamentosMock
    .filter(equip => equip.isAvailable) // Apenas equipamentos disponíveis
    .slice(0, 3) // Limitar a 3 equipamentos
    .map((equip, index) => ({
      id: (index + 1).toString(),
      title: equip.name,
      description: equip.description,
      price: `A partir de ${equip.price.toLocaleString('pt-AO')} Kz/${equip.pricePeriod === 'DAILY' ? 'dia' : 'semana'}`,
      image: equip.images[0] || 'https://via.placeholder.com/600',
      location: equip.location || 'Luanda',
      distance: ['2.5 km', '5.1 km', '8.3 km'][index] || '3.2 km'
    }));

  return (
    <>
      {/* Slideshow */}
      <Slideshow
        slides={[
          {
            image: "https://images.unsplash.com/photo-1581578731548-c64695cc6952?q=80&w=1000",
            title: "Equipamentos de Qualidade para Seu Projeto",
            description: "Acesse nossa ampla seleção de equipamentos para construção, mineração e agricultura",
            buttonText: "Ver Equipamentos",
            buttonLink: "/equipment"
          },
          {
            image: "https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=1200",
            title: "Soluções Completas para Empresas",
            description: "Oferecemos equipamentos de alta performance com suporte técnico especializado",
            buttonText: "Saiba Mais",
            buttonLink: "/about"
          },
          {
            image: "https://images.unsplash.com/photo-1581094794329-c8112a89af12?q=80&w=2070",
            title: "Alugue com Facilidade e Segurança",
            description: "Processo simplificado, equipamentos certificados e entrega em todo o país",
            buttonText: "Cadastre-se",
            buttonLink: "/auth"
          }
        ]}
        isDarkMode={isDarkMode}
      />

      {/* Search Form */}
      <div className="container mx-auto px-4 -mt-12 relative z-20 animate-slideUp animation-delay-500">
        <SearchForm isDarkMode={isDarkMode} />
      </div>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-12">
        {/* Stats Section */}
        <Stats isDarkMode={isDarkMode} />

        {/* Categories Section */}
        <Categories isDarkMode={isDarkMode} categories={categories} />

        {/* Benefits Section */}
        <Benefits isDarkMode={isDarkMode} />

        {/* Featured Equipment Section */}
        <FeaturedEquipment isDarkMode={isDarkMode} equipment={featuredEquipment} />

        {/* Map Section */}
        <section className="mb-16">
          <h2 className={`text-2xl font-bold mb-8 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>Pesquisar no Mapa</h2>
          <EquipmentMap isDarkMode={isDarkMode} />
        </section>

        {/* Trust Signals Section */}
        <TrustSignals isDarkMode={isDarkMode} />
      </main>
    </>
  );
};

export default Home;
