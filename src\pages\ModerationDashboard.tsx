import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  AlertTriangle,
  BarChart3,
  Users,
  Package,
  RefreshCw
} from 'lucide-react';
import { Button } from '../components/ui/button';
import BackButton from '../components/ui/BackButton';
import { useModeration } from '../contexts/ModerationContext';
import { useAuth } from '../contexts/AuthContext';

interface ModerationDashboardProps {
  isDarkMode: boolean;
}

const ModerationDashboard: React.FC<ModerationDashboardProps> = ({ isDarkMode }) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    pendingEquipment,
    stats,
    loading,
    error,
    fetchPendingEquipment,
    fetchStats,
    approveEquipment,
    rejectEquipment
  } = useModeration();

  const [selectedEquipment, setSelectedEquipment] = useState<string | null>(null);
  const [rejectReason, setRejectReason] = useState('');
  const [showRejectModal, setShowRejectModal] = useState(false);

  // Verificar se usuário é moderador
  const isModerator = user?.role && ['MODERATOR', 'MANAGER', 'ADMIN'].includes(user.role);

  useEffect(() => {
    if (!isModerator) {
      navigate('/');
      return;
    }
  }, [isModerator, navigate]);

  const handleApprove = async (equipmentId: string) => {
    try {
      await approveEquipment(equipmentId);
    } catch (error) {
      console.error('Erro ao aprovar equipamento:', error);
    }
  };

  const handleReject = async () => {
    if (!selectedEquipment || !rejectReason.trim()) return;

    try {
      await rejectEquipment(selectedEquipment, rejectReason);
      setShowRejectModal(false);
      setRejectReason('');
      setSelectedEquipment(null);
    } catch (error) {
      console.error('Erro ao rejeitar equipamento:', error);
    }
  };

  const openRejectModal = (equipmentId: string) => {
    setSelectedEquipment(equipmentId);
    setShowRejectModal(true);
  };

  if (!isModerator) {
    return null;
  }

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <div className="flex items-center space-x-4 mb-4 sm:mb-0">
            <BackButton isDarkMode={isDarkMode} />
            <div>
              <h1 className="text-3xl font-bold">Painel de Moderação</h1>
              <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} mt-1`}>
                Gerir equipamentos e validações
              </p>
            </div>
          </div>

          <Button
            variant="outline"
            onClick={() => {
              fetchPendingEquipment();
              fetchStats();
            }}
            disabled={loading}
            className="flex items-center space-x-2"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Atualizar</span>
          </Button>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            <p>{error}</p>
          </div>
        )}

        {!loading && !error && (
          <>
            {/* Estatísticas */}
            {stats && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        Pendentes
                      </p>
                      <p className="text-2xl font-bold text-orange-600">{stats.pending}</p>
                    </div>
                    <Clock className="w-8 h-8 text-orange-600" />
                  </div>
                </div>

                <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        Aprovados
                      </p>
                      <p className="text-2xl font-bold text-green-600">{stats.approved}</p>
                    </div>
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  </div>
                </div>

                <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        Rejeitados
                      </p>
                      <p className="text-2xl font-bold text-red-600">{stats.rejected}</p>
                    </div>
                    <XCircle className="w-8 h-8 text-red-600" />
                  </div>
                </div>

                <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        Minhas Ações
                      </p>
                      <p className="text-2xl font-bold text-blue-600">{stats.myTotal}</p>
                    </div>
                    <BarChart3 className="w-8 h-8 text-blue-600" />
                  </div>
                </div>
              </div>
            )}

            {/* Lista de Equipamentos Pendentes */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-sm border`}>
              <div className="p-6 border-b">
                <h2 className="text-xl font-semibold">Equipamentos Pendentes de Aprovação</h2>
                <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} mt-1`}>
                  {pendingEquipment.length} equipamentos aguardando moderação
                </p>
              </div>

              <div className="divide-y">
                {pendingEquipment.length === 0 ? (
                  <div className="p-8 text-center">
                    <Package className={`w-12 h-12 mx-auto mb-4 ${isDarkMode ? 'text-gray-600' : 'text-gray-400'}`} />
                    <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Nenhum equipamento pendente de moderação
                    </p>
                  </div>
                ) : (
                  pendingEquipment.map((equipment) => (
                    <div key={equipment.id} className="p-6">
                      <div className="flex flex-col lg:flex-row lg:items-center justify-between space-y-4 lg:space-y-0">
                        <div className="flex items-start space-x-4">
                          <img
                            src={equipment.images[0] || '/api/placeholder/80/80'}
                            alt={equipment.name}
                            className="w-20 h-20 object-cover rounded-lg"
                          />
                          <div className="flex-1">
                            <h3 className="font-semibold text-lg">{equipment.name}</h3>
                            <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} text-sm mt-1`}>
                              {equipment.description.substring(0, 100)}...
                            </p>
                            <div className="flex items-center space-x-4 mt-2">
                              <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                {equipment.category}
                              </span>
                              <span className="text-sm font-medium">
                                {equipment.price.toLocaleString()} Kz/{equipment.pricePeriod || 'dia'}
                              </span>
                            </div>
                            <p className={`text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-500'} mt-1`}>
                              Por: {equipment.owner.fullName} ({equipment.owner.email})
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigate(`/equipment/${equipment.id}`)}
                          >
                            <Eye className="w-4 h-4 mr-2" />
                            Ver Detalhes
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleApprove(equipment.id)}
                            className="text-green-600 border-green-600 hover:bg-green-50"
                          >
                            <CheckCircle className="w-4 h-4 mr-2" />
                            Aprovar
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openRejectModal(equipment.id)}
                            className="text-red-600 border-red-600 hover:bg-red-50"
                          >
                            <XCircle className="w-4 h-4 mr-2" />
                            Rejeitar
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </>
        )}

        {/* Modal de Rejeição */}
        {showRejectModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg max-w-md w-full mx-4`}>
              <h3 className="text-lg font-semibold mb-4">Rejeitar Equipamento</h3>
              <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
                Por favor, forneça um motivo para a rejeição:
              </p>
              <textarea
                value={rejectReason}
                onChange={(e) => setRejectReason(e.target.value)}
                placeholder="Motivo da rejeição..."
                className={`w-full p-3 border rounded-md ${
                  isDarkMode 
                    ? 'bg-gray-700 border-gray-600 text-white' 
                    : 'bg-white border-gray-300'
                }`}
                rows={4}
              />
              <div className="flex justify-end space-x-3 mt-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowRejectModal(false);
                    setRejectReason('');
                    setSelectedEquipment(null);
                  }}
                >
                  Cancelar
                </Button>
                <Button
                  onClick={handleReject}
                  disabled={!rejectReason.trim()}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  Rejeitar
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ModerationDashboard;
