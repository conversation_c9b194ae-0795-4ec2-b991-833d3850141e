import React from 'react';
import HierarchicalNotifications from '../components/HierarchicalNotifications';

interface NotificationsProps {
  isDarkMode: boolean;
}

const Notifications: React.FC<NotificationsProps> = ({ isDarkMode }) => {
  return (
    <div className={`min-h-screen pt-20 ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className={`text-3xl font-bold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
            Central de Notificações
          </h1>
          <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Gerencie suas notificações baseadas no seu nível de acesso
          </p>
        </div>

        <HierarchicalNotifications isDarkMode={isDarkMode} />
      </div>
    </div>
  );
};

export default Notifications;