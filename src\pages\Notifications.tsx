import React from 'react';
import { Bell, Package, Calendar, Star, AlertCircle } from 'lucide-react';

interface NotificationsProps {
  isDarkMode?: boolean;
}

export default function Notifications({ isDarkMode = false }: NotificationsProps) {
  const notifications = [
    {
      id: 1,
      type: 'rental',
      icon: Package,
      title: '<PERSON><PERSON><PERSON>',
      message: '<PERSON><PERSON> aluguel da Retroescavadeira CAT foi confirmado.',
      time: '2 horas atrás',
      read: false
    },
    {
      id: 2,
      type: 'schedule',
      icon: Calendar,
      title: 'Lembrete de Devolução',
      message: 'A devolução do equipamento está agendada para amanhã.',
      time: '5 horas atrás',
      read: false
    },
    {
      id: 3,
      type: 'review',
      icon: Star,
      title: 'Avalie seu Aluguel',
      message: 'Como foi sua experiência com a Retroescavadeira CAT?',
      time: '1 dia atrás',
      read: true
    },
    {
      id: 4,
      type: 'alert',
      icon: AlertCircle,
      title: 'Manutenção Programada',
      message: 'O sistema estará em manutenção no próximo domingo.',
      time: '2 dias atrás',
      read: true
    }
  ];

  return (
    <div className={`container mx-auto px-4 py-8 pt-24 ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="max-w-2xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Notificações</h1>
          <Bell className={`w-6 h-6 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
        </div>

        <div className="space-y-4">
          {notifications.map((notification) => {
            const Icon = notification.icon;
            return (
              <div
                key={notification.id}
                className={`${
                  notification.read
                    ? isDarkMode ? 'bg-gray-800' : 'bg-white'
                    : isDarkMode ? 'bg-cyan-900' : 'bg-cyan-50'
                } rounded-lg shadow p-4 transition-all hover:shadow-md`}
              >
                <div className="flex items-start space-x-4">
                  <div className={`p-2 ${isDarkMode ? 'bg-cyan-800' : 'bg-cyan-100'} rounded-full`}>
                    <Icon className="w-6 h-6 text-cyan-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-start justify-between">
                      <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{notification.title}</h3>
                      <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>{notification.time}</span>
                    </div>
                    <p className={`mt-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>{notification.message}</p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}