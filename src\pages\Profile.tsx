import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Button } from '../components/ui/button';
import BackButton from '../components/ui/BackButton';
import {
  Camera, Mail, Phone, MapPin, Calendar, Package, Star,
  Briefcase, CreditCard, Check, Shield, Settings, Globe,
  Facebook, Instagram, Twitter, Linkedin, Edit, Clock, Heart,
  MessageSquare, User, ChevronRight, ArrowLeft, Wallet, DollarSign,
  ArrowUpCircle, ArrowDownCircle, Eye, EyeOff, Building2, Smartphone
} from 'lucide-react';

interface ProfileProps {
  isDarkMode?: boolean;
}

export default function Profile({ isDarkMode = false }: ProfileProps) {
  const navigate = useNavigate();
  const { user: authUser, sendEmailVerification } = useAuth();
  const [isVerifyingEmail, setIsVerifyingEmail] = useState(false);
  const [verificationMessage, setVerificationMessage] = useState('');

  // Dados do usuário com fallbacks para dados mockados
  const user = authUser ? {
    name: authUser.fullName,
    email: authUser.email,
    phone: authUser.phoneNumber,
    location: "Luanda, Angola", // TODO: Implementar endereço real
    joinDate: new Date(authUser.createdAt).toLocaleDateString('pt-AO', {
      year: 'numeric',
      month: 'long'
    }),
    avatar: authUser.profilePicture || "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
    rentals: 12, // TODO: Implementar contagem real
    rating: 4.8, // TODO: Implementar rating real
    bio: "Profissional de engenharia civil com experiência em projetos de construção. Apaixonado por tecnologia e equipamentos de qualidade.", // TODO: Implementar bio real
    occupation: "Engenheiro Civil", // TODO: Implementar ocupação real
    company: authUser.isCompany ? "Empresa" : "Individual",
    verified: true,
    verifiedEmail: authUser.isEmailVerified,
    verifiedPhone: authUser.isPhoneVerified,
    verifiedID: !!authUser.nif,
    preferredLanguage: "Português",
    preferredCurrency: "AOA",
    socialMedia: {
      facebook: "",
      instagram: "",
      twitter: "",
      linkedin: ""
    },
    userType: authUser.userType,
    isCompany: authUser.isCompany,
    nif: authUser.nif,
    paymentMethods: [
      { type: "Cartão de Crédito", last4: "4242", brand: "Visa", isDefault: true },
      { type: "PayPal", email: authUser.email, isDefault: false }
    ],
    recentActivity: [
      { type: "rental", item: "Betoneira 400L", date: "15 Maio, 2024", status: "Em andamento" },
      { type: "message", from: "Carlos Mendes", date: "12 Maio, 2024", preview: "Olá, gostaria de saber se..." },
      { type: "favorite", item: "Gerador 5000W", date: "10 Maio, 2024" }
    ]
  } : null;

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">Carregando perfil...</h2>
        </div>
      </div>
    );
  }

  const [activeTab, setActiveTab] = useState('profile');

  // Estado para controlar a exibição de abas em dispositivos móveis
  const [isMobile, setIsMobile] = useState(false);

  // Estados para carteira digital
  const [showBalance, setShowBalance] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [showDepositModal, setShowDepositModal] = useState(false);
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [depositAmount, setDepositAmount] = useState('');
  const [selectedWithdrawMethod, setSelectedWithdrawMethod] = useState('bank');
  const [selectedDepositMethod, setSelectedDepositMethod] = useState('bank');

  // Dados mockados da carteira
  const userBalance = 2450000; // 2.450.000 Kz
  const pendingWithdrawals = 500000; // 500.000 Kz

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('pt-AO') + ' Kz';
  };

  const handleWithdraw = () => {
    console.log('Saque:', withdrawAmount, selectedWithdrawMethod);
    setShowWithdrawModal(false);
    setWithdrawAmount('');
  };

  const handleDeposit = () => {
    console.log('Depósito:', depositAmount, selectedDepositMethod);
    setShowDepositModal(false);
    setDepositAmount('');
  };

  // Detectar se é um dispositivo móvel
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    handleResize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="container mx-auto px-4 py-8 pt-24">
        <div className="max-w-5xl mx-auto">
          {/* Botão de Voltar */}
          <div className="mb-6">
            <BackButton isDarkMode={isDarkMode} />
          </div>

          {/* Verification Message */}
          {verificationMessage && (
            <div className={`mb-6 px-4 py-3 rounded-md ${
              verificationMessage.includes('Erro')
                ? 'bg-red-50 border border-red-200 text-red-700'
                : 'bg-green-50 border border-green-200 text-green-700'
            }`}>
              <div className="flex items-center">
                {verificationMessage.includes('Erro') ? (
                  <AlertCircle className="h-4 w-4 mr-2" />
                ) : (
                  <Check className="h-4 w-4 mr-2" />
                )}
                <span className="text-sm">{verificationMessage}</span>
              </div>
            </div>
          )}
        {/* Profile Header */}
        <div className="relative mb-8">
          <div className="h-32 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-t-lg"></div>
          <div className="absolute -bottom-16 left-8">
            <div className="relative">
              <img
                src={user.avatar}
                alt={user.name}
                className="w-24 h-24 md:w-32 md:h-32 rounded-full border-4 border-white"
              />
              <button className="absolute bottom-0 right-0 p-2 bg-white rounded-full shadow-lg">
                <Camera className="w-4 h-4 md:w-5 md:h-5 text-gray-600" />
              </button>
            </div>
          </div>
        </div>

        {/* Profile Info */}
        <div className="mt-20">
          <div className="flex flex-col md:flex-row justify-between items-start mb-6">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold mb-2">{user.name}</h1>
              <div className={`flex flex-wrap items-center gap-2 md:gap-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                <div className="flex items-center">
                  <Package className="w-4 h-4 mr-1" />
                  <span>{user.rentals} aluguéis</span>
                </div>
                <div className="flex items-center">
                  <Star className="w-4 h-4 mr-1 text-yellow-400" />
                  <span>{user.rating}</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  <span>Membro desde {user.joinDate}</span>
                </div>
                {user.verified && (
                  <div className="flex items-center text-green-600">
                    <Shield className="w-4 h-4 mr-1" />
                    <span>Verificado</span>
                  </div>
                )}
              </div>
              {user.occupation && (
                <div className="flex items-center mt-2 text-gray-600">
                  <Briefcase className="w-4 h-4 mr-1" />
                  <span>{user.occupation} {user.company ? `em ${user.company}` : ''}</span>
                </div>
              )}
            </div>
            <div className="flex space-x-2 mt-4 md:mt-0">
              <Button
                onClick={() => navigate('/profile/edit')}
                className="flex items-center"
              >
                <Edit className="w-4 h-4 mr-1" />
                Editar Perfil
              </Button>
              <Button
                onClick={() => navigate('/settings')}
                variant="outline"
                className="flex items-center"
              >
                <Settings className="w-4 h-4 mr-1" />
                Configurações
              </Button>
            </div>
          </div>

          {/* Profile Tabs */}
          <div className={`border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'} mb-6 overflow-x-auto`}>
            <nav className={`-mb-px flex ${isMobile ? 'space-x-4' : 'space-x-8'}`}>
              <button
                onClick={() => setActiveTab('profile')}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${activeTab === 'profile' ? 'border-blue-500 text-blue-600' : `border-transparent ${isDarkMode ? 'text-gray-400 hover:text-gray-300 hover:border-gray-600' : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}`}
                style={activeTab === 'profile' ? {borderColor: '#3569b0', color: '#3569b0'} : {}}
              >
                Perfil
              </button>
              <button
                onClick={() => setActiveTab('account')}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${activeTab === 'account' ? 'border-blue-500 text-blue-600' : `border-transparent ${isDarkMode ? 'text-gray-400 hover:text-gray-300 hover:border-gray-600' : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}`}
                style={activeTab === 'account' ? {borderColor: '#3569b0', color: '#3569b0'} : {}}
              >
                Minha Conta
              </button>
              <button
                onClick={() => setActiveTab('activity')}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${activeTab === 'activity' ? 'border-blue-500 text-blue-600' : `border-transparent ${isDarkMode ? 'text-gray-400 hover:text-gray-300 hover:border-gray-600' : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}`}
                style={activeTab === 'activity' ? {borderColor: '#3569b0', color: '#3569b0'} : {}}
              >
                Atividade Recente
              </button>
              <button
                onClick={() => setActiveTab('rentals')}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${activeTab === 'rentals' ? 'border-blue-500 text-blue-600' : `border-transparent ${isDarkMode ? 'text-gray-400 hover:text-gray-300 hover:border-gray-600' : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}`}
                style={activeTab === 'rentals' ? {borderColor: '#3569b0', color: '#3569b0'} : {}}
              >
                Meus Aluguéis
              </button>
              <button
                onClick={() => setActiveTab('payments')}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${activeTab === 'payments' ? 'border-blue-500 text-blue-600' : `border-transparent ${isDarkMode ? 'text-gray-400 hover:text-gray-300 hover:border-gray-600' : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}`}
                style={activeTab === 'payments' ? {borderColor: '#3569b0', color: '#3569b0'} : {}}
              >
                Pagamentos
              </button>
            </nav>
          </div>

          {activeTab === 'profile' && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2 space-y-6">
                {/* Bio Section */}
                <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                  <h2 className="text-xl font-semibold mb-4">Sobre</h2>
                  <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>{user.bio}</p>
                </div>

                {/* Contact Information */}
                <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                  <h2 className="text-xl font-semibold mb-4">Informações de Contato</h2>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Mail className="w-5 h-5 text-gray-400 mr-3" />
                        <span>{user.email}</span>
                      </div>
                      {user.verifiedEmail && (
                        <span className="text-green-600 flex items-center">
                          <Check className="w-4 h-4 mr-1" /> Verificado
                        </span>
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Phone className="w-5 h-5 text-gray-400 mr-3" />
                        <span>{user.phone}</span>
                      </div>
                      {user.verifiedPhone && (
                        <span className="text-green-600 flex items-center">
                          <Check className="w-4 h-4 mr-1" /> Verificado
                        </span>
                      )}
                    </div>
                    <div className="flex items-center">
                      <MapPin className="w-5 h-5 text-gray-400 mr-3" />
                      <span>{user.location}</span>
                    </div>
                  </div>
                </div>

                {/* Minha Conta */}
                <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold">Carteira Digital</h2>
                    <Button
                      onClick={() => navigate('/wallet')}
                      className="flex items-center text-white"
                      style={{backgroundColor: '#3569b0'}}
                    >
                      <Wallet className="w-4 h-4 mr-1" />
                      Minha Conta
                    </Button>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center">
                        <DollarSign className="w-5 h-5 mr-3" style={{color: '#3569b0'}} />
                        <div>
                          <p className="font-medium">Saldo Disponível</p>
                          <p className="text-sm text-gray-500">
                            {showBalance ? formatCurrency(userBalance) : '••••••••'}
                          </p>
                        </div>
                      </div>
                      <button
                        onClick={() => setShowBalance(!showBalance)}
                        className={`p-2 rounded-lg ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
                      >
                        {showBalance ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center">
                        <ArrowUpCircle className="w-5 h-5 text-yellow-500 mr-3" />
                        <div>
                          <p className="font-medium">Saques Pendentes</p>
                          <p className="text-sm text-gray-500">
                            {showBalance ? formatCurrency(pendingWithdrawals) : '••••••••'}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                {/* Verification Status */}
                <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                  <h2 className="text-xl font-semibold mb-4">Verificação</h2>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span>Email</span>
                      {user.verifiedEmail ? (
                        <span className="text-green-600 flex items-center">
                          <Check className="w-4 h-4 mr-1" /> Verificado
                        </span>
                      ) : (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={async () => {
                            setIsVerifyingEmail(true);
                            try {
                              await sendEmailVerification();
                              setVerificationMessage('Email de verificação enviado! Verifique sua caixa de entrada.');
                              setTimeout(() => setVerificationMessage(''), 5000);
                            } catch (error: any) {
                              setVerificationMessage(`Erro: ${error.message}`);
                              setTimeout(() => setVerificationMessage(''), 5000);
                            } finally {
                              setIsVerifyingEmail(false);
                            }
                          }}
                          disabled={isVerifyingEmail}
                        >
                          {isVerifyingEmail ? 'Enviando...' : 'Verificar'}
                        </Button>
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Telefone</span>
                      {user.verifiedPhone ? (
                        <span className="text-green-600 flex items-center">
                          <Check className="w-4 h-4 mr-1" /> Verificado
                        </span>
                      ) : (
                        <Button size="sm" variant="outline">Verificar</Button>
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Documento de Identidade</span>
                      {user.verifiedID ? (
                        <span className="text-green-600 flex items-center">
                          <Check className="w-4 h-4 mr-1" /> Verificado
                        </span>
                      ) : (
                        <Button size="sm" variant="outline">Verificar</Button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Preferences */}
                <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                  <h2 className="text-xl font-semibold mb-4">Preferências</h2>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Globe className="w-5 h-5 text-gray-400 mr-3" />
                        <span>Idioma</span>
                      </div>
                      <span>{user.preferredLanguage}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <CreditCard className="w-5 h-5 text-gray-400 mr-3" />
                        <span>Moeda</span>
                      </div>
                      <span>{user.preferredCurrency}</span>
                    </div>
                  </div>
                </div>

                {/* Social Media */}
                <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                  <h2 className="text-xl font-semibold mb-4">Redes Sociais</h2>
                  <div className="space-y-3">
                    {user.socialMedia.facebook && (
                      <div className="flex items-center">
                        <Facebook className="w-5 h-5 text-blue-600 mr-3" />
                        <span>@{user.socialMedia.facebook}</span>
                      </div>
                    )}
                    {user.socialMedia.instagram && (
                      <div className="flex items-center">
                        <Instagram className="w-5 h-5 text-pink-600 mr-3" />
                        <span>@{user.socialMedia.instagram}</span>
                      </div>
                    )}
                    {user.socialMedia.twitter && (
                      <div className="flex items-center">
                        <Twitter className="w-5 h-5 text-blue-400 mr-3" />
                        <span>@{user.socialMedia.twitter}</span>
                      </div>
                    )}
                    {user.socialMedia.linkedin && (
                      <div className="flex items-center">
                        <Linkedin className="w-5 h-5 text-blue-700 mr-3" />
                        <span>@{user.socialMedia.linkedin}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'account' && (
            <div className="space-y-6">
              {/* Carteira Digital */}
              <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-6`}>
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-full" style={{backgroundColor: '#3569b0'}}>
                      <Wallet className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h2 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        Carteira Digital
                      </h2>
                      <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        Gerencie seu saldo e transações
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowBalance(!showBalance)}
                    className={`p-2 rounded-lg ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
                  >
                    {showBalance ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>

                {/* Saldo */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Saldo Disponível</p>
                        <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {showBalance ? formatCurrency(userBalance) : '••••••••'}
                        </p>
                      </div>
                      <DollarSign className="w-8 h-8" style={{color: '#3569b0'}} />
                    </div>
                  </div>

                  <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Saques Pendentes</p>
                        <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {showBalance ? formatCurrency(pendingWithdrawals) : '••••••••'}
                        </p>
                      </div>
                      <ArrowUpCircle className="w-8 h-8 text-yellow-500" />
                    </div>
                  </div>
                </div>

                {/* Botões de Ação */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button
                    onClick={() => setShowDepositModal(true)}
                    className="flex items-center justify-center space-x-2 text-white"
                    style={{backgroundColor: '#3569b0'}}
                  >
                    <ArrowDownCircle className="w-5 h-5" />
                    <span>Depositar Dinheiro</span>
                  </Button>

                  <Button
                    onClick={() => setShowWithdrawModal(true)}
                    variant="outline"
                    className="flex items-center justify-center space-x-2"
                    style={{borderColor: '#3569b0', color: '#3569b0'}}
                  >
                    <ArrowUpCircle className="w-5 h-5" />
                    <span>Retirar Dinheiro</span>
                  </Button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'activity' && (
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
              <h2 className="text-xl font-semibold mb-4">Atividade Recente</h2>
              <div className="space-y-4">
                {user.recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-start p-3 border-b last:border-0">
                    <div className="rounded-full p-2 mr-3 bg-gray-100">
                      {activity.type === 'rental' && <Package className="w-5 h-5 text-blue-500" />}
                      {activity.type === 'message' && <MessageSquare className="w-5 h-5 text-green-500" />}
                      {activity.type === 'favorite' && <Heart className="w-5 h-5 text-red-500" />}
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between">
                        <p className="font-medium">
                          {activity.type === 'rental' && `Alugou ${activity.item}`}
                          {activity.type === 'message' && `Mensagem de ${activity.from}`}
                          {activity.type === 'favorite' && `Adicionou ${activity.item} aos favoritos`}
                        </p>
                        <span className="text-sm text-gray-500 flex items-center">
                          <Clock className="w-4 h-4 mr-1" />
                          {activity.date}
                        </span>
                      </div>
                      {activity.type === 'message' && (
                        <p className="text-sm text-gray-600 mt-1">{activity.preview}</p>
                      )}
                      {activity.type === 'rental' && activity.status && (
                        <p className="text-sm text-blue-600 mt-1">Status: {activity.status}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'rentals' && (
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
              <h2 className="text-xl font-semibold mb-4">Meus Aluguéis</h2>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <img
                      src="https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"
                      alt="Betoneira"
                      className="w-16 h-16 object-cover rounded"
                    />
                    <div>
                      <h3 className="font-medium">Betoneira 400L</h3>
                      <p className="text-sm text-gray-500">15 Maio - 22 Maio, 2024</p>
                      <span className="text-sm bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">Em andamento</span>
                    </div>
                  </div>
                  <Button variant="outline" size="sm" className="flex items-center">
                    <ChevronRight className="w-4 h-4" />
                    Detalhes
                  </Button>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <img
                      src="https://images.unsplash.com/photo-1572383672419-ab35444a6934?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"
                      alt="Furadeira"
                      className="w-16 h-16 object-cover rounded"
                    />
                    <div>
                      <h3 className="font-medium">Furadeira Industrial</h3>
                      <p className="text-sm text-gray-500">10 Abril - 15 Abril, 2024</p>
                      <span className="text-sm bg-green-100 text-green-800 px-2 py-0.5 rounded-full">Concluído</span>
                    </div>
                  </div>
                  <Button variant="outline" size="sm" className="flex items-center">
                    <ChevronRight className="w-4 h-4" />
                    Detalhes
                  </Button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'payments' && (
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
              <h2 className="text-xl font-semibold mb-4">Histórico de Pagamentos</h2>
              <div className="overflow-x-auto">
                <table className={`min-w-full divide-y ${isDarkMode ? 'divide-gray-700' : 'divide-gray-200'}`}>
                  <thead>
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Descrição</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Método</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valor</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    </tr>
                  </thead>
                  <tbody className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} divide-y ${isDarkMode ? 'divide-gray-700' : 'divide-gray-200'}`}>
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">15 Maio, 2024</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">Aluguel de Betoneira 400L</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Visa ****4242</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">25.000 AOA</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          Pago
                        </span>
                      </td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">10 Abril, 2024</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">Aluguel de Furadeira Industrial</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">PayPal</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">12.500 AOA</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          Pago
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
        </div>
      </div>

      {/* Modal de Depósito */}
      {showDepositModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 w-full max-w-md`}>
            <h3 className={`text-xl font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Depositar Dinheiro
            </h3>

            <div className="space-y-4">
              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Valor
                </label>
                <input
                  type="number"
                  value={depositAmount}
                  onChange={(e) => setDepositAmount(e.target.value)}
                  placeholder="0"
                  className={`w-full px-3 py-2 border rounded-lg ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Método de Pagamento
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      value="bank"
                      checked={selectedDepositMethod === 'bank'}
                      onChange={(e) => setSelectedDepositMethod(e.target.value)}
                      className="text-blue-600"
                    />
                    <Building2 className="w-5 h-5" />
                    <span>Transferência Bancária</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      value="mobile"
                      checked={selectedDepositMethod === 'mobile'}
                      onChange={(e) => setSelectedDepositMethod(e.target.value)}
                      className="text-blue-600"
                    />
                    <Smartphone className="w-5 h-5" />
                    <span>Pagamento Móvel</span>
                  </label>
                </div>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <Button
                onClick={() => setShowDepositModal(false)}
                variant="outline"
                className="flex-1"
              >
                Cancelar
              </Button>
              <Button
                onClick={handleDeposit}
                className="flex-1 text-white"
                style={{backgroundColor: '#3569b0'}}
                disabled={!depositAmount}
              >
                Depositar
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Saque */}
      {showWithdrawModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 w-full max-w-md`}>
            <h3 className={`text-xl font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Retirar Dinheiro
            </h3>

            <div className="space-y-4">
              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Valor
                </label>
                <input
                  type="number"
                  value={withdrawAmount}
                  onChange={(e) => setWithdrawAmount(e.target.value)}
                  placeholder="0"
                  max={userBalance}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
                <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Saldo disponível: {formatCurrency(userBalance)}
                </p>
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Método de Saque
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      value="bank"
                      checked={selectedWithdrawMethod === 'bank'}
                      onChange={(e) => setSelectedWithdrawMethod(e.target.value)}
                      className="text-blue-600"
                    />
                    <Building2 className="w-5 h-5" />
                    <span>Conta Bancária</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      value="mobile"
                      checked={selectedWithdrawMethod === 'mobile'}
                      onChange={(e) => setSelectedWithdrawMethod(e.target.value)}
                      className="text-blue-600"
                    />
                    <Smartphone className="w-5 h-5" />
                    <span>Carteira Móvel</span>
                  </label>
                </div>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <Button
                onClick={() => setShowWithdrawModal(false)}
                variant="outline"
                className="flex-1"
              >
                Cancelar
              </Button>
              <Button
                onClick={handleWithdraw}
                className="flex-1 text-white"
                style={{backgroundColor: '#3569b0'}}
                disabled={!withdrawAmount || Number(withdrawAmount) > userBalance}
              >
                Retirar
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}