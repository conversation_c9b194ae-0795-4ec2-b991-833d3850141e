import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Button } from '../components/ui/button';
import BackButton from '../components/ui/BackButton';
import {
  FileText, Check, X, AlertCircle, User, Mail, Phone, Calendar,
  Eye, Download
} from 'lucide-react';

interface PendingUser {
  id: string;
  email: string;
  fullName: string;
  phoneNumber: string;
  biDocument: string;
  createdAt: string;
}

interface ModeratorBiValidationProps {
  isDarkMode?: boolean;
}

export default function ModeratorBiValidation({ isDarkMode = false }: ModeratorBiValidationProps) {
  const { user } = useAuth();
  const [pendingUsers, setPendingUsers] = useState<PendingUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<PendingUser | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [validationAction, setValidationAction] = useState<'approve' | 'reject' | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    fetchPendingUsers();
  }, []);

  const fetchPendingUsers = async () => {
    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:3000'}/users/pending-bi-validation`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPendingUsers(data);
      } else {
        setError('Erro ao carregar usuários pendentes');
      }
    } catch (error) {
      setError('Erro ao conectar com o servidor');
    } finally {
      setIsLoading(false);
    }
  };

  const handleValidation = async () => {
    if (!selectedUser || !validationAction) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:3000'}/users/${selectedUser.id}/validate-bi`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
        },
        body: JSON.stringify({
          approved: validationAction === 'approve',
          reason: validationAction === 'reject' ? rejectionReason : undefined,
        }),
      });

      if (response.ok) {
        setSuccessMessage(`BI ${validationAction === 'approve' ? 'aprovado' : 'rejeitado'} com sucesso!`);
        setPendingUsers(prev => prev.filter(u => u.id !== selectedUser.id));
        setShowModal(false);
        setSelectedUser(null);
        setValidationAction(null);
        setRejectionReason('');
        setTimeout(() => setSuccessMessage(''), 3000);
      } else {
        setError('Erro ao processar validação');
      }
    } catch (error) {
      setError('Erro ao conectar com o servidor');
    } finally {
      setIsSubmitting(false);
    }
  };

  const openValidationModal = (user: PendingUser, action: 'approve' | 'reject') => {
    setSelectedUser(user);
    setValidationAction(action);
    setShowModal(true);
    setRejectionReason('');
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedUser(null);
    setValidationAction(null);
    setRejectionReason('');
  };

  if (!user || !['ADMIN', 'MANAGER', 'MODERATOR'].includes(user.role)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">Acesso Negado</h2>
          <p>Você não tem permissão para acessar esta página.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="container mx-auto px-4 py-8 pt-24">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-6">
            <BackButton isDarkMode={isDarkMode} />
          </div>

          <div className="mb-8">
            <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Validação de Documentos de BI
            </h1>
            <p className={`mt-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Revise e valide os documentos de identidade enviados pelos usuários
            </p>
          </div>

          {/* Success Message */}
          {successMessage && (
            <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md">
              <div className="flex items-center">
                <Check className="h-4 w-4 mr-2" />
                <span className="text-sm">{successMessage}</span>
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
              <div className="flex items-center">
                <AlertCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">{error}</span>
              </div>
            </div>
          )}

          {/* Loading State */}
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-500 mx-auto"></div>
              <p className="mt-4">Carregando usuários pendentes...</p>
            </div>
          ) : pendingUsers.length === 0 ? (
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-8 text-center`}>
              <FileText className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold mb-2">Nenhum documento pendente</h3>
              <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Não há documentos de BI aguardando validação no momento.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {pendingUsers.map((pendingUser) => (
                <div key={pendingUser.id} className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-cyan-500 rounded-full flex items-center justify-center">
                      <User className="w-6 h-6 text-white" />
                    </div>
                    <div className="ml-3">
                      <h3 className="font-semibold">{pendingUser.fullName}</h3>
                      <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {new Date(pendingUser.createdAt).toLocaleDateString('pt-AO')}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2 mb-4">
                    <div className="flex items-center text-sm">
                      <Mail className="w-4 h-4 mr-2 text-gray-400" />
                      <span>{pendingUser.email}</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Phone className="w-4 h-4 mr-2 text-gray-400" />
                      <span>{pendingUser.phoneNumber}</span>
                    </div>
                  </div>

                  <div className="mb-4">
                    <a
                      href={pendingUser.biDocument}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center w-full p-2 border border-cyan-500 text-cyan-500 rounded-md hover:bg-cyan-50 transition-colors"
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Ver Documento
                    </a>
                  </div>

                  <div className="flex space-x-2">
                    <Button
                      onClick={() => openValidationModal(pendingUser, 'approve')}
                      className="flex-1 bg-green-500 hover:bg-green-600 text-white"
                      size="sm"
                    >
                      <Check className="w-4 h-4 mr-1" />
                      Aprovar
                    </Button>
                    <Button
                      onClick={() => openValidationModal(pendingUser, 'reject')}
                      variant="outline"
                      className="flex-1 border-red-500 text-red-500 hover:bg-red-50"
                      size="sm"
                    >
                      <X className="w-4 h-4 mr-1" />
                      Rejeitar
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Validation Modal */}
          {showModal && selectedUser && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
              <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-xl max-w-md w-full p-6`}>
                <div className="flex items-center justify-between mb-4">
                  <h2 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {validationAction === 'approve' ? 'Aprovar' : 'Rejeitar'} Documento
                  </h2>
                  <button
                    onClick={closeModal}
                    className={`${isDarkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'}`}
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>

                <div className="mb-4">
                  <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Você está prestes a {validationAction === 'approve' ? 'aprovar' : 'rejeitar'} o documento de BI de:
                  </p>
                  <p className="font-semibold mt-2">{selectedUser.fullName}</p>
                  <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {selectedUser.email}
                  </p>
                </div>

                {validationAction === 'reject' && (
                  <div className="mb-4">
                    <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Motivo da Rejeição *
                    </label>
                    <textarea
                      value={rejectionReason}
                      onChange={(e) => setRejectionReason(e.target.value)}
                      className={`w-full px-3 py-2 border rounded-md ${
                        isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                      } focus:outline-none focus:ring-2 focus:ring-cyan-500`}
                      rows={3}
                      placeholder="Explique o motivo da rejeição..."
                    />
                  </div>
                )}

                <div className="flex space-x-3">
                  <Button
                    onClick={closeModal}
                    variant="outline"
                    className="flex-1"
                  >
                    Cancelar
                  </Button>
                  <Button
                    onClick={handleValidation}
                    disabled={isSubmitting || (validationAction === 'reject' && !rejectionReason.trim())}
                    className={`flex-1 ${
                      validationAction === 'approve' 
                        ? 'bg-green-500 hover:bg-green-600' 
                        : 'bg-red-500 hover:bg-red-600'
                    } text-white`}
                  >
                    {isSubmitting ? 'Processando...' : (validationAction === 'approve' ? 'Aprovar' : 'Rejeitar')}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
