import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Calendar, Clock, User, Tag, Heart, Share2, MessageCircle,
  Bookmark, Eye, ArrowLeft, ThumbsUp, ThumbsDown
} from 'lucide-react';
import { Button } from '../components/ui/button';
import BackButton from '../components/ui/BackButton';

interface BlogPostProps {
  isDarkMode: boolean;
}

interface BlogPost {
  id: string;
  title: string;
  content: string;
  author: string;
  authorAvatar: string;
  authorBio: string;
  publishDate: string;
  readTime: string;
  category: string;
  tags: string[];
  image: string;
  views: number;
  likes: number;
  comments: number;
}

interface Comment {
  id: string;
  author: string;
  authorAvatar: string;
  content: string;
  date: string;
  likes: number;
}

const BlogPost: React.FC<BlogPostProps> = ({ isDarkMode }) => {
  const { postId } = useParams();
  const navigate = useNavigate();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [isLiked, setIsLiked] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data - em produção viria de uma API
    const mockPost: BlogPost = {
      id: postId || '1',
      title: 'O Futuro dos Equipamentos de Construção em Angola',
      content: `
        <p>A indústria de equipamentos de construção em Angola está passando por uma transformação significativa, impulsionada por avanços tecnológicos e mudanças nas demandas do mercado.</p>
        
        <h2>Tendências Emergentes</h2>
        <p>Nos últimos anos, temos observado várias tendências que estão moldando o futuro do setor:</p>
        
        <h3>1. Digitalização e IoT</h3>
        <p>A integração de sensores IoT (Internet das Coisas) nos equipamentos está revolucionando a forma como monitoramos e gerenciamos frotas. Esses dispositivos permitem:</p>
        <ul>
          <li>Monitoramento em tempo real da localização e status dos equipamentos</li>
          <li>Manutenção preditiva baseada em dados</li>
          <li>Otimização do consumo de combustível</li>
          <li>Redução de custos operacionais</li>
        </ul>
        
        <h3>2. Sustentabilidade</h3>
        <p>A crescente preocupação com o meio ambiente está impulsionando o desenvolvimento de equipamentos mais sustentáveis:</p>
        <ul>
          <li>Motores elétricos e híbridos</li>
          <li>Redução de emissões de CO2</li>
          <li>Uso de materiais recicláveis</li>
          <li>Eficiência energética aprimorada</li>
        </ul>
        
        <h3>3. Automação e IA</h3>
        <p>A inteligência artificial está sendo integrada aos equipamentos para:</p>
        <ul>
          <li>Operação autônoma em certas condições</li>
          <li>Análise preditiva de falhas</li>
          <li>Otimização de rotas e operações</li>
          <li>Segurança aprimorada no canteiro de obras</li>
        </ul>
        
        <h2>Impacto no Mercado Angolano</h2>
        <p>Essas tendências têm um impacto direto no mercado angolano de equipamentos de construção:</p>
        
        <h3>Oportunidades</h3>
        <ul>
          <li>Maior eficiência operacional</li>
          <li>Redução de custos a longo prazo</li>
          <li>Melhoria na qualidade dos projetos</li>
          <li>Criação de novos empregos especializados</li>
        </ul>
        
        <h3>Desafios</h3>
        <ul>
          <li>Necessidade de capacitação técnica</li>
          <li>Investimento inicial elevado</li>
          <li>Infraestrutura de conectividade</li>
          <li>Adaptação às novas tecnologias</li>
        </ul>
        
        <h2>Conclusão</h2>
        <p>O futuro dos equipamentos de construção em Angola é promissor, com tecnologias emergentes oferecendo oportunidades significativas para melhorar a eficiência, sustentabilidade e segurança no setor. As empresas que se adaptarem rapidamente a essas mudanças estarão melhor posicionadas para prosperar no mercado em evolução.</p>
        
        <p>A YMRentals está comprometida em acompanhar essas tendências, oferecendo equipamentos modernos e tecnologicamente avançados para atender às necessidades em constante evolução de nossos clientes.</p>
      `,
      author: 'Carlos Mendes',
      authorAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=facearea&facepad=2&w=64&h=64&q=80',
      authorBio: 'Especialista em equipamentos de construção com mais de 15 anos de experiência no mercado angolano. Consultor técnico da YMRentals.',
      publishDate: '2024-01-20',
      readTime: '5 min',
      category: 'Indústria',
      tags: ['construção', 'tecnologia', 'angola', 'futuro'],
      image: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?auto=format&fit=crop&w=1200&q=80',
      views: 1250,
      likes: 89,
      comments: 23
    };

    const mockComments: Comment[] = [
      {
        id: '1',
        author: 'Ana Silva',
        authorAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?auto=format&fit=facearea&facepad=2&w=64&h=64&q=80',
        content: 'Excelente artigo! Realmente vemos essas tendências se materializando no mercado. A digitalização está mudando tudo.',
        date: '2024-01-21',
        likes: 12
      },
      {
        id: '2',
        author: 'João Santos',
        authorAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=facearea&facepad=2&w=64&h=64&q=80',
        content: 'Muito interessante a parte sobre sustentabilidade. Quando teremos equipamentos totalmente elétricos disponíveis em Angola?',
        date: '2024-01-21',
        likes: 8
      },
      {
        id: '3',
        author: 'Maria Costa',
        authorAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=facearea&facepad=2&w=64&h=64&q=80',
        content: 'A automação é realmente o futuro. Já estamos vendo alguns equipamentos semi-autônomos em grandes projetos.',
        date: '2024-01-22',
        likes: 5
      }
    ];

    setPost(mockPost);
    setComments(mockComments);
    setLoading(false);
  }, [postId]);

  const handleLike = () => {
    setIsLiked(!isLiked);
  };

  const handleBookmark = () => {
    setIsBookmarked(!isBookmarked);
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: post?.title,
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      alert('Link copiado para a área de transferência!');
    }
  };

  const handleCommentSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newComment.trim()) {
      const comment: Comment = {
        id: Date.now().toString(),
        author: 'Você',
        authorAvatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?auto=format&fit=facearea&facepad=2&w=64&h=64&q=80',
        content: newComment,
        date: new Date().toISOString().split('T')[0],
        likes: 0
      };
      setComments([...comments, comment]);
      setNewComment('');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2" style={{borderColor: '#3569b0'}}></div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Post não encontrado</h2>
          <Button onClick={() => navigate('/blog')}>Voltar ao Blog</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen max-w-full overflow-x-hidden">
      <div className={`container mx-auto px-4 py-8 pt-24 max-w-4xl ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
        <div className="flex items-center space-x-4 mb-8">
          <BackButton isDarkMode={isDarkMode} />
          <Button
            variant="ghost"
            onClick={() => navigate('/blog')}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Voltar ao Blog</span>
          </Button>
        </div>

        {/* Header do Post */}
        <article className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg overflow-hidden mb-8`}>
          <div className="relative">
            <img
              src={post.image}
              alt={post.title}
              className="w-full h-64 md:h-96 object-cover"
            />
            <div className="absolute inset-0 bg-black bg-opacity-40 flex items-end">
              <div className="p-6 text-white">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="px-3 py-1 bg-blue-600 rounded-full text-sm font-medium">
                    {post.category}
                  </span>
                  <div className="flex items-center space-x-4 text-sm">
                    <div className="flex items-center space-x-1">
                      <Eye className="w-4 h-4" />
                      <span>{post.views}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <MessageCircle className="w-4 h-4" />
                      <span>{post.comments}</span>
                    </div>
                  </div>
                </div>
                <h1 className="text-2xl md:text-4xl font-bold mb-4">{post.title}</h1>
                <div className="flex items-center space-x-4">
                  <img
                    src={post.authorAvatar}
                    alt={post.author}
                    className="w-12 h-12 rounded-full"
                  />
                  <div>
                    <p className="font-medium">{post.author}</p>
                    <div className="flex items-center space-x-4 text-sm opacity-90">
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>{new Date(post.publishDate).toLocaleDateString('pt-AO')}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4" />
                        <span>{post.readTime}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Ações do Post */}
          <div className={`p-6 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleLike}
                  className={`flex items-center space-x-2 ${isLiked ? 'text-red-500' : ''}`}
                >
                  <Heart className={`w-4 h-4 ${isLiked ? 'fill-current' : ''}`} />
                  <span>{post.likes + (isLiked ? 1 : 0)}</span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBookmark}
                  className={`flex items-center space-x-2 ${isBookmarked ? 'text-blue-500' : ''}`}
                >
                  <Bookmark className={`w-4 h-4 ${isBookmarked ? 'fill-current' : ''}`} />
                  <span>Salvar</span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleShare}
                  className="flex items-center space-x-2"
                >
                  <Share2 className="w-4 h-4" />
                  <span>Compartilhar</span>
                </Button>
              </div>
              <div className="flex items-center space-x-2">
                {post.tags.map((tag) => (
                  <span
                    key={tag}
                    className={`px-2 py-1 rounded-full text-xs ${
                      isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'
                    }`}
                  >
                    #{tag}
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* Conteúdo do Post */}
          <div className="p-6">
            <div
              className={`prose max-w-none ${isDarkMode ? 'prose-invert' : ''}`}
              dangerouslySetInnerHTML={{ __html: post.content }}
            />
          </div>

          {/* Autor */}
          <div className={`p-6 border-t ${isDarkMode ? 'border-gray-700 bg-gray-700' : 'border-gray-200 bg-gray-50'}`}>
            <div className="flex items-start space-x-4">
              <img
                src={post.authorAvatar}
                alt={post.author}
                className="w-16 h-16 rounded-full"
              />
              <div>
                <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {post.author}
                </h3>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {post.authorBio}
                </p>
              </div>
            </div>
          </div>
        </article>

        {/* Seção de Comentários */}
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-6`}>
          <h2 className={`text-2xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Comentários ({comments.length})
          </h2>

          {/* Formulário de Comentário */}
          <form onSubmit={handleCommentSubmit} className="mb-8">
            <textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="Deixe seu comentário..."
              rows={4}
              className={`w-full p-3 border rounded-lg resize-none ${
                isDarkMode
                  ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                  : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
              }`}
            />
            <div className="flex justify-end mt-3">
              <Button
                type="submit"
                disabled={!newComment.trim()}
                className="text-white"
                style={{backgroundColor: '#3569b0'}}
              >
                Comentar
              </Button>
            </div>
          </form>

          {/* Lista de Comentários */}
          <div className="space-y-6">
            {comments.map((comment) => (
              <div key={comment.id} className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <div className="flex items-start space-x-3">
                  <img
                    src={comment.authorAvatar}
                    alt={comment.author}
                    className="w-10 h-10 rounded-full"
                  />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {comment.author}
                      </h4>
                      <span className="text-sm text-gray-500">
                        {new Date(comment.date).toLocaleDateString('pt-AO')}
                      </span>
                    </div>
                    <p className={`text-sm mb-3 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      {comment.content}
                    </p>
                    <div className="flex items-center space-x-4">
                      <Button variant="ghost" size="sm" className="flex items-center space-x-1 text-gray-500">
                        <ThumbsUp className="w-3 h-3" />
                        <span>{comment.likes}</span>
                      </Button>
                      <Button variant="ghost" size="sm" className="text-gray-500">
                        Responder
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogPost;
