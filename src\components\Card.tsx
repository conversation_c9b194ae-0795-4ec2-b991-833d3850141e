import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from './ui/button';
import { MapPin } from 'lucide-react';

interface CardProps {
  title: string;
  description?: string;
  image?: string;
  price?: string;
  location?: string;
  distance?: string;
  buttonText?: string;
  buttonLink?: string;
  secondaryButtonIcon?: React.ReactNode;
  secondaryButtonLink?: string;
  isDarkMode?: boolean;
  className?: string;
}

const Card: React.FC<CardProps> = ({
  title,
  description,
  image,
  price,
  location,
  distance,
  buttonText = 'Ver Detalhes',
  buttonLink = '#',
  secondaryButtonIcon,
  secondaryButtonLink,
  isDarkMode = false,
  className = '',
}) => {
  return (
    <div className={`${isDarkMode ? 'bg-neutral-800' : 'bg-white'} rounded-lg shadow-lg overflow-hidden ${className}`}>
      {image && (
        <div className="relative overflow-hidden">
          <img
            src={image}
            alt={title}
            className="w-full h-48 object-cover transition-transform duration-500 hover:scale-105"
          />
          {price && (
            <div
              className="absolute bottom-0 right-0 text-white px-3 py-1 rounded-tl-md font-medium"
              style={{backgroundColor: '#3569b0'}}
            >
              {price}
            </div>
          )}
        </div>
      )}
      <div className="p-6">
        <h3 className={`text-xl font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>{title}</h3>
        {description && (
          <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'} mb-4`}>{description}</p>
        )}
        {(location || distance) && (
          <div className="flex items-center justify-between mb-4">
            {location && (
              <div className="flex items-center">
                <MapPin className="w-4 h-4 text-gray-400 mr-1" />
                <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>{location}</span>
              </div>
            )}
            {distance && (
              <span className={`text-sm font-medium ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`} style={{color: '#3569b0'}}>
                {distance}
              </span>
            )}
          </div>
        )}
        <div className="flex gap-2">
          <Link to={buttonLink} className="flex-1">
            <Button
              className="w-full text-white"
              style={{backgroundColor: '#3569b0'}}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2a5490'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3569b0'}
            >
              {buttonText}
            </Button>
          </Link>
          {secondaryButtonIcon && secondaryButtonLink && (
            <Link to={secondaryButtonLink}>
              <Button variant="outline">
                {secondaryButtonIcon}
              </Button>
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};

export default Card;
