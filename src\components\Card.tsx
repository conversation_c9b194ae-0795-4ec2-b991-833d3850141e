import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from './ui/button';
import { MapPin, Eye, Heart, Share2 } from 'lucide-react';
import ProtectedAction from './ProtectedAction';

interface CardProps {
  title: string;
  description?: string;
  image?: string;
  price?: string;
  location?: string;
  distance?: string;
  buttonText?: string;
  buttonLink?: string;
  secondaryButtonIcon?: React.ReactNode;
  secondaryButtonLink?: string;
  isDarkMode?: boolean;
  className?: string;
  requireAuth?: boolean; // Se o botão principal requer autenticação
  secondaryRequireAuth?: boolean; // Se o botão secundário requer autenticação
  equipmentId?: string; // ID do equipamento para funcionalidades avançadas
  onAddToCart?: (equipmentId: string) => void; // Função para adicionar ao carrinho
  onToggleFavorite?: (equipmentId: string) => void; // Função para favoritar
  onShare?: (equipment: any) => void; // Função para compartilhar
  isFavorite?: boolean; // Se está favoritado
  isFavoriteLoading?: boolean; // Se está carregando favorito
  showQuickActions?: boolean; // Se deve mostrar ações rápidas
}

const Card: React.FC<CardProps> = ({
  title,
  description,
  image,
  price,
  location,
  distance,
  buttonText = 'Ver Detalhes',
  buttonLink = '#',
  secondaryButtonIcon,
  secondaryButtonLink,
  isDarkMode = false,
  className = '',
  requireAuth = false,
  secondaryRequireAuth = false,
  equipmentId,
  onAddToCart,
  onToggleFavorite,
  onShare,
  isFavorite = false,
  isFavoriteLoading = false,
  showQuickActions = false,
}) => {
  const navigate = useNavigate();

  // Função para lidar com adicionar ao carrinho
  const handleAddToCart = () => {
    if (onAddToCart && equipmentId) {
      onAddToCart(equipmentId);
    } else if (secondaryButtonLink) {
      navigate(secondaryButtonLink);
    }
  };

  // Função para lidar com favoritar
  const handleToggleFavorite = () => {
    if (onToggleFavorite && equipmentId) {
      onToggleFavorite(equipmentId);
    }
  };

  // Função para compartilhar
  const handleShare = () => {
    if (onShare) {
      onShare({ id: equipmentId, title, description, image, price });
    }
  };
  return (
    <div className={`${isDarkMode ? 'bg-neutral-800' : 'bg-white'} rounded-lg shadow-lg overflow-hidden ${className}`}>
      {image && (
        <div className="relative overflow-hidden group">
          <img
            src={image}
            alt={title}
            className="w-full h-48 object-cover transition-transform duration-500 hover:scale-105"
          />
          {price && (
            <div
              className="absolute bottom-0 right-0 text-white px-3 py-1 rounded-tl-md font-medium"
              style={{backgroundColor: '#3569b0'}}
            >
              {price}
            </div>
          )}

          {/* Ações rápidas - aparecem no hover */}
          {showQuickActions && (
            <div className="absolute top-2 right-2 flex flex-col gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              {onToggleFavorite && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleToggleFavorite}
                  disabled={isFavoriteLoading}
                  className={`w-8 h-8 p-0 bg-white/90 hover:bg-white ${isFavorite ? 'text-red-500' : 'text-gray-600'}`}
                >
                  {isFavoriteLoading ? (
                    <div className="w-4 h-4 border-2 border-gray-300 border-t-red-500 rounded-full animate-spin"></div>
                  ) : (
                    <Heart className={`w-4 h-4 ${isFavorite ? 'fill-current' : ''}`} />
                  )}
                </Button>
              )}

              {onShare && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleShare}
                  className="w-8 h-8 p-0 bg-white/90 hover:bg-white text-gray-600"
                >
                  <Share2 className="w-4 h-4" />
                </Button>
              )}

              <Button
                size="sm"
                variant="outline"
                onClick={() => navigate(buttonLink)}
                className="w-8 h-8 p-0 bg-white/90 hover:bg-white text-gray-600"
              >
                <Eye className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>
      )}
      <div className="p-6">
        <h3 className={`text-xl font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>{title}</h3>
        {description && (
          <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'} mb-4`}>{description}</p>
        )}
        {(location || distance) && (
          <div className="flex items-center justify-between mb-4">
            {location && (
              <div className="flex items-center">
                <MapPin className="w-4 h-4 text-gray-400 mr-1" />
                <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>{location}</span>
              </div>
            )}
            {distance && (
              <span className={`text-sm font-medium ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`} style={{color: '#3569b0'}}>
                {distance}
              </span>
            )}
          </div>
        )}
        <div className="flex gap-2">
          <ProtectedAction
            requireAuth={requireAuth}
            className="flex-1"
            fallback={
              <Link to={buttonLink} className="flex-1">
                <Button
                  className="w-full text-white"
                  style={{backgroundColor: '#3569b0'}}
                  onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2a5490'}
                  onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3569b0'}
                >
                  {buttonText}
                </Button>
              </Link>
            }
          >
            <Link to={buttonLink} className="flex-1">
              <Button
                className="w-full text-white"
                style={{backgroundColor: '#3569b0'}}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2a5490'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3569b0'}
              >
                {buttonText}
              </Button>
            </Link>
          </ProtectedAction>

          {(secondaryButtonIcon && secondaryButtonLink) || onAddToCart ? (
            <ProtectedAction
              requireAuth={secondaryRequireAuth}
              fallback={
                <Button
                  variant="outline"
                  onClick={onAddToCart ? handleAddToCart : undefined}
                >
                  {secondaryButtonIcon}
                </Button>
              }
            >
              <Button
                variant="outline"
                onClick={onAddToCart ? handleAddToCart : undefined}
              >
                {secondaryButtonIcon}
              </Button>
            </ProtectedAction>
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default Card;
