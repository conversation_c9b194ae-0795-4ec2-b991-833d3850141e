import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNotifications } from '../contexts/NotificationContext';
import { Bell, CheckCircle, AlertTriangle, Info, XCircle, Eye, Users, Building, Wrench } from 'lucide-react';

interface HierarchicalNotificationsProps {
  isDarkMode?: boolean;
}

const HierarchicalNotifications: React.FC<HierarchicalNotificationsProps> = ({ isDarkMode = false }) => {
  const { user } = useAuth();
  const { notifications, unreadCount, markAsRead, markAllAsRead } = useNotifications();
  const [filter, setFilter] = useState<'all' | 'pending_landlord' | 'pending_equipment' | 'return_reminder'>('all');

  // Filtrar notificações baseadas no role do usuário
  const getFilteredNotifications = () => {
    if (!user) return [];

    let filtered = notifications;

    // Filtrar por tipo se selecionado
    if (filter !== 'all') {
      filtered = notifications.filter(notif => {
        const data = notif.data ? JSON.parse(notif.data) : {};
        return data.type === filter;
      });
    }

    // Filtrar por relevância do role
    return filtered.filter(notif => {
      const data = notif.data ? JSON.parse(notif.data) : {};
      
      // Admin vê tudo
      if (user.role === 'ADMIN') return true;
      
      // Moderador Gerencial vê validações de landlord e equipamentos
      if (user.role === 'MODERATOR_MANAGER') {
        return ['pending_landlord', 'pending_equipment', 'return_reminder'].includes(data.type) || !data.type;
      }
      
      // Moderador Básico vê apenas equipamentos e lembretes
      if (user.role === 'MODERATOR') {
        return ['pending_equipment', 'return_reminder'].includes(data.type) || !data.type;
      }
      
      // Usuários normais veem suas notificações pessoais
      return !data.type || ['return_reminder', 'rental_confirmed'].includes(data.type);
    });
  };

  const getNotificationIcon = (type: string, notificationType: string) => {
    if (notificationType === 'SUCCESS') return <CheckCircle className="w-5 h-5 text-green-500" />;
    if (notificationType === 'WARNING') return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
    if (notificationType === 'ERROR') return <XCircle className="w-5 h-5 text-red-500" />;
    
    // Ícones específicos por tipo de dados
    if (type === 'pending_landlord') return <Building className="w-5 h-5 text-blue-500" />;
    if (type === 'pending_equipment') return <Wrench className="w-5 h-5 text-orange-500" />;
    if (type === 'return_reminder') return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
    
    return <Info className="w-5 h-5 text-blue-500" />;
  };

  const getFilterOptions = () => {
    const options = [{ value: 'all', label: 'Todas' }];
    
    if (user?.role === 'ADMIN' || user?.role === 'MODERATOR_MANAGER') {
      options.push({ value: 'pending_landlord', label: 'Locadores Pendentes' });
    }
    
    if (user?.role === 'ADMIN' || user?.role === 'MODERATOR_MANAGER' || user?.role === 'MODERATOR') {
      options.push({ value: 'pending_equipment', label: 'Equipamentos Pendentes' });
      options.push({ value: 'return_reminder', label: 'Lembretes de Devolução' });
    }
    
    return options;
  };

  const filteredNotifications = getFilteredNotifications();

  return (
    <div className={`${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'} rounded-lg shadow-md`}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Bell className="w-6 h-6" />
            <h2 className="text-xl font-semibold">Notificações</h2>
            {unreadCount > 0 && (
              <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                {unreadCount}
              </span>
            )}
          </div>
          
          {unreadCount > 0 && (
            <button
              onClick={markAllAsRead}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              Marcar todas como lidas
            </button>
          )}
        </div>

        {/* Filtros */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-2">
            {getFilterOptions().map(option => (
              <button
                key={option.value}
                onClick={() => setFilter(option.value as any)}
                className={`px-3 py-1 rounded-full text-sm ${
                  filter === option.value
                    ? 'bg-blue-600 text-white'
                    : isDarkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>

        {/* Lista de notificações */}
        <div className="space-y-3">
          {filteredNotifications.length === 0 ? (
            <div className="text-center py-8">
              <Bell className="w-12 h-12 mx-auto mb-3 text-gray-400" />
              <p className="text-gray-500">Nenhuma notificação encontrada</p>
            </div>
          ) : (
            filteredNotifications.map((notification) => {
              const data = notification.data ? JSON.parse(notification.data) : {};
              
              return (
                <div
                  key={notification.id}
                  className={`p-4 rounded-lg border ${
                    notification.isRead
                      ? isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
                      : isDarkMode ? 'bg-gray-600 border-gray-500' : 'bg-blue-50 border-blue-200'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    {getNotificationIcon(data.type, notification.type)}
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className={`font-medium ${notification.isRead ? 'text-gray-600' : 'text-gray-900'}`}>
                          {notification.title}
                        </h4>
                        <span className="text-xs text-gray-500">
                          {new Date(notification.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                      
                      <p className={`mt-1 text-sm ${notification.isRead ? 'text-gray-500' : 'text-gray-700'}`}>
                        {notification.message}
                      </p>
                      
                      {/* Ações específicas por tipo */}
                      {data.type === 'pending_landlord' && (user?.role === 'ADMIN' || user?.role === 'MODERATOR_MANAGER') && (
                        <div className="mt-3 flex space-x-2">
                          <button
                            onClick={() => window.location.href = '/moderation/landlord-validation'}
                            className="flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
                          >
                            <Eye className="w-3 h-3" />
                            <span>Validar</span>
                          </button>
                        </div>
                      )}
                      
                      {data.type === 'pending_equipment' && (user?.role === 'ADMIN' || user?.role === 'MODERATOR_MANAGER' || user?.role === 'MODERATOR') && (
                        <div className="mt-3 flex space-x-2">
                          <button
                            onClick={() => window.location.href = '/moderation'}
                            className="flex items-center space-x-1 px-3 py-1 bg-orange-600 text-white text-xs rounded hover:bg-orange-700"
                          >
                            <Eye className="w-3 h-3" />
                            <span>Moderar</span>
                          </button>
                        </div>
                      )}
                    </div>
                    
                    {!notification.isRead && (
                      <button
                        onClick={() => markAsRead(notification.id)}
                        className="text-blue-600 hover:text-blue-800 text-xs"
                      >
                        Marcar como lida
                      </button>
                    )}
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>
    </div>
  );
};

export default HierarchicalNotifications;
