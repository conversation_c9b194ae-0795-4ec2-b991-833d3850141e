import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

// Componente para redirecionar automaticamente admins/moderadores
const AdminRedirectHandler: React.FC = () => {
  const { user, isLoading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isLoading && user) {
      // Se é Admin ou Moderador, redirecionar para dashboard
      if (['ADMIN', 'MODERATOR_MANAGER', 'MODERATOR'].includes(user.role || '')) {
        const currentPath = window.location.pathname;
        // Só redirecionar se não estiver já no dashboard admin
        if (!currentPath.startsWith('/admin-dashboard')) {
          console.log('Redirecionando usuário admin/moderador para dashboard:', user.role);
          navigate('/admin-dashboard', { replace: true });
        }
      }
    }
  }, [user, isLoading, navigate]);

  return null; // Este componente não renderiza nada
};

interface AdminOnlyRouteProps {
  children: React.ReactNode;
}

// Componente para proteger rotas administrativas
const AdminOnlyRoute: React.FC<AdminOnlyRouteProps> = ({ children }) => {
  const { user, isLoading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isLoading) {
      // Se não está logado ou não é admin/moderador, redirecionar para home
      if (!user || !['ADMIN', 'MODERATOR_MANAGER', 'MODERATOR'].includes(user.role || '')) {
        console.log('Usuário não autorizado para área admin, redirecionando para home');
        navigate('/', { replace: true });
      }
    }
  }, [user, isLoading, navigate]);

  // Se ainda está carregando
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Se não está logado ou não é admin/moderador
  if (!user || !['ADMIN', 'MODERATOR_MANAGER', 'MODERATOR'].includes(user.role || '')) {
    return null; // Redirecionamento já foi feito
  }

  // Se é admin/moderador, renderizar o conteúdo
  return <>{children}</>;
};

export { AdminRedirectHandler };
export default AdminOnlyRoute;
