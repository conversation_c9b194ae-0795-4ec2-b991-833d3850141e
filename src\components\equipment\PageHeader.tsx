import React from 'react';

interface Category {
  id: string;
  name: string;
  subcategories: {
    id: string;
    name: string;
  }[];
}

interface PageHeaderProps {
  isDarkMode: boolean;
  selectedCategory: string | null;
  selectedSubcategory: string | null;
  categories: Category[];
  filteredEquipmentsCount: number;
}

/**
 * Componente de cabeçalho da página de catálogo de equipamentos
 * Exibe o título da categoria/subcategoria selecionada e uma descrição
 */
const PageHeader: React.FC<PageHeaderProps> = ({
  isDarkMode,
  selectedCategory,
  selectedSubcategory,
  categories,
  filteredEquipmentsCount
}) => {
  // Determinar o título com base na categoria/subcategoria selecionada
  const title = selectedSubcategory
    ? categories.find(c => c.id === selectedCategory)?.subcategories.find(s => s.id === selectedSubcategory)?.name
    : selectedCategory
      ? categories.find(c => c.id === selectedCategory)?.name
      : 'Todos os Equipamentos';

  // Determinar a descrição com base na categoria/subcategoria selecionada
  const description = selectedCategory === 'power' && selectedSubcategory === 'lighting'
    ? 'A YMRentals oferece uma seleção completa de torres de iluminação portáteis para aluguel, perfeitas para sua próxima construção ou projeto de infraestrutura.'
    : 'Encontre os melhores equipamentos para seu projeto. Oferecemos uma ampla seleção de equipamentos de alta qualidade para aluguel.';

  return (
    <>
      {/* Título principal */}
      <div className="mb-6 text-center">
        <h1 className="text-3xl font-bold mb-2">
          {title}
        </h1>
        <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'} text-lg mb-4 max-w-3xl mx-auto hidden md:block`}>
          {description}
        </p>
      </div>

      {/* Resultados mobile */}
      <div className="flex md:hidden justify-center items-center mb-6">
        <span className="text-sm">Resultados: <strong>{filteredEquipmentsCount}</strong> equipamentos</span>
      </div>
    </>
  );
};

export default PageHeader;
