import React from 'react';
import { Star, MapPin, Trash2, MessageSquare } from 'lucide-react';
import { Button } from '../components/ui/button';

interface FavoritesProps {
  isDarkMode: boolean;
}

const Favorites: React.FC<FavoritesProps> = ({ isDarkMode }) => {
  const favorites = [
    {
      id: 1,
      name: 'Retroescavadeira CAT 420F2',
      image: 'https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=600',
      price: 250000,
      location: 'Luanda, Talatona',
      rating: 4.2,
      reviews: 48,
      owner: {
        name: '<PERSON>',
        avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
      }
    },
    {
      id: 2,
      name: '<PERSON><PERSON><PERSON>hadeira Toyota 8FD',
      image: 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?auto=format&fit=crop&w=600',
      price: 180000,
      location: 'Luanda, Viana',
      rating: 4.5,
      reviews: 32,
      owner: {
        name: '<PERSON>',
        avatar: 'https://randomuser.me/api/portraits/women/44.jpg'
      }
    },
    {
      id: 3,
      name: '<PERSON>uindaste Liebherr LTM',
      image: 'https://images.unsplash.com/photo-1541625602330-2277a4c46182?auto=format&fit=crop&w=600',
      price: 450000,
      location: 'Luanda, Cacuaco',
      rating: 4.8,
      reviews: 15,
      owner: {
        name: 'Pedro Santos',
        avatar: 'https://randomuser.me/api/portraits/men/67.jpg'
      }
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8 pt-24">
      <h1 className={`text-2xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Meus Favoritos</h1>

      {favorites.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {favorites.map((item) => (
            <div key={item.id} className={`rounded-lg shadow-lg overflow-hidden ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
              <div className="relative">
                <img
                  src={item.image}
                  alt={item.name}
                  className="w-full h-48 object-cover"
                />
                <div
                  className="absolute bottom-2 right-2 px-2 py-1 rounded text-sm font-semibold text-white"
                  style={{backgroundColor: '#3569b0'}}
                >
                  {item.price.toLocaleString('pt-AO')} Kz/dia
                </div>
                <button className={`absolute top-2 right-2 p-1.5 rounded-full shadow-md ${
                  isDarkMode ? 'bg-gray-700 hover:bg-red-900' : 'bg-white hover:bg-red-50'
                }`}>
                  <Trash2 className="w-5 h-5 text-red-500" />
                </button>
              </div>
              <div className="p-6">
                <h3 className={`text-xl font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{item.name}</h3>
                <div className="flex items-center space-x-2 mb-4">
                  <MapPin className="w-4 h-4 text-gray-400" />
                  <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>{item.location}</span>
                </div>

                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <img
                      src={item.owner.avatar}
                      alt={item.owner.name}
                      className="w-8 h-8 rounded-full"
                    />
                    <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`}>{item.owner.name}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      {item.rating} ({item.reviews})
                    </span>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Button
                    className="flex-1 text-white"
                    style={{backgroundColor: '#3569b0'}}
                    onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2a5490'}
                    onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3569b0'}
                  >
                    Alugar
                  </Button>
                  <Button variant="outline" className="flex items-center">
                    <MessageSquare className="w-4 h-4 mr-1" />
                    Mensagem
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className={`rounded-lg shadow-lg p-8 text-center ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <div className="text-gray-400 mb-4">
            <Star className="w-16 h-16 mx-auto" />
          </div>
          <h2 className={`text-xl font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Nenhum favorito ainda</h2>
          <p className={`mb-6 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Você ainda não adicionou nenhum equipamento aos seus favoritos.
          </p>
          <Button
            className="text-white"
            style={{backgroundColor: '#3569b0'}}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2a5490'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3569b0'}
          >
            Explorar Equipamentos
          </Button>
        </div>
      )}
    </div>
  );
};

export default Favorites;
