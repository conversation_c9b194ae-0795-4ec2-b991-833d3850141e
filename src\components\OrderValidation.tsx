import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, XCircle, Clock, Calendar, MapPin } from 'lucide-react';
import { Button } from './ui/button';

interface Order {
  id: string;
  equipmentId: string;
  equipmentName: string;
  userId: string;
  userName: string;
  startDate: string;
  endDate: string;
  location: string;
  totalAmount: number;
  status: 'pending' | 'validated' | 'conflict' | 'rejected';
  conflictReason?: string;
}

interface OrderValidationProps {
  isDarkMode: boolean;
}

const OrderValidation: React.FC<OrderValidationProps> = ({ isDarkMode }) => {
  const [orders, setOrders] = useState<Order[]>([
    {
      id: '1',
      equipmentId: 'eq1',
      equipmentName: 'Escavadeira CAT 320',
      userId: 'user1',
      userName: '<PERSON>',
      startDate: '2024-01-20',
      endDate: '2024-01-25',
      location: 'Luanda, Kilamba',
      totalAmount: 1250000,
      status: 'pending'
    },
    {
      id: '2',
      equipmentId: 'eq1',
      equipmentName: 'Escavadeira CAT 320',
      userId: 'user2',
      userName: '<PERSON>',
      startDate: '2024-01-23',
      endDate: '2024-01-28',
      location: 'Luanda, Talatona',
      totalAmount: 1500000,
      status: 'conflict',
      conflictReason: 'Conflito de datas com pedido #1'
    },
    {
      id: '3',
      equipmentId: 'eq2',
      equipmentName: 'Gerador 100kVA',
      userId: 'user3',
      userName: 'Pedro Costa',
      startDate: '2024-01-22',
      endDate: '2024-01-24',
      location: 'Luanda, Viana',
      totalAmount: 360000,
      status: 'pending'
    }
  ]);

  // Função para validar pedidos e detectar conflitos
  const validateOrder = (orderId: string) => {
    const order = orders.find(o => o.id === orderId);
    if (!order) return;

    // Verificar conflitos de data para o mesmo equipamento
    const conflicts = orders.filter(o => 
      o.id !== orderId && 
      o.equipmentId === order.equipmentId &&
      o.status !== 'rejected' &&
      (
        (new Date(order.startDate) >= new Date(o.startDate) && new Date(order.startDate) <= new Date(o.endDate)) ||
        (new Date(order.endDate) >= new Date(o.startDate) && new Date(order.endDate) <= new Date(o.endDate)) ||
        (new Date(order.startDate) <= new Date(o.startDate) && new Date(order.endDate) >= new Date(o.endDate))
      )
    );

    setOrders(prev => prev.map(o => {
      if (o.id === orderId) {
        return {
          ...o,
          status: conflicts.length > 0 ? 'conflict' : 'validated',
          conflictReason: conflicts.length > 0 ? `Conflito com pedido(s): ${conflicts.map(c => `#${c.id}`).join(', ')}` : undefined
        };
      }
      return o;
    }));
  };

  // Função para aprovar pedido
  const approveOrder = (orderId: string) => {
    setOrders(prev => prev.map(o => 
      o.id === orderId ? { ...o, status: 'validated' } : o
    ));
  };

  // Função para rejeitar pedido
  const rejectOrder = (orderId: string) => {
    setOrders(prev => prev.map(o => 
      o.id === orderId ? { ...o, status: 'rejected' } : o
    ));
  };

  // Função para formatar preço
  const formatPrice = (price: number): string => {
    return price.toLocaleString('pt-AO') + ' Kz';
  };

  // Função para obter cor do status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'validated': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'conflict': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'rejected': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  // Função para obter ícone do status
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'validated': return <CheckCircle className="w-4 h-4" />;
      case 'conflict': return <AlertTriangle className="w-4 h-4" />;
      case 'rejected': return <XCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  // Função para obter texto do status
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'Pendente';
      case 'validated': return 'Validado';
      case 'conflict': return 'Conflito';
      case 'rejected': return 'Rejeitado';
      default: return 'Desconhecido';
    }
  };

  return (
    <div className={`${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'} min-h-screen p-6`}>
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Validação de Pedidos</h1>
          <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Sistema de prevenção de conflitos e validação automática de pedidos
          </p>
        </div>

        {/* Estatísticas */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total de Pedidos</p>
                <p className="text-2xl font-bold">{orders.length}</p>
              </div>
              <Calendar className="w-8 h-8 text-blue-500" style={{color: '#3569b0'}} />
            </div>
          </div>
          
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Pendentes</p>
                <p className="text-2xl font-bold">{orders.filter(o => o.status === 'pending').length}</p>
              </div>
              <Clock className="w-8 h-8 text-yellow-500" />
            </div>
          </div>
          
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Conflitos</p>
                <p className="text-2xl font-bold">{orders.filter(o => o.status === 'conflict').length}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-500" />
            </div>
          </div>
          
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Validados</p>
                <p className="text-2xl font-bold">{orders.filter(o => o.status === 'validated').length}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </div>
        </div>

        {/* Lista de pedidos */}
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md overflow-hidden`}>
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold">Pedidos de Aluguel</h3>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pedido
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Cliente
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Equipamento
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Período
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Localização
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Valor
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ações
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {orders.map((order) => (
                  <tr key={order.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium">#{order.id}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm">{order.userName}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm">{order.equipmentName}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm">
                        {new Date(order.startDate).toLocaleDateString('pt-AO')} - {new Date(order.endDate).toLocaleDateString('pt-AO')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm">
                        <MapPin className="w-4 h-4 mr-1 text-gray-400" />
                        {order.location}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium">{formatPrice(order.totalAmount)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                        {getStatusIcon(order.status)}
                        <span className="ml-1">{getStatusText(order.status)}</span>
                      </div>
                      {order.conflictReason && (
                        <div className="text-xs text-red-500 mt-1">{order.conflictReason}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      {order.status === 'pending' && (
                        <>
                          <Button
                            size="sm"
                            onClick={() => validateOrder(order.id)}
                            className="bg-blue-600 hover:bg-blue-700"
                            style={{backgroundColor: '#3569b0'}}
                          >
                            Validar
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => approveOrder(order.id)}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            Aprovar
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => rejectOrder(order.id)}
                            className="border-red-300 text-red-600 hover:bg-red-50"
                          >
                            Rejeitar
                          </Button>
                        </>
                      )}
                      {order.status === 'conflict' && (
                        <>
                          <Button
                            size="sm"
                            onClick={() => approveOrder(order.id)}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            Forçar Aprovação
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => rejectOrder(order.id)}
                            className="border-red-300 text-red-600 hover:bg-red-50"
                          >
                            Rejeitar
                          </Button>
                        </>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderValidation;
