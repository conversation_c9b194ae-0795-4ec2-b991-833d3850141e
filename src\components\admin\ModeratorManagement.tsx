import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import apiService from '../../services/api';
import { 
  Shield, 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  UserPlus,
  Calendar,
  Mail,
  Phone
} from 'lucide-react';

interface Moderator {
  id: string;
  email: string;
  fullName: string;
  phoneNumber: string;
  role: string;
  accountStatus: string;
  createdBy: string;
  createdAt: string;
}

interface ModeratorManagementProps {
  isDarkMode?: boolean;
}

const ModeratorManagement: React.FC<ModeratorManagementProps> = ({ isDarkMode = false }) => {
  const { user: currentUser } = useAuth();
  const [moderators, setModerators] = useState<Moderator[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [newModerator, setNewModerator] = useState({
    email: '',
    password: '',
    fullName: '',
    phoneNumber: '',
    dateOfBirth: '',
    role: 'MODERATOR' as 'MODERATOR' | 'MODERATOR_MANAGER'
  });

  useEffect(() => {
    loadModerators();
  }, []);

  const loadModerators = async () => {
    try {
      setLoading(true);
      const response = await apiService.getModerators();
      setModerators(response);
    } catch (error) {
      console.error('Erro ao carregar moderadores:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateModerator = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await apiService.createModerator(newModerator);
      setNewModerator({
        email: '',
        password: '',
        fullName: '',
        phoneNumber: '',
        dateOfBirth: '',
        role: 'MODERATOR'
      });
      setShowCreateForm(false);
      await loadModerators();
      alert('Moderador criado com sucesso!');
    } catch (error) {
      console.error('Erro ao criar moderador:', error);
      alert('Erro ao criar moderador');
    }
  };

  const filteredModerators = moderators.filter(moderator =>
    moderator.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    moderator.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'MODERATOR_MANAGER': return 'bg-purple-100 text-purple-800';
      case 'MODERATOR': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'MODERATOR_MANAGER': return 'Moderador Gerencial';
      case 'MODERATOR': return 'Moderador Básico';
      default: return role;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Gerenciamento de Moderadores
          </h2>
          <p className={`mt-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Gerencie moderadores e suas permissões
          </p>
        </div>
        
        {(currentUser?.role === 'ADMIN' || currentUser?.role === 'MODERATOR_MANAGER') && (
          <button
            onClick={() => setShowCreateForm(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Plus className="w-4 h-4" />
            <span>Criar Moderador</span>
          </button>
        )}
      </div>

      {/* Search */}
      <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Buscar moderadores..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`w-full pl-10 pr-4 py-2 border rounded-lg ${
              isDarkMode 
                ? 'bg-gray-700 border-gray-600 text-white' 
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          />
        </div>
      </div>

      {/* Create Form Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className={`w-full max-w-md p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Criar Novo Moderador
            </h3>
            
            <form onSubmit={handleCreateModerator} className="space-y-4">
              <div>
                <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Nome Completo
                </label>
                <input
                  type="text"
                  required
                  value={newModerator.fullName}
                  onChange={(e) => setNewModerator({...newModerator, fullName: e.target.value})}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    isDarkMode 
                      ? 'bg-gray-700 border-gray-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Email
                </label>
                <input
                  type="email"
                  required
                  value={newModerator.email}
                  onChange={(e) => setNewModerator({...newModerator, email: e.target.value})}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    isDarkMode 
                      ? 'bg-gray-700 border-gray-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Senha
                </label>
                <input
                  type="password"
                  required
                  value={newModerator.password}
                  onChange={(e) => setNewModerator({...newModerator, password: e.target.value})}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    isDarkMode 
                      ? 'bg-gray-700 border-gray-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Telefone
                </label>
                <input
                  type="tel"
                  required
                  value={newModerator.phoneNumber}
                  onChange={(e) => setNewModerator({...newModerator, phoneNumber: e.target.value})}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    isDarkMode 
                      ? 'bg-gray-700 border-gray-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Data de Nascimento
                </label>
                <input
                  type="date"
                  required
                  value={newModerator.dateOfBirth}
                  onChange={(e) => setNewModerator({...newModerator, dateOfBirth: e.target.value})}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    isDarkMode 
                      ? 'bg-gray-700 border-gray-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Tipo de Moderador
                </label>
                <select
                  value={newModerator.role}
                  onChange={(e) => setNewModerator({...newModerator, role: e.target.value as 'MODERATOR' | 'MODERATOR_MANAGER'})}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    isDarkMode 
                      ? 'bg-gray-700 border-gray-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                >
                  <option value="MODERATOR">Moderador Básico</option>
                  {currentUser?.role === 'ADMIN' && (
                    <option value="MODERATOR_MANAGER">Moderador Gerencial</option>
                  )}
                </select>
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Criar Moderador
                </button>
                <button
                  type="button"
                  onClick={() => setShowCreateForm(false)}
                  className={`flex-1 px-4 py-2 border rounded-lg ${
                    isDarkMode 
                      ? 'border-gray-600 text-gray-300 hover:bg-gray-700' 
                      : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Cancelar
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Moderators List */}
      <div className={`rounded-lg shadow-sm ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
        <div className="p-6">
          <div className="grid gap-4">
            {filteredModerators.map((moderator) => (
              <div
                key={moderator.id}
                className={`p-4 border rounded-lg ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <Shield className="w-6 h-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {moderator.fullName}
                      </h3>
                      <div className="flex items-center space-x-4 mt-1">
                        <div className="flex items-center space-x-1">
                          <Mail className="w-4 h-4 text-gray-400" />
                          <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            {moderator.email}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Phone className="w-4 h-4 text-gray-400" />
                          <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            {moderator.phoneNumber}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <span className={`px-3 py-1 text-sm font-medium rounded-full ${getRoleColor(moderator.role)}`}>
                      {getRoleLabel(moderator.role)}
                    </span>
                    <div className="flex items-center space-x-1 text-sm text-gray-500">
                      <Calendar className="w-4 h-4" />
                      <span>{new Date(moderator.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {filteredModerators.length === 0 && (
        <div className="text-center py-12">
          <Shield className="w-12 h-12 mx-auto mb-4 text-gray-400" />
          <h3 className={`text-lg font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Nenhum moderador encontrado
          </h3>
          <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            {searchTerm ? 'Tente ajustar o termo de busca.' : 'Crie o primeiro moderador para começar.'}
          </p>
        </div>
      )}
    </div>
  );
};

export default ModeratorManagement;
