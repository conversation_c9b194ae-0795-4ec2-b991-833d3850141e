import React, { useState, useEffect } from 'react';
import {
  Calendar,
  MapPin,
  Clock,
  DollarSign,
  User,
  Phone,
  MessageSquare,
  Star,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  Download,
  RefreshCw
} from 'lucide-react';
import { Button } from '../components/ui/button';
import BackButton from '../components/ui/BackButton';
import { useRental } from '../contexts/RentalContext';
import { useAuth } from '../contexts/AuthContext';

interface MyRentalsProps {
  isDarkMode: boolean;
}

interface Rental {
  id: string;
  equipmentName: string;
  equipmentImage: string;
  renterName: string;
  renterAvatar: string;
  renterPhone: string;
  startDate: string;
  endDate: string;
  totalDays: number;
  dailyRate: number;
  totalAmount: number;
  status: 'pending' | 'active' | 'completed' | 'cancelled';
  location: string;
  rating?: number;
  review?: string;
  paymentStatus: 'pending' | 'paid' | 'overdue';
}

const MyRentals: React.FC<MyRentalsProps> = ({ isDarkMode }) => {
  const { user } = useAuth();
  const {
    myRentals,
    loading,
    error,
    fetchMyRentals,
    updateRentalStatus,
    cancelRental
  } = useRental();

  const [activeTab, setActiveTab] = useState<'ACTIVE' | 'PENDING' | 'COMPLETED' | 'CANCELLED'>('ACTIVE');

  useEffect(() => {
    fetchMyRentals('owner'); // Carregar aluguéis onde o usuário é proprietário
  }, []);

  // Usar dados reais ou fallback para mock
  const rentals = myRentals.length > 0 ? myRentals.map(rental => ({
    id: rental.id,
    equipmentName: rental.equipment.name,
    equipmentImage: rental.equipment.images?.[0] || 'https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=600',
    renterName: rental.renter.fullName,
    renterAvatar: rental.renter.profilePicture || '/api/placeholder/40/40',
    renterPhone: '+244 923 456 789', // TODO: Implementar telefone real
    startDate: rental.startDate,
    endDate: rental.endDate,
    totalDays: Math.ceil((new Date(rental.endDate).getTime() - new Date(rental.startDate).getTime()) / (1000 * 60 * 60 * 24)),
    dailyRate: rental.equipment.price,
    totalAmount: rental.totalAmount,
    status: rental.status.toLowerCase(),
    location: 'Luanda, Angola', // TODO: Implementar localização real
    paymentStatus: rental.paymentStatus?.toLowerCase() || 'pending',
    rating: 4.8, // TODO: Implementar rating real
    review: 'Excelente equipamento!'
  })) : [
    {
      id: '1',
      equipmentName: 'Retroescavadeira CAT 420F2',
      equipmentImage: 'https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=600',
      renterName: 'João Silva',
      renterAvatar: '/api/placeholder/40/40',
      renterPhone: '+244 923 456 789',
      startDate: '2024-01-15',
      endDate: '2024-01-20',
      totalDays: 5,
      dailyRate: 250000,
      totalAmount: 1250000,
      status: 'active',
      location: 'Luanda, Talatona',
      paymentStatus: 'paid'
    },
    {
      id: '2',
      equipmentName: 'Empilhadeira Toyota 8FD',
      equipmentImage: 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?auto=format&fit=crop&w=600',
      renterName: 'Maria Santos',
      renterAvatar: '/api/placeholder/40/40',
      renterPhone: '+244 912 345 678',
      startDate: '2024-01-10',
      endDate: '2024-01-12',
      totalDays: 2,
      dailyRate: 180000,
      totalAmount: 360000,
      status: 'pending',
      location: 'Luanda, Viana',
      paymentStatus: 'pending'
    },
    {
      id: '3',
      equipmentName: 'Guindaste Liebherr LTM',
      equipmentImage: 'https://images.unsplash.com/photo-1541625602330-2277a4c46182?auto=format&fit=crop&w=600',
      renterName: 'Pedro Costa',
      renterAvatar: '/api/placeholder/40/40',
      renterPhone: '+244 934 567 890',
      startDate: '2024-01-01',
      endDate: '2024-01-05',
      totalDays: 4,
      dailyRate: 450000,
      totalAmount: 1800000,
      status: 'completed',
      location: 'Luanda, Cacuaco',
      paymentStatus: 'paid',
      rating: 5,
      review: 'Excelente equipamento! Funcionou perfeitamente durante todo o período.'
    },
    {
      id: '4',
      equipmentName: 'Compressor Atlas Copco',
      equipmentImage: 'https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=600',
      renterName: 'Ana Ferreira',
      renterAvatar: '/api/placeholder/40/40',
      renterPhone: '+244 945 678 901',
      startDate: '2023-12-20',
      endDate: '2023-12-22',
      totalDays: 2,
      dailyRate: 120000,
      totalAmount: 240000,
      status: 'cancelled',
      location: 'Luanda, Maianga',
      paymentStatus: 'pending'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'completed':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4" />;
      case 'pending':
        return <AlertCircle className="w-4 h-4" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4" />;
      default:
        return <AlertCircle className="w-4 h-4" />;
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'overdue':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  const handleRefresh = () => {
    fetchMyRentals('owner');
  };

  const handleStatusUpdate = async (rentalId: string, newStatus: string) => {
    try {
      await updateRentalStatus(rentalId, newStatus);
      fetchMyRentals('owner'); // Recarregar dados
    } catch (error) {
      console.error('Erro ao atualizar status:', error);
    }
  };

  const handleCancelRental = async (rentalId: string) => {
    try {
      await cancelRental(rentalId);
      fetchMyRentals('owner'); // Recarregar dados
    } catch (error) {
      console.error('Erro ao cancelar aluguel:', error);
    }
  };

  const filteredRentals = rentals.filter(rental =>
    rental.status.toUpperCase() === activeTab ||
    (activeTab === 'ACTIVE' && rental.status === 'active') ||
    (activeTab === 'PENDING' && rental.status === 'pending') ||
    (activeTab === 'COMPLETED' && rental.status === 'completed') ||
    (activeTab === 'CANCELLED' && rental.status === 'cancelled')
  );

  const tabs = [
    { key: 'ACTIVE', label: 'Ativos', count: rentals.filter(r => r.status === 'active' || r.status === 'ACTIVE').length },
    { key: 'PENDING', label: 'Pendentes', count: rentals.filter(r => r.status === 'pending' || r.status === 'PENDING').length },
    { key: 'COMPLETED', label: 'Concluídos', count: rentals.filter(r => r.status === 'completed' || r.status === 'COMPLETED').length },
    { key: 'CANCELLED', label: 'Cancelados', count: rentals.filter(r => r.status === 'cancelled' || r.status === 'CANCELLED').length }
  ];

  return (
    <div className="min-h-screen max-w-full overflow-x-hidden">
      <div className="container mx-auto px-4 py-8 pt-24 max-w-7xl">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <BackButton isDarkMode={isDarkMode} />
            <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Minhas Rendas</h1>
          </div>
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={loading}
            className="flex items-center space-x-2 flex-shrink-0"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span className="hidden sm:inline">Atualizar</span>
          </Button>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            <p>{error}</p>
          </div>
        )}

      {/* Estatísticas */}
      {!loading && !error && (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total de Rendas</p>
              <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{rentals.length}</p>
            </div>
            <div className="p-2 rounded-full" style={{backgroundColor: '#3569b0'}}>
              <Calendar className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>

        <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Receita Total</p>
              <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {rentals.reduce((sum, rental) => sum + rental.totalAmount, 0).toLocaleString('pt-AO')} Kz
              </p>
            </div>
            <div className="p-2 rounded-full" style={{backgroundColor: '#3569b0'}}>
              <DollarSign className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>

        <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Rendas Ativas</p>
              <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {rentals.filter(r => r.status === 'active').length}
              </p>
            </div>
            <div className="p-2 bg-blue-500 rounded-full">
              <CheckCircle className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>

        <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Avaliação Média</p>
              <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>4.8</p>
            </div>
            <div className="p-2 bg-yellow-500 rounded-full">
              <Star className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>
      </div>
      )}

        {/* Tabs */}
        {!loading && !error && (
        <div className={`border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'} mb-6`}>
          <div className="overflow-x-auto scrollbar-hide">
            <nav className="flex space-x-8 min-w-max px-1">
              {tabs.map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center space-x-2 ${
                    activeTab === tab.key
                      ? 'border-blue-500 text-blue-600'
                      : isDarkMode
                      ? 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                  style={activeTab === tab.key ? {borderColor: '#3569b0', color: '#3569b0'} : {}}
                >
                  <span>{tab.label}</span>
                  <span className={`py-0.5 px-2 rounded-full text-xs ${
                    activeTab === tab.key
                      ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-200'
                      : isDarkMode
                      ? 'bg-gray-700 text-gray-300'
                      : 'bg-gray-100 text-gray-600'
                  }`}>
                    {tab.count}
                  </span>
                </button>
              ))}
            </nav>
          </div>
        </div>
        )}

      {/* Lista de Rendas */}
      {!loading && !error && (
      <div className="space-y-4">
        {filteredRentals.length > 0 ? (
          filteredRentals.map((rental) => (
            <div
              key={rental.id}
              className={`rounded-lg shadow-md overflow-hidden ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}
            >
              <div className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                  {/* Informações do Equipamento */}
                  <div className="flex flex-col md:flex-row items-start space-y-4 md:space-y-0 md:space-x-4 mb-4 lg:mb-0">
                    <img
                      src={rental.equipmentImage}
                      alt={rental.equipmentName}
                      className="w-20 h-20 rounded-lg object-cover flex-shrink-0"
                    />
                    <div className="flex-1 min-w-0">
                      <h3 className={`text-lg font-semibold mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'} truncate`}>
                        {rental.equipmentName}
                      </h3>
                      <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-1 sm:space-y-0 text-sm">
                        <div className="flex items-center space-x-1">
                          <MapPin className="w-4 h-4 text-gray-400 flex-shrink-0" />
                          <span className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'} truncate`}>{rental.location}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4 text-gray-400 flex-shrink-0" />
                          <span className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'} text-xs sm:text-sm`}>
                            {new Date(rental.startDate).toLocaleDateString('pt-AO')} - {new Date(rental.endDate).toLocaleDateString('pt-AO')}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock className="w-4 h-4 text-gray-400 flex-shrink-0" />
                          <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>{rental.totalDays} dias</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Status e Ações */}
                  <div className="flex flex-col lg:items-end space-y-2">
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(rental.status)}`}>
                        {getStatusIcon(rental.status)}
                        <span className="ml-1 capitalize">{rental.status === 'active' ? 'Ativo' : rental.status === 'pending' ? 'Pendente' : rental.status === 'completed' ? 'Concluído' : 'Cancelado'}</span>
                      </span>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPaymentStatusColor(rental.paymentStatus)}`}>
                        {rental.paymentStatus === 'paid' ? 'Pago' : rental.paymentStatus === 'pending' ? 'Pendente' : 'Atrasado'}
                      </span>
                    </div>
                    <div className="text-right">
                      <p className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {rental.totalAmount.toLocaleString('pt-AO')} Kz
                      </p>
                      <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {rental.dailyRate.toLocaleString('pt-AO')} Kz/dia
                      </p>
                    </div>
                  </div>
                </div>

                {/* Informações do Locatário */}
                <div className={`mt-4 pt-4 border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-10 h-10 rounded-full ${isDarkMode ? 'bg-gray-600' : 'bg-gray-300'} flex items-center justify-center`}>
                        <User className="w-5 h-5" />
                      </div>
                      <div>
                        <p className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{rental.renterName}</p>
                        <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>{rental.renterPhone}</p>
                      </div>
                    </div>

                    <div className="flex flex-wrap items-center gap-2">
                      <Button variant="outline" size="sm" className="flex-shrink-0">
                        <Phone className="w-4 h-4 mr-1" />
                        Ligar
                      </Button>
                      <Button variant="outline" size="sm" className="flex-shrink-0">
                        <MessageSquare className="w-4 h-4 mr-1" />
                        Mensagem
                      </Button>
                      <Button variant="outline" size="sm" className="flex-shrink-0">
                        <Eye className="w-4 h-4 mr-1" />
                        Detalhes
                      </Button>
                      <Button variant="outline" size="sm" className="flex-shrink-0">
                        <Download className="w-4 h-4 mr-1" />
                        Contrato
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Avaliação (apenas para concluídos) */}
                {rental.status === 'completed' && rental.rating && (
                  <div className={`mt-4 pt-4 border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                    <div className="flex items-start space-x-3">
                      <div className="flex items-center space-x-1">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-4 h-4 ${
                              i < rental.rating! ? 'text-yellow-400 fill-current' : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                      <div className="flex-1">
                        <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                          "{rental.review}"
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))
        ) : (
          <div className={`rounded-lg shadow-md p-8 text-center ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <div className="text-gray-400 mb-4">
              <Calendar className="w-16 h-16 mx-auto" />
            </div>
            <h3 className={`text-lg font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Nenhuma renda {activeTab === 'active' ? 'ativa' : activeTab === 'pending' ? 'pendente' : activeTab === 'completed' ? 'concluída' : 'cancelada'}
            </h3>
            <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {activeTab === 'active' && 'Você não possui rendas ativas no momento.'}
              {activeTab === 'pending' && 'Não há rendas aguardando aprovação.'}
              {activeTab === 'completed' && 'Você ainda não possui rendas concluídas.'}
              {activeTab === 'cancelled' && 'Não há rendas canceladas.'}
            </p>
          </div>
        )}
        </div>
        )}
      </div>
    </div>
  );
};

export default MyRentals;
