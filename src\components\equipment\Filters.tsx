import React from 'react';
import { ChevronDown, ChevronRight, X } from 'lucide-react';
import { Button } from '../ui/button';

interface Category {
  id: string;
  name: string;
  subcategories: {
    id: string;
    name: string;
  }[];
}

interface FiltersProps {
  isDarkMode: boolean;
  categories: Category[];
  selectedCategory: string | null;
  setSelectedCategory: (category: string | null) => void;
  selectedSubcategory: string | null;
  setSelectedSubcategory: (subcategory: string | null) => void;
  expandedCategories: Record<string, boolean>;
  toggleCategory: (categoryId: string) => void;
  priceFilter: string | null;
  setPriceFilter: (filter: string | null) => void;
  periodFilter: 'daily' | 'weekly' | 'monthly' | 'annual' | null;
  setPeriodFilter: (filter: 'daily' | 'weekly' | 'monthly' | 'annual' | null) => void;
  showMobileFilters: boolean;
  setShowMobileFilters: (show: boolean) => void;
}

/**
 * Componente de filtros para a página de catálogo de equipamentos
 * Inclui filtros por categoria, subcategoria, preço e período
 */
const Filters: React.FC<FiltersProps> = ({
  isDarkMode,
  categories,
  selectedCategory,
  setSelectedCategory,
  selectedSubcategory,
  setSelectedSubcategory,
  expandedCategories,
  toggleCategory,
  priceFilter,
  setPriceFilter,
  periodFilter,
  setPeriodFilter,
  showMobileFilters,
  setShowMobileFilters
}) => {
  return (
    <>
      {/* Filtros (Desktop) */}
      <div className={`hidden md:block w-64 flex-shrink-0 ${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md overflow-hidden`}>
        <div className="p-4 border-b border-gray-200">
          <h2 className="font-semibold text-lg">Filtros</h2>
        </div>

        {/* Categorias */}
        <div className="p-4 border-b border-gray-200">
          <h3 className="font-medium mb-3">Categorias</h3>
          <ul className="space-y-2">
            {categories.map((category) => (
              <li key={category.id}>
                <div
                  className="flex items-center justify-between cursor-pointer"
                  onClick={() => toggleCategory(category.id)}
                >
                  <span
                    className={`${selectedCategory === category.id ? 'font-semibold text-blue-600' : ''}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedCategory(selectedCategory === category.id ? null : category.id);
                      setSelectedSubcategory(null);
                    }}
                  >
                    {category.name}
                  </span>
                  {expandedCategories[category.id] ? (
                    <ChevronDown className="w-4 h-4" />
                  ) : (
                    <ChevronRight className="w-4 h-4" />
                  )}
                </div>
                {expandedCategories[category.id] && (
                  <ul className="ml-4 mt-2 space-y-1">
                    {category.subcategories.map((subcategory) => (
                      <li
                        key={subcategory.id}
                        className={`cursor-pointer ${selectedSubcategory === subcategory.id ? 'font-semibold text-blue-600' : ''}`}
                        onClick={() => {
                          setSelectedCategory(category.id);
                          setSelectedSubcategory(selectedSubcategory === subcategory.id ? null : subcategory.id);
                        }}
                      >
                        {subcategory.name}
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            ))}
          </ul>
        </div>

        {/* Filtro de Preço */}
        <div className="p-4 border-b border-gray-200">
          <h3 className="font-medium mb-3">Preço</h3>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="radio"
                name="price"
                checked={priceFilter === 'under100k'}
                onChange={() => setPriceFilter('under100k')}
                className="mr-2"
              />
              <span>Menos de 100.000 Kz</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="price"
                checked={priceFilter === '100k-500k'}
                onChange={() => setPriceFilter('100k-500k')}
                className="mr-2"
              />
              <span>100.000 - 500.000 Kz</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="price"
                checked={priceFilter === '500k-1m'}
                onChange={() => setPriceFilter('500k-1m')}
                className="mr-2"
              />
              <span>500.000 - 1.000.000 Kz</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="price"
                checked={priceFilter === 'above1m'}
                onChange={() => setPriceFilter('above1m')}
                className="mr-2"
              />
              <span>Acima de 1.000.000 Kz</span>
            </label>
            {priceFilter && (
              <button
                onClick={() => setPriceFilter(null)}
                className="text-sm text-blue-600 hover:underline mt-2"
              >
                Limpar filtro de preço
              </button>
            )}
          </div>
        </div>

        {/* Filtro de Período */}
        <div className="p-4">
          <h3 className="font-medium mb-3">Período</h3>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="radio"
                name="period"
                checked={periodFilter === 'daily'}
                onChange={() => setPeriodFilter('daily')}
                className="mr-2"
              />
              <span>Diário</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="period"
                checked={periodFilter === 'weekly'}
                onChange={() => setPeriodFilter('weekly')}
                className="mr-2"
              />
              <span>Semanal</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="period"
                checked={periodFilter === 'monthly'}
                onChange={() => setPeriodFilter('monthly')}
                className="mr-2"
              />
              <span>Mensal</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="period"
                checked={periodFilter === 'annual'}
                onChange={() => setPeriodFilter('annual')}
                className="mr-2"
              />
              <span>Anual</span>
            </label>
            {periodFilter && (
              <button
                onClick={() => setPeriodFilter(null)}
                className="text-sm text-blue-600 hover:underline mt-2"
              >
                Limpar filtro de período
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Filtros (Mobile) */}
      {showMobileFilters && (
        <div className="md:hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-end">
          <div className={`w-4/5 h-full ${isDarkMode ? 'bg-gray-800' : 'bg-white'} overflow-y-auto`}>
            <div className="p-4 flex justify-between items-center border-b">
              <h2 className="font-semibold text-lg">Filtros</h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowMobileFilters(false)}
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            {/* Categorias */}
            <div className="p-4 border-b border-gray-200">
              <h3 className="font-medium mb-3">Categorias</h3>
              <ul className="space-y-2">
                {categories.map((category) => (
                  <li key={category.id}>
                    <div
                      className="flex items-center justify-between cursor-pointer"
                      onClick={() => toggleCategory(category.id)}
                    >
                      <span
                        className={`${selectedCategory === category.id ? 'font-semibold text-blue-600' : ''}`}
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedCategory(selectedCategory === category.id ? null : category.id);
                          setSelectedSubcategory(null);
                        }}
                      >
                        {category.name}
                      </span>
                      {expandedCategories[category.id] ? (
                        <ChevronDown className="w-4 h-4" />
                      ) : (
                        <ChevronRight className="w-4 h-4" />
                      )}
                    </div>
                    {expandedCategories[category.id] && (
                      <ul className="ml-4 mt-2 space-y-1">
                        {category.subcategories.map((subcategory) => (
                          <li
                            key={subcategory.id}
                            className={`cursor-pointer ${selectedSubcategory === subcategory.id ? 'font-semibold text-blue-600' : ''}`}
                            onClick={() => {
                              setSelectedCategory(category.id);
                              setSelectedSubcategory(selectedSubcategory === subcategory.id ? null : subcategory.id);
                            }}
                          >
                            {subcategory.name}
                          </li>
                        ))}
                      </ul>
                    )}
                  </li>
                ))}
              </ul>
            </div>

            {/* Filtro de Preço */}
            <div className="p-4 border-b border-gray-200">
              <h3 className="font-medium mb-3">Preço</h3>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="price-mobile"
                    checked={priceFilter === 'under100k'}
                    onChange={() => setPriceFilter('under100k')}
                    className="mr-2"
                  />
                  <span>Menos de 100.000 Kz</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="price-mobile"
                    checked={priceFilter === '100k-500k'}
                    onChange={() => setPriceFilter('100k-500k')}
                    className="mr-2"
                  />
                  <span>100.000 - 500.000 Kz</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="price-mobile"
                    checked={priceFilter === '500k-1m'}
                    onChange={() => setPriceFilter('500k-1m')}
                    className="mr-2"
                  />
                  <span>500.000 - 1.000.000 Kz</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="price-mobile"
                    checked={priceFilter === 'above1m'}
                    onChange={() => setPriceFilter('above1m')}
                    className="mr-2"
                  />
                  <span>Acima de 1.000.000 Kz</span>
                </label>
                {priceFilter && (
                  <button
                    onClick={() => setPriceFilter(null)}
                    className="text-sm text-blue-600 hover:underline mt-2"
                  >
                    Limpar filtro de preço
                  </button>
                )}
              </div>
            </div>

            {/* Filtro de Período */}
            <div className="p-4">
              <h3 className="font-medium mb-3">Período</h3>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="period-mobile"
                    checked={periodFilter === 'daily'}
                    onChange={() => setPeriodFilter('daily')}
                    className="mr-2"
                  />
                  <span>Diário</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="period-mobile"
                    checked={periodFilter === 'weekly'}
                    onChange={() => setPeriodFilter('weekly')}
                    className="mr-2"
                  />
                  <span>Semanal</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="period-mobile"
                    checked={periodFilter === 'monthly'}
                    onChange={() => setPeriodFilter('monthly')}
                    className="mr-2"
                  />
                  <span>Mensal</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="period-mobile"
                    checked={periodFilter === 'annual'}
                    onChange={() => setPeriodFilter('annual')}
                    className="mr-2"
                  />
                  <span>Anual</span>
                </label>
                {periodFilter && (
                  <button
                    onClick={() => setPeriodFilter(null)}
                    className="text-sm text-blue-600 hover:underline mt-2"
                  >
                    Limpar filtro de período
                  </button>
                )}
              </div>
            </div>

            <div className="p-4 border-t sticky bottom-0 bg-inherit">
              <Button
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                onClick={() => setShowMobileFilters(false)}
              >
                Aplicar Filtros
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Filters;
