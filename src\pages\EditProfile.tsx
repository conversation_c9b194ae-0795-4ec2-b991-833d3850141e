import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Button } from '../components/ui/button';
import BackButton from '../components/ui/BackButton';
import {
  User, Mail, Phone, Calendar, CreditCard, MapPin,
  Building, Camera, Save, X, Upload, FileText, AlertCircle
} from 'lucide-react';

interface EditProfileProps {
  isDarkMode?: boolean;
}

export default function EditProfile({ isDarkMode = false }: EditProfileProps) {
  const navigate = useNavigate();
  const { user, updateUser } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState('');
  const [profilePicture, setProfilePicture] = useState<File | null>(null);
  const [profilePicturePreview, setProfilePicturePreview] = useState('');
  const [companyDocuments, setCompanyDocuments] = useState<File[]>([]);

  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phoneNumber: '',
    dateOfBirth: '',
    nif: '',
    companyName: '',
    companyAddress: '',
    companyType: '',
    bio: '',
    occupation: '',
    location: ''
  });

  const [biDocument, setBiDocument] = useState<File | null>(null);
  const [companyCoverImage, setCompanyCoverImage] = useState<File | null>(null);
  const [companyCoverPreview, setCompanyCoverPreview] = useState<string>('');

  // Carregar dados do usuário
  useEffect(() => {
    if (user) {
      setFormData({
        fullName: user.fullName || '',
        email: user.email || '',
        phoneNumber: user.phoneNumber || '',
        dateOfBirth: user.dateOfBirth ? user.dateOfBirth.split('T')[0] : '',
        nif: user.nif || '',
        companyName: user.companyName || '',
        companyAddress: user.companyAddress || '',
        companyType: user.companyType || '',
        bio: user.bio || '',
        occupation: user.occupation || '',
        location: user.location || ''
      });
      setProfilePicturePreview(user.profilePicture || '');
    }
  }, [user]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    // Limpar erro do campo quando o usuário começar a digitar
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleProfilePictureChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB
        setErrors(prev => ({ ...prev, profilePicture: 'Imagem deve ter menos de 5MB' }));
        return;
      }
      
      if (!file.type.startsWith('image/')) {
        setErrors(prev => ({ ...prev, profilePicture: 'Apenas imagens são permitidas' }));
        return;
      }

      setProfilePicture(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfilePicturePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
      setErrors(prev => ({ ...prev, profilePicture: '' }));
    }
  };

  const handleDocumentUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const validFiles = files.filter(file => {
      const isValidType = file.type === 'application/pdf' || file.type.startsWith('image/');
      const isValidSize = file.size <= 5 * 1024 * 1024; // 5MB
      return isValidType && isValidSize;
    });

    if (validFiles.length !== files.length) {
      setErrors(prev => ({ ...prev, documents: 'Alguns arquivos foram rejeitados. Apenas PDF e imagens até 5MB são aceitos.' }));
    }

    setCompanyDocuments(prev => [...prev, ...validFiles]);
  };

  const removeDocument = (index: number) => {
    setCompanyDocuments(prev => prev.filter((_, i) => i !== index));
  };

  const handleBiDocumentUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB
        setErrors(prev => ({ ...prev, biDocument: 'Arquivo deve ter menos de 5MB' }));
        return;
      }

      if (!file.type.startsWith('image/') && file.type !== 'application/pdf') {
        setErrors(prev => ({ ...prev, biDocument: 'Apenas imagens e PDFs são aceitos' }));
        return;
      }

      setBiDocument(file);
      setErrors(prev => ({ ...prev, biDocument: '' }));
    }
  };

  const handleCompanyCoverUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB
        setErrors(prev => ({ ...prev, companyCoverImage: 'Arquivo deve ter menos de 5MB' }));
        return;
      }

      if (!file.type.startsWith('image/')) {
        setErrors(prev => ({ ...prev, companyCoverImage: 'Apenas imagens são aceitas' }));
        return;
      }

      setCompanyCoverImage(file);

      // Criar preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setCompanyCoverPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      setErrors(prev => ({ ...prev, companyCoverImage: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: any = {};

    if (!formData.fullName || formData.fullName.length < 3) {
      newErrors.fullName = 'Nome completo deve ter pelo menos 3 caracteres';
    }

    if (!formData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email inválido';
    }

    if (!formData.phoneNumber) {
      newErrors.phoneNumber = 'Telefone é obrigatório';
    } else {
      const phoneRegex = /^(\+244|00244)?[9][0-9]{8}$/;
      if (!phoneRegex.test(formData.phoneNumber.replace(/\s/g, ''))) {
        newErrors.phoneNumber = 'Número de telefone angolano inválido';
      }
    }

    if (formData.dateOfBirth) {
      const date = new Date(formData.dateOfBirth);
      const today = new Date();
      let age = today.getFullYear() - date.getFullYear();
      const monthDiff = today.getMonth() - date.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < date.getDate())) {
        age--;
      }

      if (age < 18) {
        newErrors.dateOfBirth = 'Deve ser maior de 18 anos';
      }
    }



    // Validações específicas para empresas
    if (user?.isCompany) {
      if (!formData.companyName) {
        newErrors.companyName = 'Nome da empresa é obrigatório';
      }

      if (!formData.nif) {
        newErrors.nif = 'NIF é obrigatório para empresas';
      } else if (!/^5[0-9]{8}$/.test(formData.nif.replace(/\s/g, ''))) {
        newErrors.nif = 'NIF empresarial inválido (deve começar com 5 e ter 9 dígitos)';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setSuccessMessage('');

    try {
      // Upload da foto de perfil se houver
      let profilePictureUrl = user?.profilePicture;
      if (profilePicture) {
        const formDataUpload = new FormData();
        formDataUpload.append('file', profilePicture);
        
        const uploadResponse = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:3000'}/upload/profile-picture`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          },
          body: formDataUpload,
        });
        
        if (uploadResponse.ok) {
          const uploadResult = await uploadResponse.json();
          profilePictureUrl = uploadResult.url;
        }
      }

      // Upload da foto de capa da empresa se houver
      let companyCoverImageUrl = '';
      if (companyCoverImage) {
        const formDataUpload = new FormData();
        formDataUpload.append('file', companyCoverImage);

        const uploadResponse = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:3000'}/upload/profile`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          },
          body: formDataUpload,
        });

        if (uploadResponse.ok) {
          const uploadResult = await uploadResponse.json();
          companyCoverImageUrl = uploadResult.url;
        }
      }

      // Upload dos documentos da empresa se houver
      const documentUrls = [];
      if (companyDocuments.length > 0) {
        for (const file of companyDocuments) {
          const formDataUpload = new FormData();
          formDataUpload.append('file', file);
          
          try {
            const uploadResponse = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:3000'}/upload/document`, {
              method: 'POST',
              body: formDataUpload,
            });
            
            if (uploadResponse.ok) {
              const uploadResult = await uploadResponse.json();
              documentUrls.push(uploadResult.url);
            }
          } catch (uploadError) {
            console.error('Erro no upload do documento:', uploadError);
          }
        }
      }

      // Preparar dados para atualização (apenas campos permitidos)
      const updateData: any = {
        fullName: formData.fullName,
        email: formData.email,
        phoneNumber: formData.phoneNumber,
        dateOfBirth: formData.dateOfBirth,
        nif: formData.nif,
        bio: formData.bio,
        occupation: formData.occupation,
        location: formData.location,
      };

      // Adicionar campos específicos de empresa se aplicável
      if (user?.isCompany) {
        updateData.companyName = formData.companyName;
        updateData.companyAddress = formData.companyAddress;
        updateData.companyType = formData.companyType;
        if (documentUrls.length > 0) {
          updateData.companyDocuments = documentUrls;
        }
        if (companyCoverImageUrl) {
          updateData.companyCoverImage = companyCoverImageUrl;
        }
      }

      // Adicionar foto de perfil se houver
      if (profilePictureUrl) {
        updateData.profilePicture = profilePictureUrl;
      }



      await updateUser(updateData);
      setSuccessMessage('Perfil atualizado com sucesso!');
      
      // Redirecionar após 2 segundos
      setTimeout(() => {
        navigate('/profile');
      }, 2000);

    } catch (error: any) {
      setErrors({ submit: error.message || 'Erro ao atualizar perfil' });
    } finally {
      setIsLoading(false);
    }
  };

  const renderField = (name: string, label: string, type: string, icon: React.ReactNode, placeholder: string, required = false) => {
    const hasError = errors[name];
    return (
      <div className="relative">
        <label htmlFor={name} className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
          {label} {required && '*'}
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            {icon}
          </div>
          <input
            id={name}
            name={name}
            type={type}
            value={formData[name]}
            onChange={handleChange}
            className={`appearance-none rounded-lg relative block w-full pl-10 px-3 py-2 border ${
              hasError ? 'border-red-500' : isDarkMode ? 'border-gray-600' : 'border-gray-300'
            } ${
              isDarkMode ? 'bg-gray-800 text-white placeholder-gray-400' : 'bg-white text-gray-900 placeholder-gray-500'
            } focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 focus:z-10 sm:text-sm`}
            placeholder={placeholder}
          />
        </div>
        {hasError && (
          <div className="text-red-500 text-xs mt-1 flex items-center">
            <AlertCircle className="h-3 w-3 mr-1" />
            {errors[name]}
          </div>
        )}
      </div>
    );
  };

  const renderSelect = (name: string, label: string, icon: React.ReactNode, options: { value: string; label: string }[], required = false) => {
    const hasError = errors[name];
    return (
      <div className="relative">
        <label htmlFor={name} className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
          {label} {required && '*'}
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            {icon}
          </div>
          <select
            id={name}
            name={name}
            value={formData[name]}
            onChange={handleChange}
            className={`appearance-none rounded-lg relative block w-full pl-10 px-3 py-2 border ${
              hasError ? 'border-red-500' : isDarkMode ? 'border-gray-600' : 'border-gray-300'
            } ${
              isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'
            } focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 focus:z-10 sm:text-sm`}
          >
            <option value="">{label}</option>
            {options.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
        {hasError && (
          <div className="text-red-500 text-xs mt-1 flex items-center">
            <AlertCircle className="h-3 w-3 mr-1" />
            {errors[name]}
          </div>
        )}
      </div>
    );
  };

  const renderTextArea = (name: string, label: string, placeholder: string, rows = 3) => {
    const hasError = errors[name];
    return (
      <div className="relative">
        <label htmlFor={name} className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
          {label}
        </label>
        <textarea
          id={name}
          name={name}
          rows={rows}
          value={formData[name]}
          onChange={handleChange}
          className={`appearance-none rounded-lg relative block w-full px-3 py-2 border ${
            hasError ? 'border-red-500' : isDarkMode ? 'border-gray-600' : 'border-gray-300'
          } ${
            isDarkMode ? 'bg-gray-800 text-white placeholder-gray-400' : 'bg-white text-gray-900 placeholder-gray-500'
          } focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 focus:z-10 sm:text-sm`}
          placeholder={placeholder}
        />
        {hasError && (
          <div className="text-red-500 text-xs mt-1 flex items-center">
            <AlertCircle className="h-3 w-3 mr-1" />
            {errors[name]}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="container mx-auto px-4 py-8 pt-24">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-6">
            <BackButton isDarkMode={isDarkMode} />
          </div>

          <div className="mb-8">
            <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Editar Perfil
            </h1>
            <p className={`mt-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Atualize suas informações pessoais e de empresa
            </p>
          </div>

          {/* Success Message */}
          {successMessage && (
            <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md">
              <div className="flex items-center">
                <AlertCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">{successMessage}</span>
              </div>
            </div>
          )}

          {/* Error Message */}
          {errors.submit && (
            <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
              <div className="flex items-center">
                <AlertCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">{errors.submit}</span>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Profile Picture Section */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
              <h2 className="text-xl font-semibold mb-4">Foto de Perfil</h2>
              <div className="flex items-center space-x-6">
                <div className="relative">
                  <img
                    src={profilePicturePreview || "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"}
                    alt="Profile"
                    className="w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg"
                  />
                  <label htmlFor="profilePicture" className="absolute bottom-0 right-0 p-2 bg-cyan-500 rounded-full shadow-lg cursor-pointer hover:bg-cyan-600 transition-colors">
                    <Camera className="w-4 h-4 text-white" />
                  </label>
                  <input
                    id="profilePicture"
                    type="file"
                    accept="image/*"
                    onChange={handleProfilePictureChange}
                    className="hidden"
                  />
                </div>
                <div>
                  <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Clique no ícone da câmera para alterar sua foto de perfil
                  </p>
                  <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} mt-1`}>
                    Formatos aceitos: JPG, PNG (máx. 5MB)
                  </p>
                  {errors.profilePicture && (
                    <div className="text-red-500 text-xs mt-1 flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {errors.profilePicture}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Personal Information Section */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
              <h2 className="text-xl font-semibold mb-6">Informações Pessoais</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {renderField('fullName', 'Nome Completo', 'text', <User className="h-5 w-5 text-gray-400" />, 'Nome completo', true)}
                {renderField('email', 'Email', 'email', <Mail className="h-5 w-5 text-gray-400" />, 'Email', true)}
                {renderField('phoneNumber', 'Telefone', 'tel', <Phone className="h-5 w-5 text-gray-400" />, 'Telefone (ex: +244 *********)', true)}
                {renderField('dateOfBirth', 'Data de Nascimento', 'date', <Calendar className="h-5 w-5 text-gray-400" />, 'Data de Nascimento')}

                {!user?.isCompany && renderField('nif', 'Número do BI', 'text', <CreditCard className="h-5 w-5 text-gray-400" />, 'Número do BI (ex: 000000000LA000)')}

                {renderField('location', 'Localização', 'text', <MapPin className="h-5 w-5 text-gray-400" />, 'Cidade, País')}
              </div>

              <div className="mt-6">
                {renderTextArea('bio', 'Biografia', 'Conte um pouco sobre você...', 4)}
              </div>

              <div className="mt-6">
                {renderField('occupation', 'Profissão', 'text', <User className="h-5 w-5 text-gray-400" />, 'Sua profissão')}
              </div>

              {/* BI Document Upload (for non-company users) */}
              {!user?.isCompany && (
                <div className="mt-6">
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Documento de Identidade (BI) - Opcional
                  </label>
                  <div className="flex items-center justify-center w-full">
                    <label htmlFor="biDocument" className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer ${isDarkMode ? 'border-gray-600 bg-gray-700 hover:bg-gray-600' : 'border-gray-300 bg-gray-50 hover:bg-gray-100'}`}>
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <Upload className={`w-8 h-8 mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                        <p className={`mb-2 text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          <span className="font-semibold">Clique para enviar</span> seu BI
                        </p>
                        <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          PDF ou imagem (máx. 5MB)
                        </p>
                      </div>
                      <input
                        id="biDocument"
                        type="file"
                        accept=".pdf,image/*"
                        onChange={handleBiDocumentUpload}
                        className="hidden"
                      />
                    </label>
                  </div>

                  {biDocument && (
                    <div className="mt-4">
                      <div className={`flex items-center justify-between p-2 rounded border ${isDarkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-300 bg-gray-50'}`}>
                        <div className="flex items-center">
                          <FileText className="w-4 h-4 mr-2 text-gray-500" />
                          <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                            {biDocument.name}
                          </span>
                        </div>
                        <button
                          type="button"
                          onClick={() => setBiDocument(null)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  )}

                  {errors.biDocument && (
                    <div className="text-red-500 text-xs mt-1 flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {errors.biDocument}
                    </div>
                  )}
                </div>
              )}
            </div>



            {/* Company Information Section (only for companies) */}
            {user?.isCompany && (
              <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                <h2 className="text-xl font-semibold mb-6">Informações da Empresa</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {renderField('companyName', 'Nome da Empresa', 'text', <Building className="h-5 w-5 text-gray-400" />, 'Nome da Empresa', true)}

                  {renderSelect('companyType', 'Tipo de Empresa', <Building className="h-5 w-5 text-gray-400" />, [
                    { value: 'LDA', label: 'Limitada (Lda)' },
                    { value: 'SA', label: 'Sociedade Anónima (SA)' },
                    { value: 'UNIPESSOAL', label: 'Unipessoal' },
                    { value: 'COOPERATIVA', label: 'Cooperativa' },
                    { value: 'OUTRO', label: 'Outro' }
                  ], true)}

                  {renderField('nif', 'NIF Empresarial', 'text', <CreditCard className="h-5 w-5 text-gray-400" />, 'NIF Empresarial (ex: *********)', true)}
                </div>

                <div className="mt-6">
                  {renderField('companyAddress', 'Morada da Empresa', 'text', <MapPin className="h-5 w-5 text-gray-400" />, 'Morada da Empresa')}
                </div>

                {/* Company Cover Image Upload */}
                <div className="mt-6">
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Foto de Capa da Empresa - Opcional
                  </label>
                  <div className="flex items-center justify-center w-full">
                    <label htmlFor="companyCoverImage" className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer ${isDarkMode ? 'border-gray-600 bg-gray-700 hover:bg-gray-600' : 'border-gray-300 bg-gray-50 hover:bg-gray-100'}`}>
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <Upload className={`w-8 h-8 mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                        <p className={`mb-2 text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          <span className="font-semibold">Clique para enviar</span> foto de capa
                        </p>
                        <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          Imagem (máx. 5MB)
                        </p>
                      </div>
                      <input
                        id="companyCoverImage"
                        type="file"
                        accept="image/*"
                        onChange={handleCompanyCoverUpload}
                        className="hidden"
                      />
                    </label>
                  </div>

                  {companyCoverPreview && (
                    <div className="mt-4">
                      <div className={`relative rounded-lg overflow-hidden ${isDarkMode ? 'border-gray-600' : 'border-gray-300'} border`}>
                        <img
                          src={companyCoverPreview}
                          alt="Company Cover Preview"
                          className="w-full h-32 object-cover"
                        />
                        <button
                          type="button"
                          onClick={() => {
                            setCompanyCoverImage(null);
                            setCompanyCoverPreview('');
                          }}
                          className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  )}

                  {errors.companyCoverImage && (
                    <div className="text-red-500 text-xs mt-1 flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {errors.companyCoverImage}
                    </div>
                  )}
                </div>

                {/* Company Documents Upload */}
                <div className="mt-6">
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Documentos da Empresa
                  </label>
                  <div className="flex items-center justify-center w-full">
                    <label htmlFor="companyDocuments" className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer ${isDarkMode ? 'border-gray-600 bg-gray-700 hover:bg-gray-600' : 'border-gray-300 bg-gray-50 hover:bg-gray-100'}`}>
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <Upload className={`w-8 h-8 mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                        <p className={`mb-2 text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          <span className="font-semibold">Clique para enviar</span> novos documentos
                        </p>
                        <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          PDF ou imagens (máx. 5MB cada)
                        </p>
                      </div>
                      <input
                        id="companyDocuments"
                        type="file"
                        multiple
                        accept=".pdf,image/*"
                        onChange={handleDocumentUpload}
                        className="hidden"
                      />
                    </label>
                  </div>

                  {/* Lista de documentos enviados */}
                  {companyDocuments.length > 0 && (
                    <div className="mt-4 space-y-2">
                      <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        Novos documentos:
                      </p>
                      {companyDocuments.map((file, index) => (
                        <div key={index} className={`flex items-center justify-between p-2 rounded border ${isDarkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-300 bg-gray-50'}`}>
                          <div className="flex items-center">
                            <FileText className="w-4 h-4 mr-2 text-gray-500" />
                            <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                              {file.name}
                            </span>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeDocument(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  {errors.documents && (
                    <div className="text-red-500 text-xs mt-1 flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {errors.documents}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/profile')}
                className="flex items-center space-x-2"
              >
                <X className="w-4 h-4" />
                <span>Cancelar</span>
              </Button>

              <Button
                type="submit"
                disabled={isLoading}
                className="flex items-center space-x-2 bg-cyan-500 hover:bg-cyan-600 text-white"
              >
                <Save className="w-4 h-4" />
                <span>{isLoading ? 'Salvando...' : 'Salvar Alterações'}</span>
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
