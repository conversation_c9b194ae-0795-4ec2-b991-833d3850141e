/**
 * Interface para os dados de um equipamento
 */
export interface Equipment {
  id: string;
  title: string;
  description: string;
  classCode: string;
  price: {
    daily: number;
    weekly: number;
    monthly: number;
    annual?: number; // Preço anual opcional
  };
  image: string;
  category: string;
  subcategory: string;
  specifications: string[];
}

/**
 * Interface para categorias e subcategorias
 */
export interface Category {
  id: string;
  name: string;
  subcategories: {
    id: string;
    name: string;
  }[];
}

/**
 * Tipos de visualização disponíveis para a lista de equipamentos
 */
export type ViewType = 'list' | 'grid' | 'card';

/**
 * Tipos de ordenação disponíveis para a lista de equipamentos
 */
export type SortOrder = 'relevance' | 'price_asc' | 'price_desc' | 'newest';

/**
 * Tipos de período para filtro de preço
 */
export type PeriodFilter = 'daily' | 'weekly' | 'monthly' | 'annual' | null;

/**
 * Tipos de filtro de preço
 */
export type PriceFilter = 'under100k' | '100k-500k' | '500k-1m' | 'above1m' | null;
