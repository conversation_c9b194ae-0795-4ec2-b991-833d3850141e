import { useState } from 'react';
import { Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { EquipmentProvider } from './contexts/EquipmentContext';
import { ChatProvider } from './contexts/ChatContext';
import { RentalProvider } from './contexts/RentalContext';
import { ModerationProvider } from './contexts/ModerationContext';
import { NotificationProvider } from './contexts/NotificationContext';
import Layout from './components/Layout';
import ProtectedRoute from './components/ProtectedRoute';
import RoleProtectedRoute from './components/RoleProtectedRoute';
import Home from './pages/Home';
import Auth from './pages/Auth';
import Cart from './pages/Cart';
import Notifications from './pages/Notifications';
// import EquipmentList from './pages/EquipmentList'; // Substituído por AllEquipment
import EquipmentDetails from './pages/EquipmentDetails';
import Profile from './pages/Profile';
import Categories from './pages/Categories';
import CreateListing from './pages/CreateListing';
import Messages from './pages/Messages';
import Favorites from './pages/Favorites';
import MyRentals from './pages/MyRentals';
import History from './pages/History';
import Settings from './pages/Settings';
import MapSearch from './pages/MapSearch';
// import AllEquipment from './pages/AllEquipment'; // Removido - usando EquipmentCatalog
import EquipmentCatalog from './pages/EquipmentCatalog';
import AdminDashboard from './pages/AdminDashboard';
import PrivacyPolicy from './pages/PrivacyPolicy';
import TermsOfService from './pages/TermsOfService';
import Help from './pages/Help';
import Contact from './pages/Contact';
import About from './pages/About';
import Wallet from './pages/Wallet';
import PublicProfile from './pages/PublicProfile';
import Blog from './pages/Blog';
import BlogPost from './pages/BlogPost';
import Careers from './pages/Careers';
import AdminContentManager from './pages/AdminContentManager';
import ModerationDashboard from './pages/ModerationDashboard';
import EditProfile from './pages/EditProfile';
import PaymentValidation from './pages/PaymentValidation';
import VerifyEmail from './pages/VerifyEmail';
import ModeratorBiValidation from './pages/ModeratorBiValidation';
import LandlordValidation from './pages/LandlordValidation';
import HierarchicalAdminDashboard from './pages/HierarchicalAdminDashboard';
import AdminOnlyRoute, { AdminRedirectHandler } from './components/AdminOnlyRoute';

function App() {
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Toggle dark mode function
  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
  };

  return (
    <AuthProvider>
      <NotificationProvider>
        <EquipmentProvider>
          <ChatProvider>
            <RentalProvider>
              <ModerationProvider>
                <Layout isDarkMode={isDarkMode} toggleDarkMode={toggleDarkMode}>
                    <AdminRedirectHandler />
                    <Routes>
        {/* Rotas públicas */}
        <Route path="/auth" element={
          <ProtectedRoute requireAuth={false}>
            <Auth isDarkMode={isDarkMode} />
          </ProtectedRoute>
        } />
        <Route path="/" element={<Home isDarkMode={isDarkMode} />} />
        <Route path="/equipment" element={<EquipmentCatalog isDarkMode={isDarkMode} />} />
        <Route path="/equipment/:id" element={<EquipmentDetails isDarkMode={isDarkMode} />} />
        <Route path="/categories" element={<Categories isDarkMode={isDarkMode} />} />
        <Route path="/user/:userId" element={<PublicProfile isDarkMode={isDarkMode} />} />
        <Route path="/blog" element={<Blog isDarkMode={isDarkMode} />} />
        <Route path="/blog/:postId" element={<BlogPost isDarkMode={isDarkMode} />} />
        <Route path="/contact" element={<Contact isDarkMode={isDarkMode} />} />
        <Route path="/about" element={<About isDarkMode={isDarkMode} />} />
        <Route path="/help" element={<Help isDarkMode={isDarkMode} />} />
        <Route path="/privacy" element={<PrivacyPolicy isDarkMode={isDarkMode} />} />
        <Route path="/terms" element={<TermsOfService isDarkMode={isDarkMode} />} />
        <Route path="/careers" element={<Careers isDarkMode={isDarkMode} />} />
        <Route path="/verify-email/:token" element={<VerifyEmail isDarkMode={isDarkMode} />} />

        {/* Rotas protegidas */}
        <Route path="/cart" element={
          <ProtectedRoute>
            <Cart isDarkMode={isDarkMode} />
          </ProtectedRoute>
        } />
        <Route path="/notifications" element={
          <ProtectedRoute>
            <Notifications isDarkMode={isDarkMode} />
          </ProtectedRoute>
        } />
        <Route path="/profile" element={
          <ProtectedRoute>
            <Profile isDarkMode={isDarkMode} />
          </ProtectedRoute>
        } />
        <Route path="/profile/edit" element={
          <ProtectedRoute>
            <EditProfile isDarkMode={isDarkMode} />
          </ProtectedRoute>
        } />
        <Route path="/create-listing" element={
          <ProtectedRoute>
            <CreateListing isDarkMode={isDarkMode} />
          </ProtectedRoute>
        } />
        <Route path="/messages" element={
          <ProtectedRoute>
            <Messages isDarkMode={isDarkMode} />
          </ProtectedRoute>
        } />
        <Route path="/favorites" element={
          <ProtectedRoute>
            <Favorites isDarkMode={isDarkMode} />
          </ProtectedRoute>
        } />
        <Route path="/my-rentals" element={
          <ProtectedRoute>
            <MyRentals isDarkMode={isDarkMode} />
          </ProtectedRoute>
        } />
        <Route path="/history" element={
          <ProtectedRoute>
            <History isDarkMode={isDarkMode} />
          </ProtectedRoute>
        } />
        <Route path="/payment-validation" element={
          <ProtectedRoute>
            <PaymentValidation isDarkMode={isDarkMode} />
          </ProtectedRoute>
        } />
        <Route path="/settings" element={
          <ProtectedRoute>
            <Settings isDarkMode={isDarkMode} toggleDarkMode={toggleDarkMode} />
          </ProtectedRoute>
        } />
        <Route path="/map" element={
          <ProtectedRoute>
            <MapSearch isDarkMode={isDarkMode} />
          </ProtectedRoute>
        } />
        <Route path="/wallet" element={
          <ProtectedRoute>
            <Wallet isDarkMode={isDarkMode} />
          </ProtectedRoute>
        } />
        <Route path="/admin" element={
          <ProtectedRoute>
            <RoleProtectedRoute allowedRoles={['ADMIN']}>
              <AdminDashboard isDarkMode={isDarkMode} />
            </RoleProtectedRoute>
          </ProtectedRoute>
        } />
        <Route path="/admin/content" element={
          <ProtectedRoute>
            <RoleProtectedRoute allowedRoles={['ADMIN']}>
              <AdminContentManager isDarkMode={isDarkMode} />
            </RoleProtectedRoute>
          </ProtectedRoute>
        } />
        <Route path="/moderation" element={
          <ProtectedRoute>
            <RoleProtectedRoute allowedRoles={['MODERATOR', 'MODERATOR_MANAGER', 'ADMIN']}>
              <ModerationDashboard isDarkMode={isDarkMode} />
            </RoleProtectedRoute>
          </ProtectedRoute>
        } />
        <Route path="/moderation/bi-validation" element={
          <ProtectedRoute>
            <RoleProtectedRoute allowedRoles={['MODERATOR', 'MODERATOR_MANAGER', 'ADMIN']}>
              <ModeratorBiValidation isDarkMode={isDarkMode} />
            </RoleProtectedRoute>
          </ProtectedRoute>
        } />
        <Route path="/moderation/landlord-validation" element={
          <ProtectedRoute>
            <RoleProtectedRoute allowedRoles={['MODERATOR_MANAGER', 'ADMIN']}>
              <LandlordValidation isDarkMode={isDarkMode} />
            </RoleProtectedRoute>
          </ProtectedRoute>
        } />
        <Route path="/admin-dashboard" element={
          <ProtectedRoute>
            <AdminOnlyRoute>
              <HierarchicalAdminDashboard isDarkMode={isDarkMode} />
            </AdminOnlyRoute>
          </ProtectedRoute>
        } />
                    </Routes>
                </Layout>
              </ModerationProvider>
            </RentalProvider>
          </ChatProvider>
        </EquipmentProvider>
      </NotificationProvider>
    </AuthProvider>
  );
}

export default App;