import { useState } from 'react';
import { Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import Home from './pages/Home';
import Auth from './pages/Auth';
import Cart from './pages/Cart';
import Notifications from './pages/Notifications';
// import EquipmentList from './pages/EquipmentList'; // Substituído por AllEquipment
import EquipmentDetails from './pages/EquipmentDetails';
import Profile from './pages/Profile';
import Categories from './pages/Categories';
import CreateListing from './pages/CreateListing';
import Messages from './pages/Messages';
import Favorites from './pages/Favorites';
import MyRentals from './pages/MyRentals';
import History from './pages/History';
import Settings from './pages/Settings';
import MapSearch from './pages/MapSearch';
// import AllEquipment from './pages/AllEquipment'; // Removido - usando EquipmentCatalog
import EquipmentCatalog from './pages/EquipmentCatalog';
import AdminDashboard from './pages/AdminDashboard';
import PrivacyPolicy from './pages/PrivacyPolicy';
import TermsOfService from './pages/TermsOfService';
import Help from './pages/Help';
import Contact from './pages/Contact';
import About from './pages/About';
import Wallet from './pages/Wallet';
import PublicProfile from './pages/PublicProfile';
import Blog from './pages/Blog';
import BlogPost from './pages/BlogPost';
import Careers from './pages/Careers';
import AdminContentManager from './pages/AdminContentManager';

function App() {
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Toggle dark mode function
  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
  };

  return (
    <Layout isDarkMode={isDarkMode} toggleDarkMode={toggleDarkMode}>

      <Routes>
        <Route path="/auth" element={<Auth isDarkMode={isDarkMode} />} />
        <Route path="/cart" element={<Cart isDarkMode={isDarkMode} />} />
        <Route path="/notifications" element={<Notifications isDarkMode={isDarkMode} />} />
        <Route path="/equipment" element={<EquipmentCatalog isDarkMode={isDarkMode} />} />
        <Route path="/equipment/:id" element={<EquipmentDetails isDarkMode={isDarkMode} />} />
        <Route path="/profile" element={<Profile isDarkMode={isDarkMode} />} />
        <Route path="/categories" element={<Categories isDarkMode={isDarkMode} />} />
        <Route path="/create-listing" element={<CreateListing isDarkMode={isDarkMode} />} />
        <Route path="/messages" element={<Messages isDarkMode={isDarkMode} />} />
        <Route path="/favorites" element={<Favorites isDarkMode={isDarkMode} />} />
        <Route path="/my-rentals" element={<MyRentals isDarkMode={isDarkMode} />} />
        <Route path="/history" element={<History isDarkMode={isDarkMode} />} />
        <Route path="/settings" element={<Settings isDarkMode={isDarkMode} toggleDarkMode={toggleDarkMode} />} />
        <Route path="/map" element={<MapSearch isDarkMode={isDarkMode} />} />
        <Route path="/admin" element={<AdminDashboard isDarkMode={isDarkMode} />} />
        <Route path="/privacy" element={<PrivacyPolicy isDarkMode={isDarkMode} />} />
        <Route path="/terms" element={<TermsOfService isDarkMode={isDarkMode} />} />
        <Route path="/help" element={<Help isDarkMode={isDarkMode} />} />
        <Route path="/contact" element={<Contact isDarkMode={isDarkMode} />} />
        <Route path="/about" element={<About isDarkMode={isDarkMode} />} />
        <Route path="/wallet" element={<Wallet isDarkMode={isDarkMode} />} />
        <Route path="/user/:userId" element={<PublicProfile isDarkMode={isDarkMode} />} />
        <Route path="/blog" element={<Blog isDarkMode={isDarkMode} />} />
        <Route path="/blog/:postId" element={<BlogPost isDarkMode={isDarkMode} />} />
        <Route path="/careers" element={<Careers isDarkMode={isDarkMode} />} />
        <Route path="/admin/content" element={<AdminContentManager isDarkMode={isDarkMode} />} />
        <Route path="/" element={<Home isDarkMode={isDarkMode} />} />
      </Routes>

    </Layout>
  );
}

export default App;