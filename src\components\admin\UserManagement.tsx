import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import adminApi from '../../services/adminApi';
import { 
  Users, 
  Search, 
  Ban, 
  CheckCircle, 
  XCircle,
  Eye,
  Edit,
  Trash2,
  Loader2,
  AlertCircle,
  ChevronLeft,
  ChevronRight,
  X,
  Save
} from 'lucide-react';

interface User {
  id: string;
  email: string;
  fullName: string;
  phoneNumber?: string;
  userType: string;
  role: string;
  accountStatus: string;
  isBlocked: boolean;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  isCompany?: boolean;
  companyName?: string;
  createdAt: string;
  updatedAt: string;
}

interface UserDetails extends User {
  dateOfBirth?: string;
  bio?: string;
  occupation?: string;
  location?: string;
  companyAddress?: string;
  companyType?: string;
  nif?: string;
  profilePicture?: string;
  biDocument?: string;
  biValidated?: boolean;
  approvedAt?: string;
  rejectedAt?: string;
  rejectionReason?: string;
}

interface UserManagementProps {
  isDarkMode?: boolean;
}

interface UsersResponse {
  data: User[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

const UserManagement: React.FC<UserManagementProps> = ({ isDarkMode = false }) => {
  const { user: currentUser } = useAuth();
  const [usersData, setUsersData] = useState<UsersResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterUserType, setFilterUserType] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [selectedUser, setSelectedUser] = useState<UserDetails | null>(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingUser, setEditingUser] = useState<Partial<UserDetails>>({});
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    loadUsers();
  }, [currentPage, searchTerm, filterUserType, filterStatus]);

  const showMessage = (type: 'success' | 'error', text: string) => {
    setMessage({ type, text });
    setTimeout(() => setMessage(null), 5000);
  };

  const loadUsers = async () => {
    try {
      setLoading(true);
      const response = await adminApi.getUsers({
        page: currentPage,
        limit: pageSize,
        search: searchTerm || undefined,
        userType: filterUserType || undefined,
        status: filterStatus || undefined,
      });
      setUsersData(response);
    } catch (error: any) {
      console.error('Erro ao carregar usuários:', error);
      showMessage('error', error.message || 'Erro ao carregar usuários');
      setUsersData(null);
    } finally {
      setLoading(false);
    }
  };

  const handleViewUser = async (userId: string) => {
    try {
      setActionLoading(userId);
      const userDetails = await adminApi.getUserDetails(userId);
      setSelectedUser(userDetails);
      setShowUserModal(true);
    } catch (error: any) {
      console.error('Erro ao carregar detalhes do usuário:', error);
      showMessage('error', error.message || 'Erro ao carregar detalhes do usuário');
    } finally {
      setActionLoading(null);
    }
  };

  const handleEditUser = (user: User) => {
    setEditingUser({
      id: user.id,
      fullName: user.fullName,
      email: user.email,
      phoneNumber: user.phoneNumber,
      companyName: user.companyName,
      isEmailVerified: user.isEmailVerified,
      isPhoneVerified: user.isPhoneVerified,
    });
    setShowEditModal(true);
  };

  const handleSaveUser = async () => {
    try {
      setActionLoading('save');
      await adminApi.updateUser(editingUser.id!, editingUser);
      showMessage('success', 'Usuário atualizado com sucesso!');
      setShowEditModal(false);
      setEditingUser({});
      await loadUsers();
    } catch (error: any) {
      console.error('Erro ao atualizar usuário:', error);
      showMessage('error', error.message || 'Erro ao atualizar usuário');
    } finally {
      setActionLoading(null);
    }
  };

  const handleBlockUser = async (userId: string, blocked: boolean, reason?: string) => {
    try {
      setActionLoading(userId);
      await adminApi.toggleUserBlock(userId, blocked, reason);
      showMessage('success', `Usuário ${blocked ? 'bloqueado' : 'desbloqueado'} com sucesso!`);
      await loadUsers();
    } catch (error: any) {
      console.error('Erro ao bloquear/desbloquear usuário:', error);
      showMessage('error', error.message || 'Erro ao bloquear/desbloquear usuário');
    } finally {
      setActionLoading(null);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Tem certeza que deseja deletar este usuário? Esta ação não pode ser desfeita.')) {
      return;
    }

    try {
      setActionLoading(userId);
      await adminApi.deleteUser(userId);
      showMessage('success', 'Usuário deletado com sucesso!');
      await loadUsers();
    } catch (error: any) {
      console.error('Erro ao deletar usuário:', error);
      showMessage('error', error.message || 'Erro ao deletar usuário');
    } finally {
      setActionLoading(null);
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleSearch = () => {
    setCurrentPage(1);
    loadUsers();
  };

  const clearFilters = () => {
    setSearchTerm('');
    setFilterUserType('');
    setFilterStatus('');
    setCurrentPage(1);
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'bg-red-100 text-red-800';
      case 'MODERATOR_MANAGER': return 'bg-purple-100 text-purple-800';
      case 'MODERATOR': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED': return 'bg-green-100 text-green-800';
      case 'PENDING': return 'bg-yellow-100 text-yellow-800';
      case 'REJECTED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getUserTypeLabel = (userType: string) => {
    switch (userType) {
      case 'LANDLORD': return 'Locador';
      case 'TENANT': return 'Locatário';
      default: return userType;
    }
  };

  const getRoleLabel = (role: string, userType: string) => {
    switch (role) {
      case 'ADMIN': return 'Administrador';
      case 'MODERATOR_MANAGER': return 'Mod. Gerencial';
      case 'MODERATOR': return 'Moderador';
      case 'USER':
        return userType === 'LANDLORD' ? 'Locador' : 'Locatário';
      default: return role;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Gerenciamento de Usuários
          </h2>
          <p className={`mt-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Gerencie todos os usuários da plataforma
          </p>
        </div>
        <div className="text-sm text-gray-500">
          {usersData ? `${usersData.total} usuários encontrados` : ''}
        </div>
      </div>

      {/* Message */}
      {message && (
        <div className={`p-4 rounded-lg flex items-center space-x-2 ${
          message.type === 'success'
            ? 'bg-green-100 text-green-800 border border-green-200'
            : 'bg-red-100 text-red-800 border border-red-200'
        }`}>
          {message.type === 'success' ? (
            <CheckCircle className="w-5 h-5" />
          ) : (
            <AlertCircle className="w-5 h-5" />
          )}
          <span>{message.text}</span>
        </div>
      )}

      {/* Filters */}
      <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Nome, email ou telefone..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className={`w-full pl-10 pr-4 py-2 border rounded-lg ${
                isDarkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            />
          </div>

          <select
            value={filterUserType}
            onChange={(e) => setFilterUserType(e.target.value)}
            className={`px-3 py-2 border rounded-lg ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value="">Todos os Roles</option>
            <option value="TENANT">Locatários</option>
            <option value="LANDLORD">Locadores</option>
            <option value="ADMIN">Administradores</option>
            <option value="MODERATOR">Moderadores</option>
            <option value="MODERATOR_MANAGER">Mod. Gerenciais</option>
          </select>

          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className={`px-3 py-2 border rounded-lg ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value="">Todos os Status</option>
            <option value="approved">Aprovados</option>
            <option value="pending">Pendentes</option>
            <option value="rejected">Rejeitados</option>
            <option value="blocked">Bloqueados</option>
            <option value="verified">Verificados</option>
          </select>

          <button
            onClick={handleSearch}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Buscar
          </button>

          <button
            onClick={clearFilters}
            className={`px-4 py-2 border rounded-lg transition-colors ${
              isDarkMode
                ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            Limpar
          </button>
        </div>
      </div>

      {/* Users Table */}
      <div className={`rounded-lg shadow-sm overflow-hidden ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
        {!usersData || usersData.data.length === 0 ? (
          <div className="p-8 text-center">
            <Users className={`w-12 h-12 mx-auto mb-4 ${isDarkMode ? 'text-gray-600' : 'text-gray-400'}`} />
            <h3 className={`text-lg font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Nenhum usuário encontrado
            </h3>
            <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {searchTerm || filterUserType || filterStatus
                ? 'Tente ajustar os filtros de busca'
                : 'Não há usuários cadastrados no sistema'}
            </p>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className={isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}>
                  <tr>
                    <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      Usuário
                    </th>
                    <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      Role
                    </th>
                    <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      Status
                    </th>
                    <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      Verificação
                    </th>
                    <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      Criado em
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody className={`divide-y ${isDarkMode ? 'divide-gray-700' : 'divide-gray-200'}`}>
                  {usersData.data.map((user) => (
                    <tr key={user.id} className={isDarkMode ? 'bg-gray-800 hover:bg-gray-700' : 'bg-white hover:bg-gray-50'}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <Users className="w-5 h-5 text-blue-600" />
                          </div>
                          <div className="ml-4">
                            <div className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                              {user.fullName}
                              {user.isCompany && user.companyName && (
                                <span className="text-xs text-gray-500 block">
                                  {user.companyName}
                                </span>
                              )}
                            </div>
                            <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                              {user.email}
                            </div>
                            {user.phoneNumber && (
                              <div className={`text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                                {user.phoneNumber}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(user.role)}`}>
                          {getRoleLabel(user.role, user.userType)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col space-y-1">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(user.accountStatus)}`}>
                            {user.accountStatus === 'APPROVED' ? 'Aprovado' :
                             user.accountStatus === 'PENDING' ? 'Pendente' : 'Rejeitado'}
                          </span>
                          {user.isBlocked && (
                            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                              Bloqueado
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex space-x-1">
                          {user.isEmailVerified ? (
                            <CheckCircle className="w-4 h-4 text-green-500" title="Email verificado" />
                          ) : (
                            <XCircle className="w-4 h-4 text-red-500" title="Email não verificado" />
                          )}
                          {user.isPhoneVerified ? (
                            <CheckCircle className="w-4 h-4 text-green-500" title="Telefone verificado" />
                          ) : (
                            <XCircle className="w-4 h-4 text-gray-400" title="Telefone não verificado" />
                          )}
                        </div>
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        {new Date(user.createdAt).toLocaleDateString('pt-BR')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          {/* Não mostrar ações para o próprio usuário logado */}
                          {user.id !== currentUser?.id ? (
                            <>
                              <button
                                onClick={() => handleViewUser(user.id)}
                                disabled={actionLoading === user.id}
                                className="text-blue-600 hover:text-blue-900 disabled:opacity-50"
                                title="Ver detalhes"
                              >
                                {actionLoading === user.id ? (
                                  <Loader2 className="w-4 h-4 animate-spin" />
                                ) : (
                                  <Eye className="w-4 h-4" />
                                )}
                              </button>
                              <button
                                onClick={() => handleEditUser(user)}
                                className="text-green-600 hover:text-green-900"
                                title="Editar"
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleBlockUser(user.id, !user.isBlocked)}
                                disabled={actionLoading === user.id}
                                className={`disabled:opacity-50 ${
                                  user.isBlocked
                                    ? 'text-green-600 hover:text-green-900'
                                    : 'text-red-600 hover:text-red-900'
                                }`}
                                title={user.isBlocked ? 'Desbloquear' : 'Bloquear'}
                              >
                                {user.isBlocked ? <CheckCircle className="w-4 h-4" /> : <Ban className="w-4 h-4" />}
                              </button>

                              {currentUser?.role === 'ADMIN' && (
                                <button
                                  onClick={() => handleDeleteUser(user.id)}
                                  disabled={actionLoading === user.id}
                                  className="text-red-600 hover:text-red-900 disabled:opacity-50"
                                  title="Deletar"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </button>
                              )}
                            </>
                          ) : (
                            <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                              Você
                            </span>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {usersData.totalPages > 1 && (
              <div className={`px-6 py-3 border-t ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'}`}>
                <div className="flex items-center justify-between">
                  <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-700'}`}>
                    Mostrando {((usersData.page - 1) * usersData.limit) + 1} a {Math.min(usersData.page * usersData.limit, usersData.total)} de {usersData.total} usuários
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handlePageChange(usersData.page - 1)}
                      disabled={usersData.page <= 1}
                      className={`p-2 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
                        isDarkMode
                          ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                          : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                      }`}
                    >
                      <ChevronLeft className="w-4 h-4" />
                    </button>

                    <span className={`px-3 py-1 text-sm ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      Página {usersData.page} de {usersData.totalPages}
                    </span>

                    <button
                      onClick={() => handlePageChange(usersData.page + 1)}
                      disabled={usersData.page >= usersData.totalPages}
                      className={`p-2 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
                        isDarkMode
                          ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                          : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                      }`}
                    >
                      <ChevronRight className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* User Details Modal */}
      {showUserModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`max-w-4xl w-full mx-4 rounded-lg shadow-xl max-h-[90vh] overflow-y-auto ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Detalhes do Usuário
                </h3>
                <button
                  onClick={() => setShowUserModal(false)}
                  className={`p-2 rounded-lg transition-colors ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Informações Básicas */}
                <div>
                  <h4 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    Informações Básicas
                  </h4>
                  <div className="space-y-3">
                    <div>
                      <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        Nome Completo
                      </label>
                      <p className={`mt-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {selectedUser.fullName}
                      </p>
                    </div>
                    <div>
                      <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        Email
                      </label>
                      <p className={`mt-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {selectedUser.email}
                      </p>
                    </div>
                    {selectedUser.phoneNumber && (
                      <div>
                        <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                          Telefone
                        </label>
                        <p className={`mt-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {selectedUser.phoneNumber}
                        </p>
                      </div>
                    )}
                    <div>
                      <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        Role
                      </label>
                      <p className={`mt-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {getRoleLabel(selectedUser.role, selectedUser.userType)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Status e Verificações */}
                <div>
                  <h4 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    Status e Verificações
                  </h4>
                  <div className="space-y-3">
                    <div>
                      <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        Status da Conta
                      </label>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(selectedUser.accountStatus)}`}>
                        {selectedUser.accountStatus === 'APPROVED' ? 'Aprovado' :
                         selectedUser.accountStatus === 'PENDING' ? 'Pendente' : 'Rejeitado'}
                      </span>
                    </div>
                    <div>
                      <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        Email Verificado
                      </label>
                      <div className="flex items-center space-x-2">
                        {selectedUser.isEmailVerified ? (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        ) : (
                          <XCircle className="w-4 h-4 text-red-500" />
                        )}
                        <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>
                          {selectedUser.isEmailVerified ? 'Sim' : 'Não'}
                        </span>
                      </div>
                    </div>
                    <div>
                      <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        Telefone Verificado
                      </label>
                      <div className="flex items-center space-x-2">
                        {selectedUser.isPhoneVerified ? (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        ) : (
                          <XCircle className="w-4 h-4 text-gray-400" />
                        )}
                        <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>
                          {selectedUser.isPhoneVerified ? 'Sim' : 'Não'}
                        </span>
                      </div>
                    </div>
                    <div>
                      <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        Conta Bloqueada
                      </label>
                      <div className="flex items-center space-x-2">
                        {selectedUser.isBlocked ? (
                          <Ban className="w-4 h-4 text-red-500" />
                        ) : (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        )}
                        <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>
                          {selectedUser.isBlocked ? 'Sim' : 'Não'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Informações da Empresa (se aplicável) */}
              {selectedUser.isCompany && (
                <div className="mt-6">
                  <h4 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    Informações da Empresa
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {selectedUser.companyName && (
                      <div>
                        <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                          Nome da Empresa
                        </label>
                        <p className={`mt-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {selectedUser.companyName}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Datas */}
              <div className="mt-6">
                <h4 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Datas Importantes
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Criado em
                    </label>
                    <p className={`mt-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {new Date(selectedUser.createdAt).toLocaleDateString('pt-BR')}
                    </p>
                  </div>
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Atualizado em
                    </label>
                    <p className={`mt-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {new Date(selectedUser.updatedAt).toLocaleDateString('pt-BR')}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit User Modal */}
      {showEditModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`max-w-2xl w-full mx-4 rounded-lg shadow-xl ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Editar Usuário
                </h3>
                <button
                  onClick={() => setShowEditModal(false)}
                  className={`p-2 rounded-lg transition-colors ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Nome Completo
                    </label>
                    <input
                      type="text"
                      value={editingUser.fullName || ''}
                      onChange={(e) => setEditingUser({ ...editingUser, fullName: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Email
                    </label>
                    <input
                      type="email"
                      value={editingUser.email || ''}
                      onChange={(e) => setEditingUser({ ...editingUser, email: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Telefone
                    </label>
                    <input
                      type="text"
                      value={editingUser.phoneNumber || ''}
                      onChange={(e) => setEditingUser({ ...editingUser, phoneNumber: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  {editingUser.companyName !== undefined && (
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        Nome da Empresa
                      </label>
                      <input
                        type="text"
                        value={editingUser.companyName || ''}
                        onChange={(e) => setEditingUser({ ...editingUser, companyName: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                          isDarkMode
                            ? 'bg-gray-700 border-gray-600 text-white'
                            : 'bg-white border-gray-300 text-gray-900'
                        }`}
                      />
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isEmailVerified"
                      checked={editingUser.isEmailVerified || false}
                      onChange={(e) => setEditingUser({ ...editingUser, isEmailVerified: e.target.checked })}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <label htmlFor="isEmailVerified" className={`ml-2 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Email verificado
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isPhoneVerified"
                      checked={editingUser.isPhoneVerified || false}
                      onChange={(e) => setEditingUser({ ...editingUser, isPhoneVerified: e.target.checked })}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <label htmlFor="isPhoneVerified" className={`ml-2 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Telefone verificado
                    </label>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowEditModal(false)}
                  className={`px-4 py-2 border rounded-lg transition-colors ${
                    isDarkMode
                      ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                      : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Cancelar
                </button>
                <button
                  onClick={handleSaveUser}
                  disabled={actionLoading === 'save' || !editingUser.fullName || !editingUser.email}
                  className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {actionLoading === 'save' ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Save className="w-4 h-4" />
                  )}
                  <span>{actionLoading === 'save' ? 'Salvando...' : 'Salvar'}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagement;
