import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Loader2, 
  Users, 
  Award, 
  MapPin, 
  Calendar,
  Target,
  Heart,
  Shield,
  Zap
} from 'lucide-react';
import { Button } from '../components/ui/button';
import apiService from '../services/api';

interface AboutProps {
  isDarkMode: boolean;
}

const About: React.FC<AboutProps> = ({ isDarkMode }) => {
  const navigate = useNavigate();
  const [content, setContent] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadContent = async () => {
      try {
        setLoading(true);
        const response = await apiService.getAboutContent();
        setContent(response);
      } catch (error) {
        console.error('Erro ao carregar conteúdo:', error);
        setContent({
          title: 'Sobre a YMRentals',
          content: 'Somos a principal plataforma de aluguel de equipamentos em Angola, conectando proprietários e locatários de forma simples, segura e eficiente.'
        });
      } finally {
        setLoading(false);
      }
    };

    loadContent();
  }, []);

  // Estatísticas da empresa
  const stats = [
    { icon: Users, number: '10,000+', label: 'Clientes Ativos' },
    { icon: Award, number: '5,000+', label: 'Equipamentos' },
    { icon: MapPin, number: '18', label: 'Províncias' },
    { icon: Calendar, number: '5+', label: 'Anos de Experiência' }
  ];

  // Valores da empresa
  const values = [
    {
      icon: Target,
      title: 'Missão',
      description: 'Facilitar o acesso a equipamentos de qualidade, conectando proprietários e locatários de forma eficiente e segura.'
    },
    {
      icon: Heart,
      title: 'Valores',
      description: 'Transparência, confiabilidade, inovação e compromisso com a satisfação dos nossos clientes.'
    },
    {
      icon: Shield,
      title: 'Segurança',
      description: 'Garantimos transações seguras e equipamentos verificados para total tranquilidade dos usuários.'
    },
    {
      icon: Zap,
      title: 'Inovação',
      description: 'Utilizamos tecnologia de ponta para oferecer a melhor experiência em aluguel de equipamentos.'
    }
  ];

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Botão de Voltar */}
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => navigate(-1)}
            className={`flex items-center space-x-2 ${isDarkMode ? 'text-white hover:bg-gray-800' : 'text-gray-700 hover:bg-gray-100'}`}
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Voltar</span>
          </Button>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-20">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
            <span className={`ml-2 ${isDarkMode ? 'text-white' : 'text-gray-700'}`}>
              Carregando conteúdo...
            </span>
          </div>
        )}

        {/* Hero Section - Conteúdo Dinâmico */}
        {!loading && content && (
          <div className="text-center mb-16">
            <h1 className={`text-4xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              {content.title || 'Sobre a YMRentals'}
            </h1>
            <div 
              className={`text-xl max-w-3xl mx-auto leading-relaxed ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}
              dangerouslySetInnerHTML={{ __html: content.content || 'Conteúdo não disponível.' }}
            />
          </div>
        )}

        {/* Estatísticas */}
        {!loading && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <div key={index} className={`text-center p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
                  <div className="w-12 h-12 mx-auto mb-4 rounded-full flex items-center justify-center" style={{backgroundColor: '#3569b0'}}>
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  <div className={`text-3xl font-bold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {stat.number}
                  </div>
                  <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {stat.label}
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Nossa História */}
        {!loading && (
          <div className="mb-16">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className={`text-3xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Nossa História
                </h2>
                <p className={`text-lg mb-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  A YMRentals nasceu da necessidade de democratizar o acesso a equipamentos de qualidade em Angola. 
                  Fundada em 2019, nossa plataforma conecta proprietários de equipamentos a empresas e profissionais 
                  que precisam de soluções temporárias para seus projetos.
                </p>
                <p className={`text-lg ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  Desde então, crescemos exponencialmente, tornando-nos a principal referência em aluguel de 
                  equipamentos no país, sempre priorizando a segurança, qualidade e satisfação dos nossos clientes.
                </p>
              </div>
              <div className={`p-8 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
                <h3 className={`text-xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Marcos Importantes
                </h3>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-3 h-3 rounded-full mt-2" style={{backgroundColor: '#3569b0'}}></div>
                    <div>
                      <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>2019</p>
                      <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Fundação da YMRentals</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-3 h-3 rounded-full mt-2" style={{backgroundColor: '#3569b0'}}></div>
                    <div>
                      <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>2021</p>
                      <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Expansão para todas as províncias</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-3 h-3 rounded-full mt-2" style={{backgroundColor: '#3569b0'}}></div>
                    <div>
                      <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>2023</p>
                      <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>10.000+ clientes ativos</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-3 h-3 rounded-full mt-2" style={{backgroundColor: '#3569b0'}}></div>
                    <div>
                      <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>2024</p>
                      <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Lançamento da nova plataforma</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Nossos Valores */}
        {!loading && (
          <div className="mb-16">
            <div className="text-center mb-12">
              <h2 className={`text-3xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Nossos Valores
              </h2>
              <p className={`text-lg max-w-2xl mx-auto ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Os princípios que guiam nossa empresa e definem nossa cultura organizacional
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {values.map((value, index) => {
                const IconComponent = value.icon;
                return (
                  <div key={index} className={`text-center p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md hover:shadow-lg transition-shadow`}>
                    <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" style={{backgroundColor: '#3569b0'}}>
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>
                    <h3 className={`text-xl font-bold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {value.title}
                    </h3>
                    <p className={`text-sm leading-relaxed ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {value.description}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Call to Action */}
        {!loading && (
          <div className={`text-center p-12 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
            <h2 className={`text-3xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Faça Parte da Nossa História
            </h2>
            <p className={`text-lg mb-8 max-w-2xl mx-auto ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Junte-se a milhares de clientes que confiam na YMRentals para seus projetos. 
              Descubra como podemos ajudar você a alcançar seus objetivos.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                className="text-white"
                style={{backgroundColor: '#3569b0'}}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2a5490'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3569b0'}
                onClick={() => navigate('/equipment')}
              >
                Explorar Equipamentos
              </Button>
              <Button 
                variant="outline"
                onClick={() => navigate('/contact')}
              >
                Entre em Contato
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default About;
