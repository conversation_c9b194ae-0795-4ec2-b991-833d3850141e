import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowLeft,
  Target,
  Eye,
  Heart,
  Users,
  Award,
  Truck,
  Shield,
  Clock,
  Star,
  CheckCircle,
  Globe
} from 'lucide-react';
import { Button } from '../components/ui/button';

interface AboutProps {
  isDarkMode: boolean;
}

const About: React.FC<AboutProps> = ({ isDarkMode }) => {
  const navigate = useNavigate();

  const stats = [
    { number: '500+', label: 'Equipamentos Disponíveis', icon: Truck },
    { number: '1000+', label: 'Clientes Satisfeitos', icon: Users },
    { number: '50+', label: 'Cidades Atendidas', icon: Globe },
    { number: '24/7', label: 'Suporte Disponível', icon: Clock }
  ];

  const values = [
    {
      icon: Shield,
      title: 'Confiabilidade',
      description: 'Equipamentos verificados e seguros para todos os projetos'
    },
    {
      icon: Clock,
      title: 'Agilidade',
      description: 'Processo rápido de aluguel com entrega no prazo'
    },
    {
      icon: Heart,
      title: 'Compromisso',
      description: 'Dedicados ao sucesso dos nossos clientes'
    },
    {
      icon: Award,
      title: 'Qualidade',
      description: 'Equipamentos de alta qualidade e bem mantidos'
    }
  ];

  const team = [
    {
      name: 'Carlos Mendes',
      role: 'CEO & Fundador',
      image: 'https://randomuser.me/api/portraits/men/32.jpg',
      description: 'Engenheiro com 15 anos de experiência no setor de construção'
    },
    {
      name: 'Ana Silva',
      role: 'Diretora de Operações',
      image: 'https://randomuser.me/api/portraits/women/44.jpg',
      description: 'Especialista em logística e gestão de equipamentos'
    },
    {
      name: 'Pedro Santos',
      role: 'Diretor Técnico',
      image: 'https://randomuser.me/api/portraits/men/67.jpg',
      description: 'Técnico especializado em manutenção de equipamentos pesados'
    }
  ];

  const milestones = [
    { year: '2020', event: 'Fundação da YMRentals' },
    { year: '2021', event: 'Primeiro centro de distribuição em Luanda' },
    { year: '2022', event: 'Expansão para 10 cidades' },
    { year: '2023', event: 'Lançamento da plataforma digital' },
    { year: '2024', event: 'Mais de 1000 clientes ativos' }
  ];

  return (
    <div className="container mx-auto px-4 py-8 pt-24">
      {/* Botão de Voltar */}
      <div className="mb-6">
        <Button
          variant="outline"
          onClick={() => navigate(-1)}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Voltar</span>
        </Button>
      </div>

      {/* Hero Section */}
      <div className="text-center mb-16">
        <h1 className={`text-4xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Sobre a YMRentals
        </h1>
        <p className={`text-xl max-w-3xl mx-auto leading-relaxed ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          Somos a principal plataforma de aluguel de equipamentos em Angola, conectando proprietários 
          e locatários de forma simples, segura e eficiente.
        </p>
      </div>

      {/* Estatísticas */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
        {stats.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <div key={index} className={`text-center p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
              <div className="w-12 h-12 mx-auto mb-4 rounded-full flex items-center justify-center" style={{backgroundColor: '#3569b0'}}>
                <IconComponent className="w-6 h-6 text-white" />
              </div>
              <div className={`text-3xl font-bold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {stat.number}
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {stat.label}
              </div>
            </div>
          );
        })}
      </div>

      {/* Nossa História */}
      <div className="mb-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className={`text-3xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Nossa História
            </h2>
            <p className={`text-lg mb-6 leading-relaxed ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              A YMRentals nasceu da necessidade de democratizar o acesso a equipamentos de construção 
              e industriais em Angola. Fundada em 2020, nossa missão é facilitar o crescimento de 
              empresas e projetos através de uma plataforma inovadora.
            </p>
            <p className={`text-lg leading-relaxed ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Hoje, somos referência no mercado angolano, oferecendo uma ampla gama de equipamentos 
              com a garantia de qualidade e suporte que nossos clientes merecem.
            </p>
          </div>
          <div className={`rounded-lg overflow-hidden ${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>
            <img 
              src="https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=800" 
              alt="Equipamentos YMRentals"
              className="w-full h-80 object-cover"
            />
          </div>
        </div>
      </div>

      {/* Missão, Visão e Valores */}
      <div className="mb-16">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className={`p-8 rounded-lg text-center ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
            <div className="w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center" style={{backgroundColor: '#3569b0'}}>
              <Target className="w-8 h-8 text-white" />
            </div>
            <h3 className={`text-xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Missão
            </h3>
            <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Facilitar o acesso a equipamentos de qualidade, impulsionando o desenvolvimento 
              de projetos e empresas em Angola.
            </p>
          </div>

          <div className={`p-8 rounded-lg text-center ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
            <div className="w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center" style={{backgroundColor: '#3569b0'}}>
              <Eye className="w-8 h-8 text-white" />
            </div>
            <h3 className={`text-xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Visão
            </h3>
            <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Ser a principal plataforma de aluguel de equipamentos em África, 
              reconhecida pela inovação e excelência.
            </p>
          </div>

          <div className={`p-8 rounded-lg text-center ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
            <div className="w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center" style={{backgroundColor: '#3569b0'}}>
              <Heart className="w-8 h-8 text-white" />
            </div>
            <h3 className={`text-xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Valores
            </h3>
            <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Confiança, transparência, inovação e compromisso com o sucesso 
              dos nossos clientes e parceiros.
            </p>
          </div>
        </div>
      </div>

      {/* Nossos Valores */}
      <div className="mb-16">
        <h2 className={`text-3xl font-bold text-center mb-12 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          O que nos Move
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {values.map((value, index) => {
            const IconComponent = value.icon;
            return (
              <div key={index} className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
                <div className="w-12 h-12 mb-4 rounded-lg flex items-center justify-center" style={{backgroundColor: '#3569b0'}}>
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
                <h3 className={`text-lg font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {value.title}
                </h3>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {value.description}
                </p>
              </div>
            );
          })}
        </div>
      </div>

      {/* Nossa Equipe */}
      <div className="mb-16">
        <h2 className={`text-3xl font-bold text-center mb-12 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Nossa Equipe
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {team.map((member, index) => (
            <div key={index} className={`text-center p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
              <img 
                src={member.image} 
                alt={member.name}
                className="w-24 h-24 rounded-full mx-auto mb-4 object-cover"
              />
              <h3 className={`text-xl font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {member.name}
              </h3>
              <p className="text-blue-600 font-medium mb-3" style={{color: '#3569b0'}}>
                {member.role}
              </p>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {member.description}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Timeline */}
      <div className="mb-16">
        <h2 className={`text-3xl font-bold text-center mb-12 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Nossa Jornada
        </h2>
        <div className="max-w-4xl mx-auto">
          {milestones.map((milestone, index) => (
            <div key={index} className="flex items-center mb-8">
              <div className="w-16 h-16 rounded-full flex items-center justify-center flex-shrink-0 mr-6" style={{backgroundColor: '#3569b0'}}>
                <span className="text-white font-bold">{milestone.year}</span>
              </div>
              <div className={`p-4 rounded-lg flex-1 ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
                <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  {milestone.event}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Call to Action */}
      <div className={`text-center p-12 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
        <h2 className={`text-3xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Faça Parte da Nossa História
        </h2>
        <p className={`text-lg mb-8 max-w-2xl mx-auto ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          Junte-se a milhares de clientes que confiam na YMRentals para seus projetos. 
          Descubra como podemos ajudar você a alcançar seus objetivos.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button 
            className="text-white"
            style={{backgroundColor: '#3569b0'}}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2a5490'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3569b0'}
            onClick={() => navigate('/equipment')}
          >
            Explorar Equipamentos
          </Button>
          <Button 
            variant="outline"
            onClick={() => navigate('/contact')}
          >
            Entre em Contato
          </Button>
        </div>
      </div>
    </div>
  );
};

export default About;
