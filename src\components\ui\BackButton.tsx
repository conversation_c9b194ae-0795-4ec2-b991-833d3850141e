import React, { useState, useEffect } from 'react';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface BackButtonProps {
  onClick?: () => void;
  className?: string;
  isDarkMode?: boolean;
}

const BackButton: React.FC<BackButtonProps> = ({
  onClick,
  className = '',
  isDarkMode = false
}) => {
  const navigate = useNavigate();
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      navigate(-1);
    }
  };

  // Só mostrar em mobile
  if (!isMobile) return null;

  return (
    <button
      onClick={handleClick}
      className={`
        p-2
        transition-all
        duration-200
        hover:scale-110
        active:scale-95
        ${isDarkMode ? 'text-white hover:text-gray-300' : 'text-gray-700 hover:text-gray-900'}
        ${className}
      `}
      aria-label="Voltar"
    >
      <ArrowLeft className="w-5 h-5" />
    </button>
  );
};

export default BackButton;
