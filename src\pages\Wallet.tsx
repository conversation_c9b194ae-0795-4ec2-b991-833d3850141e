import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Wallet as WalletIcon,
  DollarSign,
  ArrowUpCircle,
  ArrowDownCircle,
  Eye,
  EyeOff,
  Building2,
  Smartphone,
  CreditCard,
  History,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { Button } from '../components/ui/button';
import BackButton from '../components/ui/BackButton';

interface WalletProps {
  isDarkMode: boolean;
}

const Wallet: React.FC<WalletProps> = ({ isDarkMode }) => {
  const navigate = useNavigate();

  // Estados para carteira digital
  const [showBalance, setShowBalance] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [showDepositModal, setShowDepositModal] = useState(false);
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [depositAmount, setDepositAmount] = useState('');
  const [selectedWithdrawMethod, setSelectedWithdrawMethod] = useState('bank');
  const [selectedDepositMethod, setSelectedDepositMethod] = useState('bank');

  // Dados mockados da carteira
  const userBalance = 2450000; // 2.450.000 Kz
  const pendingWithdrawals = 500000; // 500.000 Kz
  const totalIncome = 5200000; // 5.200.000 Kz
  const totalExpenses = 320000; // 320.000 Kz

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('pt-AO') + ' Kz';
  };

  const handleWithdraw = () => {
    console.log('Saque:', withdrawAmount, selectedWithdrawMethod);
    setShowWithdrawModal(false);
    setWithdrawAmount('');
  };

  const handleDeposit = () => {
    console.log('Depósito:', depositAmount, selectedDepositMethod);
    setShowDepositModal(false);
    setDepositAmount('');
  };

  // Transações recentes mockadas
  const recentTransactions = [
    {
      id: '1',
      type: 'income',
      description: 'Aluguel - Retroescavadeira CAT',
      amount: 1250000,
      date: '2024-01-20',
      method: 'Transferência Bancária'
    },
    {
      id: '2',
      type: 'expense',
      description: 'Taxa da Plataforma',
      amount: -62500,
      date: '2024-01-20',
      method: 'Débito Automático'
    },
    {
      id: '3',
      type: 'income',
      description: 'Aluguel - Empilhadeira Toyota',
      amount: 360000,
      date: '2024-01-18',
      method: 'Pagamento Móvel'
    },
    {
      id: '4',
      type: 'expense',
      description: 'Saque para Conta Bancária',
      amount: -800000,
      date: '2024-01-15',
      method: 'Transferência'
    }
  ];

  return (
    <div className="min-h-screen max-w-full overflow-x-hidden">
      <div className={`container mx-auto px-4 py-8 pt-24 max-w-6xl ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
        <div className="flex items-center space-x-4 mb-8">
          <BackButton isDarkMode={isDarkMode} />
          <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Minha Conta</h1>
        </div>

        {/* Resumo da Carteira */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Saldo Disponível</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {showBalance ? formatCurrency(userBalance) : '••••••••'}
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <DollarSign className="w-8 h-8" style={{color: '#3569b0'}} />
                <button
                  onClick={() => setShowBalance(!showBalance)}
                  className={`p-2 rounded-lg ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
                >
                  {showBalance ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>
          </div>

          <div className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Saques Pendentes</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {showBalance ? formatCurrency(pendingWithdrawals) : '••••••••'}
                </p>
              </div>
              <ArrowUpCircle className="w-8 h-8 text-yellow-500" />
            </div>
          </div>

          <div className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Receitas Totais</p>
                <p className={`text-2xl font-bold text-green-600`}>
                  {showBalance ? formatCurrency(totalIncome) : '••••••••'}
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-500" />
            </div>
          </div>

          <div className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Despesas Totais</p>
                <p className={`text-2xl font-bold text-red-600`}>
                  {showBalance ? formatCurrency(totalExpenses) : '••••••••'}
                </p>
              </div>
              <TrendingDown className="w-8 h-8 text-red-500" />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Ações Principais */}
          <div className="lg:col-span-2">
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-6 mb-8`}>
              <h2 className={`text-xl font-semibold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Ações Rápidas
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <Button
                  onClick={() => setShowDepositModal(true)}
                  className="flex items-center justify-center space-x-2 text-white p-6 h-auto"
                  style={{backgroundColor: '#3569b0'}}
                >
                  <ArrowDownCircle className="w-6 h-6" />
                  <div className="text-left">
                    <div className="font-semibold">Depositar Dinheiro</div>
                    <div className="text-sm opacity-90">Adicionar fundos à carteira</div>
                  </div>
                </Button>

                <Button
                  onClick={() => setShowWithdrawModal(true)}
                  variant="outline"
                  className="flex items-center justify-center space-x-2 p-6 h-auto"
                  style={{borderColor: '#3569b0', color: '#3569b0'}}
                >
                  <ArrowUpCircle className="w-6 h-6" />
                  <div className="text-left">
                    <div className="font-semibold">Retirar Dinheiro</div>
                    <div className="text-sm opacity-75">Sacar para sua conta</div>
                  </div>
                </Button>
              </div>

              <Button
                onClick={() => navigate('/history')}
                variant="outline"
                className="w-full flex items-center justify-center space-x-2"
              >
                <History className="w-5 h-5" />
                <span>Ver Histórico Completo</span>
              </Button>
            </div>

            {/* Transações Recentes */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-6`}>
              <h2 className={`text-xl font-semibold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Transações Recentes
              </h2>

              <div className="space-y-4">
                {recentTransactions.map((transaction) => (
                  <div key={transaction.id} className={`flex items-center justify-between p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-full ${transaction.type === 'income' ? 'bg-green-100' : 'bg-red-100'}`}>
                        {transaction.type === 'income' ? (
                          <TrendingUp className="w-5 h-5 text-green-600" />
                        ) : (
                          <TrendingDown className="w-5 h-5 text-red-600" />
                        )}
                      </div>
                      <div>
                        <p className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {transaction.description}
                        </p>
                        <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          {transaction.method} • {new Date(transaction.date).toLocaleDateString('pt-AO')}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`font-bold ${transaction.amount > 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {transaction.amount > 0 ? '+' : ''}{formatCurrency(Math.abs(transaction.amount))}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar - Métodos de Pagamento */}
          <div className="lg:col-span-1">
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-6 sticky top-24`}>
              <h2 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Métodos de Pagamento
              </h2>

              <div className="space-y-3">
                <div className={`p-3 rounded-lg border ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
                  <div className="flex items-center space-x-3">
                    <Building2 className="w-5 h-5" style={{color: '#3569b0'}} />
                    <div>
                      <p className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Banco BAI</p>
                      <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>**** 1234</p>
                    </div>
                  </div>
                </div>

                <div className={`p-3 rounded-lg border ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
                  <div className="flex items-center space-x-3">
                    <Smartphone className="w-5 h-5" style={{color: '#3569b0'}} />
                    <div>
                      <p className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Multicaixa</p>
                      <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>+244 923 ***</p>
                    </div>
                  </div>
                </div>

                <Button
                  variant="outline"
                  className="w-full flex items-center justify-center space-x-2"
                  style={{borderColor: '#3569b0', color: '#3569b0'}}
                >
                  <CreditCard className="w-4 h-4" />
                  <span>Adicionar Método</span>
                </Button>
              </div>

              <div className={`mt-6 pt-6 border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                <Button
                  onClick={() => navigate('/settings')}
                  variant="ghost"
                  className="w-full"
                >
                  Configurar Contas
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modal de Depósito */}
      {showDepositModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 w-full max-w-md`}>
            <h3 className={`text-xl font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Depositar Dinheiro
            </h3>

            <div className="space-y-4">
              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Valor
                </label>
                <input
                  type="number"
                  value={depositAmount}
                  onChange={(e) => setDepositAmount(e.target.value)}
                  placeholder="0"
                  className={`w-full px-3 py-2 border rounded-lg ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Método de Pagamento
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      value="bank"
                      checked={selectedDepositMethod === 'bank'}
                      onChange={(e) => setSelectedDepositMethod(e.target.value)}
                      className="text-blue-600"
                    />
                    <Building2 className="w-5 h-5" />
                    <span>Transferência Bancária</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      value="mobile"
                      checked={selectedDepositMethod === 'mobile'}
                      onChange={(e) => setSelectedDepositMethod(e.target.value)}
                      className="text-blue-600"
                    />
                    <Smartphone className="w-5 h-5" />
                    <span>Pagamento Móvel</span>
                  </label>
                </div>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <Button
                onClick={() => setShowDepositModal(false)}
                variant="outline"
                className="flex-1"
              >
                Cancelar
              </Button>
              <Button
                onClick={handleDeposit}
                className="flex-1 text-white"
                style={{backgroundColor: '#3569b0'}}
                disabled={!depositAmount}
              >
                Depositar
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Saque */}
      {showWithdrawModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 w-full max-w-md`}>
            <h3 className={`text-xl font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Retirar Dinheiro
            </h3>

            <div className="space-y-4">
              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Valor
                </label>
                <input
                  type="number"
                  value={withdrawAmount}
                  onChange={(e) => setWithdrawAmount(e.target.value)}
                  placeholder="0"
                  max={userBalance}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
                <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Saldo disponível: {formatCurrency(userBalance)}
                </p>
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Método de Saque
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      value="bank"
                      checked={selectedWithdrawMethod === 'bank'}
                      onChange={(e) => setSelectedWithdrawMethod(e.target.value)}
                      className="text-blue-600"
                    />
                    <Building2 className="w-5 h-5" />
                    <span>Conta Bancária</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      value="mobile"
                      checked={selectedWithdrawMethod === 'mobile'}
                      onChange={(e) => setSelectedWithdrawMethod(e.target.value)}
                      className="text-blue-600"
                    />
                    <Smartphone className="w-5 h-5" />
                    <span>Carteira Móvel</span>
                  </label>
                </div>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <Button
                onClick={() => setShowWithdrawModal(false)}
                variant="outline"
                className="flex-1"
              >
                Cancelar
              </Button>
              <Button
                onClick={handleWithdraw}
                className="flex-1 text-white"
                style={{backgroundColor: '#3569b0'}}
                disabled={!withdrawAmount || Number(withdrawAmount) > userBalance}
              >
                Retirar
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Wallet;
