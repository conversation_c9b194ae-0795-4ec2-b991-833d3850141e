import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useEquipment } from '../contexts/EquipmentContext';
import { useAuth } from '../contexts/AuthContext';
import { Search, ChevronDown, ChevronRight, X, MapPin, Eye, List, Grid, Heart, Share2, ShoppingCart, Star } from 'lucide-react';
import { Button } from '../components/ui/button';
import MapModal from '../components/MapModal';
import RecentSearches from '../components/RecentSearches';
import apiService from '../services/api';

// Removido dados mockados - usando apenas dados da API

interface EquipmentCatalogProps {
  isDarkMode: boolean;
}

interface Equipment {
  id: string;
  title: string;
  description: string;
  classCode: string;
  price: {
    daily: number;
    weekly: number;
    monthly: number;
    annual?: number; // Preço anual opcional
  };
  image: string;
  category: string;
  subcategory: string;
  specifications: string[];
}

const EquipmentCatalog: React.FC<EquipmentCatalogProps> = ({ isDarkMode }) => {
  const navigate = useNavigate();
  const routerLocation = useLocation();
  const { isAuthenticated, user } = useAuth();
  const {
    equipment,
    loading,
    error,
    totalPages,
    currentPage,
    total,
    categories,
    favorites,
    fetchEquipment,
    toggleFavorite,
    isFavorite,
    isFavoriteLoading,
    fetchCategories
  } = useEquipment();

  const [searchTerm, setSearchTerm] = useState('');
  const [location, setLocation] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | null>(null);
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  const [showMobileFilters, setShowMobileFilters] = useState(false);

  // Estados para o modal do mapa
  const [isMapModalOpen, setIsMapModalOpen] = useState(false);
  const [selectedEquipmentForMap, setSelectedEquipmentForMap] = useState<any>(null);

  // Estado para controlar o filtro de preço
  const [priceFilter, setPriceFilter] = useState<string | null>(null);

  // Estado para controlar o filtro de período
  const [periodFilter, setPeriodFilter] = useState<'daily' | 'weekly' | 'monthly' | 'annual' | null>(null);

  // Estado para controlar a visualização rápida
  const [quickViewEquipmentId, setQuickViewEquipmentId] = useState<string | null>(null);

  // Estado para controlar o tipo de visualização (lista, grade, cartão)
  const [viewType, setViewType] = useState<'list' | 'grid' | 'card'>('list');

  // Estado para controlar a ordenação
  const [sortOrder, setSortOrder] = useState<'relevance' | 'price_asc' | 'price_desc' | 'newest'>('relevance');

  // Estados para as datas
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');

  // Estado para armazenar categorias do banco de dados
  const [dbCategories, setDbCategories] = useState<any[]>([]);

  // Carregar dados iniciais
  useEffect(() => {
    fetchEquipment();
    fetchCategories();
    loadDbCategories();
  }, []);

  // Carregar categorias do banco de dados
  const loadDbCategories = async () => {
    try {
      const response = await apiService.getCategories();
      setDbCategories(response || []);
    } catch (error) {
      console.error('Erro ao carregar categorias:', error);
    }
  };

  // Aplicar filtros quando mudarem (exceto localização, datas e filtros de categoria local)
  useEffect(() => {
    const filters: any = {};

    if (searchTerm) filters.search = searchTerm;
    // Não incluir selectedCategory aqui - será filtrado localmente
    if (priceFilter) {
      // Converter filtro de preço para valores numéricos
      switch (priceFilter) {
        case 'under100k':
          filters.maxPrice = 100000;
          break;
        case '100k-500k':
          filters.minPrice = 100000;
          filters.maxPrice = 500000;
          break;
        case '500k-1m':
          filters.minPrice = 500000;
          filters.maxPrice = 1000000;
          break;
        case 'above1m':
          filters.minPrice = 1000000;
          break;
      }
    }

    // Adicionar ordenação
    if (sortOrder) {
      filters.sortBy = sortOrder;
    }

    fetchEquipment(filters);
  }, [searchTerm, priceFilter, sortOrder]);

  // Pesquisa por localização em tempo real
  useEffect(() => {
    if (!location) {
      // Se localização estiver vazia, aplicar filtros normais
      const filters: any = {};
      if (searchTerm) filters.search = searchTerm;
      // Não incluir selectedCategory - será filtrado localmente
      if (priceFilter) {
        switch (priceFilter) {
          case 'under100k':
            filters.maxPrice = 100000;
            break;
          case '100k-500k':
            filters.minPrice = 100000;
            filters.maxPrice = 500000;
            break;
          case '500k-1m':
            filters.minPrice = 500000;
            filters.maxPrice = 1000000;
            break;
          case 'above1m':
            filters.minPrice = 1000000;
            break;
        }
      }
      if (sortOrder) filters.sortBy = sortOrder;
      fetchEquipment(filters);
      return;
    }

    // Debounce para pesquisa por localização
    const timeoutId = setTimeout(() => {
      const filters: any = {};
      if (searchTerm) filters.search = searchTerm;
      if (location) filters.location = location;
      // Não incluir selectedCategory - será filtrado localmente
      if (priceFilter) {
        switch (priceFilter) {
          case 'under100k':
            filters.maxPrice = 100000;
            break;
          case '100k-500k':
            filters.minPrice = 100000;
            filters.maxPrice = 500000;
            break;
          case '500k-1m':
            filters.minPrice = 500000;
            filters.maxPrice = 1000000;
            break;
          case 'above1m':
            filters.minPrice = 1000000;
            break;
        }
      }
      if (sortOrder) filters.sortBy = sortOrder;
      fetchEquipment(filters);
    }, 500); // 500ms de debounce

    return () => clearTimeout(timeoutId);
  }, [location]);

  // Obter a data atual formatada para o input date (YYYY-MM-DD)
  const today = new Date().toISOString().split('T')[0];

  // Função para validar a data de início
  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newStartDate = e.target.value;
    setStartDate(newStartDate);

    // Se a data de fim for anterior à nova data de início, atualizar a data de fim
    if (endDate && endDate < newStartDate) {
      setEndDate(newStartDate);
    }
  };

  // Função para validar a data de fim
  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newEndDate = e.target.value;

    // Só permitir datas de fim que sejam iguais ou posteriores à data de início
    if (!startDate || newEndDate >= startDate) {
      setEndDate(newEndDate);
    }
  };

  // Parse query parameters
  useEffect(() => {
    const params = new URLSearchParams(routerLocation.search);
    const categoryParam = params.get('category');
    const categoryIdParam = params.get('categoryId');
    const searchParam = params.get('search');
    const locationParam = params.get('location');

    // Priorizar categoryId sobre category
    const selectedCategoryParam = categoryIdParam || categoryParam;

    if (selectedCategoryParam) {
      setSelectedCategory(selectedCategoryParam);
      toggleCategory(selectedCategoryParam);
    }
    if (searchParam) {
      setSearchTerm(searchParam);
    }
    if (locationParam) {
      setLocation(locationParam);
    }
  }, [routerLocation.search]);

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  // Transformar equipamentos do contexto para o formato esperado
  const transformedEquipments = equipment.map(equip => ({
    id: equip.id,
    title: equip.name,
    description: equip.description || '',
    classCode: `EQ-${equip.id.slice(0, 8)}`,
    price: {
      daily: equip.pricePeriod === 'DAILY' ? equip.price : Math.round((equip.price || 0) * 0.1),
      weekly: equip.pricePeriod === 'WEEKLY' ? equip.price : Math.round((equip.price || 0) * 0.5),
      monthly: equip.pricePeriod === 'MONTHLY' ? equip.price : Math.round((equip.price || 0) * 2),
      annual: Math.round((equip.price || 0) * 20)
    },
    image: equip.images?.[0] || 'https://picsum.photos/600/400?random=9',
    category: equip.category?.toLowerCase().replace(/\s+/g, '-') || 'outros',
    subcategory: `${equip.category?.toLowerCase().replace(/\s+/g, '-') || 'outros'}-sub1`,
    specifications: equip.specifications ?
      Object.entries(equip.specifications).map(([key, value]) => `${key}: ${value}`) :
      ['Especificações não disponíveis'],
    owner: equip.owner,
    isAvailable: equip.isAvailable,
    categoryId: equip.categoryId,
    rating: equip.reviews?.length ?
      equip.reviews.reduce((acc, review) => acc + review.rating, 0) / equip.reviews.length :
      4.5, // Rating padrão
    reviewCount: equip.reviews?.length || 0
  }));

  // Transformar categorias do banco de dados para o formato esperado pelo componente
  const categoriesFormatted = dbCategories
    .filter(cat => cat.isActive)
    .map(cat => {
      // Contar equipamentos desta categoria nos dados atuais
      const categoryEquipments = transformedEquipments.filter(eq =>
        eq.categoryId === cat.id || eq.category === cat.name.toLowerCase().replace(/\s+/g, '-')
      );

      const totalCount = categoryEquipments.length;
      const availableCount = categoryEquipments.filter(eq => eq.isAvailable).length;
      const unavailableCount = categoryEquipments.filter(eq => !eq.isAvailable).length;

      // Criar subcategorias baseadas nos equipamentos atuais
      const subcategories = [
        { id: `${cat.id}-disponivel`, name: `Disponível (${availableCount})` },
        { id: `${cat.id}-indisponivel`, name: `Indisponível (${unavailableCount})` },
        { id: `${cat.id}-todos`, name: `Todos (${totalCount})` }
      ];

      return {
        id: cat.id,
        name: `${cat.name} (${totalCount})`,
        subcategories,
        count: totalCount
      };
    });

  // Aplicar filtros locais baseados na categoria e subcategoria selecionadas
  const filteredEquipments = transformedEquipments.filter(equip => {
    // Filtro por categoria
    if (selectedCategory) {
      const matchesCategory = equip.categoryId === selectedCategory ||
                             equip.category === selectedCategory.toLowerCase().replace(/\s+/g, '-');
      if (!matchesCategory) return false;
    }

    // Filtro por subcategoria (disponibilidade)
    if (selectedSubcategory) {
      if (selectedSubcategory.includes('-disponivel')) {
        return equip.isAvailable === true;
      } else if (selectedSubcategory.includes('-indisponivel')) {
        return equip.isAvailable === false;
      }
      // Para 'todos', não aplicar filtro adicional
    }

    return true;
  });

  // Formatar preço
  const formatPrice = (price: number) => {
    return price.toLocaleString('pt-AO') + ' Kz';
  };

  // Função para calcular rating real baseado nas avaliações
  const calculateRating = (reviews: any[]) => {
    if (!reviews || reviews.length === 0) return 0;
    const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
    return sum / reviews.length;
  };

  // Função para renderizar estrelas
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />);
    }

    if (hasHalfStar) {
      stars.push(<Star key="half" className="w-4 h-4 text-yellow-400 fill-current opacity-50" />);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<Star key={`empty-${i}`} className="w-4 h-4 text-gray-300" />);
    }

    return stars;
  };

  // Função para calcular distância real (quando implementado GPS)
  const calculateDistance = (equipmentLocation: any) => {
    // TODO: Implementar cálculo real de distância baseado na localização do usuário
    return 'N/A';
  };

  // Função para adicionar ao carrinho
  const handleAddToCart = (equipment: any) => {
    // Verificar se o usuário está logado
    const token = localStorage.getItem('authToken');
    if (!token) {
      navigate('/login');
      return;
    }

    // Verificar se o usuário é um locador (locadores não podem adicionar ao carrinho)
    if (user?.userType === 'LANDLORD') {
      alert('Locadores não podem adicionar equipamentos ao carrinho. Apenas locatários podem alugar.');
      return;
    }

    // Adicionar ao carrinho local (localStorage)
    const cart = JSON.parse(localStorage.getItem('cart') || '[]');
    const existingItem = cart.find((item: any) => item.id === equipment.id);

    if (existingItem) {
      // Se já existe, aumentar quantidade
      existingItem.quantity += 1;
    } else {
      // Adicionar novo item
      cart.push({
        id: equipment.id,
        name: equipment.title,
        price: equipment.price.daily,
        image: equipment.image,
        quantity: 1,
        period: 'daily',
        startDate: startDate || '',
        endDate: endDate || ''
      });
    }

    localStorage.setItem('cart', JSON.stringify(cart));

    // Mostrar feedback visual (toast)
    alert(`${equipment.title} adicionado ao carrinho!`);
  };

  // Função para abrir mapa
  const handleOpenMap = (equipment: any) => {
    setSelectedEquipmentForMap(equipment);
    setIsMapModalOpen(true);
  };

  // Função para favoritar equipamento (protegida)
  const handleToggleFavorite = (equipmentId: string) => {
    if (!isAuthenticated) {
      navigate('/auth');
      return;
    }
    toggleFavorite(equipmentId);
  };

  // Função para compartilhar equipamento (protegida)
  const handleShare = (equipment: any) => {
    if (!isAuthenticated) {
      navigate('/auth');
      return;
    }

    if (navigator.share) {
      navigator.share({
        title: equipment.title,
        text: equipment.description,
        url: `${window.location.origin}/equipment/${equipment.id}`
      });
    } else {
      // Fallback para copiar URL
      navigator.clipboard.writeText(`${window.location.origin}/equipment/${equipment.id}`);
      alert('Link copiado para a área de transferência!');
    }
  };

  // Função para aplicar filtros (incluindo datas e localização)
  const handleApplyFilters = () => {
    const filters: any = {};

    if (searchTerm) filters.search = searchTerm;
    if (location) filters.location = location;
    // Incluir categoria apenas quando aplicar filtros via botão
    if (selectedCategory) filters.categoryId = selectedCategory;
    if (priceFilter) {
      switch (priceFilter) {
        case 'under100k':
          filters.maxPrice = 100000;
          break;
        case '100k-500k':
          filters.minPrice = 100000;
          filters.maxPrice = 500000;
          break;
        case '500k-1m':
          filters.minPrice = 500000;
          filters.maxPrice = 1000000;
          break;
        case 'above1m':
          filters.minPrice = 1000000;
          break;
      }
    }

    // Aplicar filtros de data apenas quando o botão for clicado
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;

    if (sortOrder) filters.sortBy = sortOrder;

    // Salvar pesquisa recente
    saveRecentSearch();

    // Limpar filtros locais de categoria/subcategoria ao aplicar filtros via botão
    setSelectedCategory(null);
    setSelectedSubcategory(null);

    fetchEquipment(filters);
  };

  // Função para salvar pesquisa recente
  const saveRecentSearch = () => {
    if (!searchTerm && !location && !selectedCategory) return;

    const recentSearches = JSON.parse(localStorage.getItem('recentSearches') || '[]');
    const newSearch = {
      id: Date.now().toString(),
      searchTerm,
      location,
      category: selectedCategory,
      timestamp: new Date().toISOString()
    };

    // Evitar duplicatas
    const filteredSearches = recentSearches.filter((search: any) =>
      !(search.searchTerm === searchTerm && search.location === location && search.category === selectedCategory)
    );

    // Adicionar nova pesquisa no início e manter apenas as 10 mais recentes
    const updatedSearches = [newSearch, ...filteredSearches].slice(0, 10);
    localStorage.setItem('recentSearches', JSON.stringify(updatedSearches));
  };

  // Função para aplicar pesquisa recente selecionada
  const handleRecentSearchSelect = (search: any) => {
    setSearchTerm(search.searchTerm || '');
    setLocation(search.location || '');
    setSelectedCategory(search.category || null);

    if (search.category) {
      toggleCategory(search.category);
    }

    // Aplicar filtros automaticamente
    setTimeout(() => {
      handleApplyFilters();
    }, 100);
  };

  // Função para limpar filtro de categoria
  const clearCategoryFilter = () => {
    setSelectedCategory(null);
    setSelectedSubcategory(null);
  };

  return (
    <div className={`min-h-screen pt-16 ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Barra de pesquisa e localização */}
      <div className={`w-full ${isDarkMode ? 'bg-gray-800' : 'bg-gray-100'} py-4`}>
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-12 gap-3">
            {/* Campo de pesquisa */}
            <div className="col-span-1 sm:col-span-1 lg:col-span-3">
              <div className={`flex items-center rounded-md overflow-hidden shadow-sm ${
                isDarkMode ? 'bg-gray-700' : 'bg-white'
              }`}>
                <div className={`p-3 border-r ${
                  isDarkMode ? 'bg-gray-600 border-gray-500' : 'bg-gray-50 border-gray-200'
                }`}>
                  <Search className={`w-5 h-5 ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`} />
                </div>
                <input
                  type="text"
                  placeholder="Pesquisar equipamentos..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full p-3 outline-none ${
                    isDarkMode
                      ? 'bg-gray-700 text-white placeholder-gray-400'
                      : 'bg-white text-gray-900 placeholder-gray-500'
                  }`}
                />
              </div>
            </div>

            {/* Campo de localização */}
            <div className="col-span-1 sm:col-span-1 lg:col-span-3">
              <div className={`flex items-center rounded-md overflow-hidden shadow-sm ${
                isDarkMode ? 'bg-gray-700' : 'bg-white'
              }`}>
                <div className={`p-3 border-r ${
                  isDarkMode ? 'bg-gray-600 border-gray-500' : 'bg-gray-50 border-gray-200'
                }`}>
                  <MapPin className={`w-5 h-5 ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`} />
                </div>
                <input
                  type="text"
                  placeholder="Localização"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  className={`w-full p-3 outline-none ${
                    isDarkMode
                      ? 'bg-gray-700 text-white placeholder-gray-400'
                      : 'bg-white text-gray-900 placeholder-gray-500'
                  }`}
                />
              </div>
            </div>

            {/* Datas */}
            <div className="col-span-1 sm:col-span-1 lg:col-span-2">
              <div className={`flex items-center rounded-md overflow-hidden shadow-sm ${
                isDarkMode ? 'bg-gray-700' : 'bg-white'
              }`}>
                <div className={`p-3 border-r ${
                  isDarkMode ? 'bg-gray-600 border-gray-500' : 'bg-gray-50 border-gray-200'
                }`}>
                  <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>Início</span>
                </div>
                <input
                  type="date"
                  min={today}
                  value={startDate}
                  onChange={handleStartDateChange}
                  className={`w-full p-3 outline-none ${
                    isDarkMode
                      ? 'bg-gray-700 text-white'
                      : 'bg-white text-gray-900'
                  }`}
                />
              </div>
            </div>
            <div className="col-span-1 sm:col-span-1 lg:col-span-2">
              <div className={`flex items-center rounded-md overflow-hidden shadow-sm ${
                isDarkMode ? 'bg-gray-700' : 'bg-white'
              }`}>
                <div className={`p-3 border-r ${
                  isDarkMode ? 'bg-gray-600 border-gray-500' : 'bg-gray-50 border-gray-200'
                }`}>
                  <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>Fim</span>
                </div>
                <input
                  type="date"
                  min={startDate || today}
                  value={endDate}
                  onChange={handleEndDateChange}
                  className={`w-full p-3 outline-none ${
                    isDarkMode
                      ? 'bg-gray-700 text-white'
                      : 'bg-white text-gray-900'
                  }`}
                />
              </div>
            </div>

            {/* Entrega */}
            <div className="col-span-1 sm:col-span-1 lg:col-span-1">
              <div className={`h-full items-center rounded-md overflow-hidden shadow-sm ${
                isDarkMode ? 'bg-gray-700' : 'bg-white'
              }`}>
                <label className="flex items-center h-full p-3 cursor-pointer">
                  <input type="checkbox" className="mr-2" />
                  <span className={`text-sm ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Entrega</span>
                </label>
              </div>
            </div>

            {/* Botões aplicar e filtros */}
            <div className="col-span-1 sm:col-span-1 lg:col-span-1 flex gap-2">
              <Button
                variant={showMobileFilters ? "default" : "outline"}
                onClick={() => setShowMobileFilters(!showMobileFilters)}
                className="md:hidden h-full flex-1 flex items-center justify-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
              </Button>
              <Button
                onClick={handleApplyFilters}
                className="w-full h-full text-white px-6 py-3"
                style={{backgroundColor: '#3569b0'}}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2a5490'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3569b0'}
              >
                Aplicar
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-4">


        {/* Título principal */}
        <div className="mb-6 text-center">
          <h1 className="text-3xl font-bold mb-2">
            {selectedSubcategory
              ? categoriesFormatted.find(c => c.id === selectedCategory)?.subcategories.find(s => s.id === selectedSubcategory)?.name
              : selectedCategory
                ? categoriesFormatted.find(c => c.id === selectedCategory)?.name
                : 'Todos os Equipamentos'}
          </h1>
          <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'} text-lg mb-4 max-w-3xl mx-auto hidden md:block`}>
            {selectedCategory === 'power' && selectedSubcategory === 'lighting' ?
              'A YMRentals oferece uma seleção completa de torres de iluminação portáteis para aluguel, perfeitas para sua próxima construção ou projeto de infraestrutura.' :
              'Encontre os melhores equipamentos para seu projeto. Oferecemos uma ampla seleção de equipamentos de alta qualidade para aluguel.'}
          </p>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            <p>{error}</p>
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && filteredEquipments.length === 0 && (
          <div className="text-center py-20">
            <div className={`text-lg font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
              Nenhum equipamento encontrado
            </div>
            <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
              {selectedCategory
                ? 'Não há equipamentos disponíveis nesta categoria no momento.'
                : 'Ainda não temos equipamentos cadastrados. Volte mais tarde!'}
            </p>
            {selectedCategory && (
              <Button
                onClick={() => {
                  setSelectedCategory(null);
                  setSelectedSubcategory(null);
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                Ver Todos os Equipamentos
              </Button>
            )}
          </div>
        )}

        {/* Resultados mobile */}
        {!loading && !error && (
          <div className="flex md:hidden justify-center items-center mb-6">
            <span className="text-sm">Resultados: <strong>{filteredEquipments.length}</strong> equipamentos</span>
          </div>
        )}

        <div className="flex flex-col md:flex-row gap-6">
          {/* Filtros (Desktop) */}
          <div className={`hidden md:block w-64 flex-shrink-0 ${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md overflow-hidden`}>
            {/* Cabeçalho da categoria */}
            <div className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'} p-4 border-b ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
              <div className="flex justify-between items-center">
                <h2 className="font-semibold">Categoria</h2>
                {(selectedCategory || selectedSubcategory) && (
                  <button
                    onClick={clearCategoryFilter}
                    className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    Limpar
                  </button>
                )}
              </div>
            </div>

            <div className="p-4">
              <ul className="space-y-2">
                {categoriesFormatted.map(category => (
                  <li key={category.id} className={`${isDarkMode ? 'border-gray-700' : 'border-gray-200'} ${selectedCategory === category.id ? 'border-l-4 border-l-blue-600 pl-2 -ml-3' : ''}`}>
                    <div className="flex items-center justify-between">
                      <button
                        className={`text-left flex-1 ${selectedCategory === category.id
                            ? isDarkMode ? 'text-blue-400 font-medium' : 'text-blue-600 font-medium'
                            : ''}`}
                        onClick={() => {
                          const newCategory = selectedCategory === category.id ? null : category.id;
                          setSelectedCategory(newCategory);
                          setSelectedSubcategory(null);
                          toggleCategory(category.id);
                          // Não fazer busca na API - apenas filtrar localmente
                        }}
                      >
                        {category.name}
                      </button>
                      <button
                        onClick={() => toggleCategory(category.id)}
                        className={`${isDarkMode ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700'}`}
                      >
                        {expandedCategories[category.id] ? (
                          <ChevronDown className="w-4 h-4" />
                        ) : (
                          <ChevronRight className="w-4 h-4" />
                        )}
                      </button>
                    </div>

                    {expandedCategories[category.id] && (
                      <ul className="ml-4 mt-2 space-y-1">
                        {category.subcategories.map(subcategory => (
                          <li key={subcategory.id}>
                            <button
                              className={`text-left w-full py-1 px-2 rounded-md ${selectedSubcategory === subcategory.id
                                  ? isDarkMode ? 'bg-blue-900/30 text-blue-400' : 'bg-blue-50 text-blue-600'
                                  : isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
                              onClick={() => {
                                setSelectedCategory(category.id);
                                setSelectedSubcategory(
                                  selectedSubcategory === subcategory.id ? null : subcategory.id
                                );
                                // Não fazer busca na API - apenas filtrar localmente
                              }}
                            >
                              {subcategory.name}
                            </button>
                          </li>
                        ))}
                      </ul>
                    )}
                  </li>
                ))}
              </ul>
            </div>

            {/* Filtros adicionais */}
            <div className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'} p-4 border-b ${isDarkMode ? 'border-gray-600' : 'border-gray-200'} mt-4`}>
              <h2 className="font-semibold">Filtros</h2>
            </div>

            <div className="p-4">
              <div className="mb-4">
                <h3 className="text-sm font-medium mb-2">Preço</h3>
                <div className="space-y-1">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="priceFilter"
                      value="under100k"
                      checked={priceFilter === 'under100k'}
                      onChange={() => setPriceFilter('under100k')}
                      className="mr-2"
                    />
                    <span className="text-sm">Até 100.000 Kz</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="priceFilter"
                      value="100k-500k"
                      checked={priceFilter === '100k-500k'}
                      onChange={() => setPriceFilter('100k-500k')}
                      className="mr-2"
                    />
                    <span className="text-sm">100.000 - 500.000 Kz</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="priceFilter"
                      value="500k-1m"
                      checked={priceFilter === '500k-1m'}
                      onChange={() => setPriceFilter('500k-1m')}
                      className="mr-2"
                    />
                    <span className="text-sm">500.000 - 1.000.000 Kz</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="priceFilter"
                      value="above1m"
                      checked={priceFilter === 'above1m'}
                      onChange={() => setPriceFilter('above1m')}
                      className="mr-2"
                    />
                    <span className="text-sm">Acima de 1.000.000 Kz</span>
                  </label>
                  {priceFilter && (
                    <button
                      onClick={() => setPriceFilter(null)}
                      className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mt-1"
                    >
                      Limpar filtro
                    </button>
                  )}
                </div>
              </div>

              <div className="mb-4">
                <h3 className="text-sm font-medium mb-2">Período</h3>
                <div className="space-y-1">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="periodFilter"
                      value="daily"
                      checked={periodFilter === 'daily'}
                      onChange={() => setPeriodFilter('daily')}
                      className="mr-2"
                    />
                    <span className="text-sm">Diário</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="periodFilter"
                      value="weekly"
                      checked={periodFilter === 'weekly'}
                      onChange={() => setPeriodFilter('weekly')}
                      className="mr-2"
                    />
                    <span className="text-sm">Semanal</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="periodFilter"
                      value="monthly"
                      checked={periodFilter === 'monthly'}
                      onChange={() => setPeriodFilter('monthly')}
                      className="mr-2"
                    />
                    <span className="text-sm">Mensal</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="periodFilter"
                      value="annual"
                      checked={periodFilter === 'annual'}
                      onChange={() => setPeriodFilter('annual')}
                      className="mr-2"
                    />
                    <span className="text-sm">Anual</span>
                  </label>
                  {periodFilter && (
                    <button
                      onClick={() => setPeriodFilter(null)}
                      className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mt-1"
                    >
                      Limpar filtro
                    </button>
                  )}
                </div>
              </div>

              <div className="mb-4">
                <h3 className="text-sm font-medium mb-2">Disponibilidade</h3>
                <div className="space-y-1">
                  <label className="flex items-center">
                    <input type="checkbox" className="mr-2" />
                    <span className="text-sm">Disponível para entrega</span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="mr-2" />
                    <span className="text-sm">Disponível para retirada</span>
                  </label>
                </div>
              </div>

              <div className="border-t my-4 pt-4">
                <RecentSearches
                  isDarkMode={isDarkMode}
                  onSearchSelect={handleRecentSearchSelect}
                />
              </div>
            </div>
          </div>

          {/* Filtros (Mobile) */}
          {showMobileFilters && (
            <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex md:hidden">
              <div className={`w-4/5 h-full ${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-4 overflow-y-auto`}>
                <div className="flex justify-between items-center mb-4">
                  <h2 className="font-semibold">Filtros</h2>
                  <button onClick={() => setShowMobileFilters(false)}>
                    <X className="w-5 h-5" />
                  </button>
                </div>

                {/* Pesquisa mobile */}
                <div className="mb-4">
                  <h3 className="font-semibold mb-2">Pesquisar</h3>
                  <div className="flex items-center bg-white rounded-md overflow-hidden shadow-sm mb-2">
                    <div className="p-2 bg-gray-50 border-r">
                      <Search className="w-4 h-4 text-gray-500" />
                    </div>
                    <input
                      type="text"
                      placeholder="Pesquisar equipamentos..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full p-2 outline-none text-sm"
                    />
                  </div>
                </div>

                {/* Localização mobile */}
                <div className="mb-4">
                  <h3 className="font-semibold mb-2">Localização</h3>
                  <div className="flex items-center bg-white rounded-md overflow-hidden shadow-sm mb-2">
                    <div className="p-2 bg-gray-50 border-r">
                      <MapPin className="w-4 h-4 text-gray-500" />
                    </div>
                    <input
                      type="text"
                      placeholder="Localização"
                      value={location}
                      onChange={(e) => setLocation(e.target.value)}
                      className="w-full p-2 outline-none text-sm"
                    />
                  </div>
                </div>

                {/* Datas mobile */}
                <div className="mb-4">
                  <h3 className="font-semibold mb-2">Período</h3>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="flex items-center bg-white rounded-md overflow-hidden shadow-sm">
                      <div className="p-2 bg-gray-50 border-r">
                        <span className="text-xs font-medium text-gray-500">Início</span>
                      </div>
                      <input
                        type="date"
                        min={today}
                        value={startDate}
                        onChange={handleStartDateChange}
                        className="w-full p-2 outline-none text-sm"
                      />
                    </div>
                    <div className="flex items-center bg-white rounded-md overflow-hidden shadow-sm">
                      <div className="p-2 bg-gray-50 border-r">
                        <span className="text-xs font-medium text-gray-500">Fim</span>
                      </div>
                      <input
                        type="date"
                        min={startDate || today}
                        value={endDate}
                        onChange={handleEndDateChange}
                        className="w-full p-2 outline-none text-sm"
                      />
                    </div>
                  </div>
                </div>

                {/* Entrega mobile */}
                <div className="mb-4">
                  <label className="flex items-center p-2 bg-white rounded-md shadow-sm cursor-pointer">
                    <input type="checkbox" className="mr-2" />
                    <span className="text-sm">Entrega disponível</span>
                  </label>
                </div>

                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-semibold">Categorias</h3>
                  {(selectedCategory || selectedSubcategory) && (
                    <button
                      onClick={clearCategoryFilter}
                      className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      Limpar
                    </button>
                  )}
                </div>
                <ul className="space-y-2 mb-4">
                  {categoriesFormatted.map(category => (
                    <li key={category.id}>
                      <div className="flex items-center justify-between">
                        <button
                          className={`text-left flex-1 ${
                            selectedCategory === category.id
                              ? isDarkMode ? 'text-blue-400 font-medium' : 'text-blue-600 font-medium'
                              : ''
                          }`}
                          onClick={() => {
                            const newCategory = selectedCategory === category.id ? null : category.id;
                            setSelectedCategory(newCategory);
                            setSelectedSubcategory(null);
                            toggleCategory(category.id);
                            // Não fazer busca na API - apenas filtrar localmente
                          }}
                        >
                          {category.name}
                        </button>
                        <button
                          onClick={() => toggleCategory(category.id)}
                          className={`${isDarkMode ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700'}`}
                        >
                          {expandedCategories[category.id] ? (
                            <ChevronDown className="w-4 h-4" />
                          ) : (
                            <ChevronRight className="w-4 h-4" />
                          )}
                        </button>
                      </div>

                      {expandedCategories[category.id] && (
                        <ul className="ml-4 mt-2 space-y-1">
                          {category.subcategories.map(subcategory => (
                            <li key={subcategory.id}>
                              <button
                                className={`text-left w-full py-1 px-2 rounded-md ${
                                  selectedSubcategory === subcategory.id
                                    ? isDarkMode ? 'bg-blue-900/30 text-blue-400' : 'bg-blue-50 text-blue-600'
                                    : isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                                }`}
                                onClick={() => {
                                  setSelectedCategory(category.id);
                                  setSelectedSubcategory(
                                    selectedSubcategory === subcategory.id ? null : subcategory.id
                                  );
                                  // Não fazer busca na API - apenas filtrar localmente
                                }}
                              >
                                {subcategory.name}
                              </button>
                            </li>
                          ))}
                        </ul>
                      )}
                    </li>
                  ))}
                </ul>

                {/* Preço mobile */}
                <div className="mb-4">
                  <h3 className="font-semibold mb-2">Preço</h3>
                  <div className="space-y-1">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mobilePriceFilter"
                        value="under100k"
                        checked={priceFilter === 'under100k'}
                        onChange={() => setPriceFilter('under100k')}
                        className="mr-2"
                      />
                      <span className="text-sm">Até 100.000 Kz</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mobilePriceFilter"
                        value="100k-500k"
                        checked={priceFilter === '100k-500k'}
                        onChange={() => setPriceFilter('100k-500k')}
                        className="mr-2"
                      />
                      <span className="text-sm">100.000 - 500.000 Kz</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mobilePriceFilter"
                        value="500k-1m"
                        checked={priceFilter === '500k-1m'}
                        onChange={() => setPriceFilter('500k-1m')}
                        className="mr-2"
                      />
                      <span className="text-sm">500.000 - 1.000.000 Kz</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mobilePriceFilter"
                        value="above1m"
                        checked={priceFilter === 'above1m'}
                        onChange={() => setPriceFilter('above1m')}
                        className="mr-2"
                      />
                      <span className="text-sm">Acima de 1.000.000 Kz</span>
                    </label>
                    {priceFilter && (
                      <button
                        onClick={() => setPriceFilter(null)}
                        className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mt-1"
                      >
                        Limpar filtro
                      </button>
                    )}
                  </div>
                </div>

                {/* Período mobile */}
                <div className="mb-4">
                  <h3 className="font-semibold mb-2">Período</h3>
                  <div className="space-y-1">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mobilePeriodFilter"
                        value="daily"
                        checked={periodFilter === 'daily'}
                        onChange={() => setPeriodFilter('daily')}
                        className="mr-2"
                      />
                      <span className="text-sm">Diário</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mobilePeriodFilter"
                        value="weekly"
                        checked={periodFilter === 'weekly'}
                        onChange={() => setPeriodFilter('weekly')}
                        className="mr-2"
                      />
                      <span className="text-sm">Semanal</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mobilePeriodFilter"
                        value="monthly"
                        checked={periodFilter === 'monthly'}
                        onChange={() => setPeriodFilter('monthly')}
                        className="mr-2"
                      />
                      <span className="text-sm">Mensal</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="mobilePeriodFilter"
                        value="annual"
                        checked={periodFilter === 'annual'}
                        onChange={() => setPeriodFilter('annual')}
                        className="mr-2"
                      />
                      <span className="text-sm">Anual</span>
                    </label>
                    {periodFilter && (
                      <button
                        onClick={() => setPeriodFilter(null)}
                        className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mt-1"
                      >
                        Limpar filtro
                      </button>
                    )}
                  </div>
                </div>

                <div className="mt-auto pt-4 border-t">
                  <Button
                    className="w-full bg-blue-600 hover:bg-blue-700"
                    onClick={() => setShowMobileFilters(false)}
                  >
                    Aplicar Filtros
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Lista de Equipamentos */}
          <div className="flex-1">
            {filteredEquipments.length > 0 ? (
              <div>
                {/* Opções de ordenação e visualização */}
                <div className={`flex flex-wrap justify-between mb-4 p-4 ${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md`}>
                  {/* Resultados - apenas desktop */}
                  <div className="hidden md:flex items-center">
                    <span className="text-sm">Resultados: <strong>{filteredEquipments.length}</strong> equipamentos</span>
                  </div>

                  {/* Opções de visualização - apenas mobile */}
                  <div className="flex md:hidden flex-col mr-auto items-center">
                    <span className="text-sm mb-1 font-medium">Visualizar</span>
                    <div className="flex space-x-1">
                      <button
                        className={`p-1 rounded flex items-center ${viewType === 'list' ? (isDarkMode ? 'bg-blue-900 text-white' : 'bg-blue-100 text-blue-800') : (isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')}`}
                        onClick={() => setViewType('list')}
                        title="Visualização em Lista"
                      >
                        <List className="h-4 w-4" />
                      </button>
                      <button
                        className={`p-1 rounded flex items-center ${viewType === 'grid' ? (isDarkMode ? 'bg-blue-900 text-white' : 'bg-blue-100 text-blue-800') : (isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')}`}
                        onClick={() => setViewType('grid')}
                        title="Visualização em Grade"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                        </svg>
                      </button>
                      <button
                        className={`p-1 rounded flex items-center ${viewType === 'card' ? (isDarkMode ? 'bg-blue-900 text-white' : 'bg-blue-100 text-blue-800') : (isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')}`}
                        onClick={() => setViewType('card')}
                        title="Visualização em Cartões"
                      >
                        <Grid className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  {/* Opções de visualização - apenas desktop */}
                  <div className="hidden md:flex items-center space-x-1">
                    <span className="text-sm mr-2">Visualizar:</span>
                    <button
                      className={`p-1.5 px-3 rounded flex items-center ${viewType === 'list' ? (isDarkMode ? 'bg-blue-900 text-white' : 'bg-blue-100 text-blue-800') : (isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')}`}
                      onClick={() => setViewType('list')}
                      title="Visualização em Lista"
                    >
                      <List className="h-4 w-4 mr-1" />
                      <span className="text-xs">Lista</span>
                    </button>
                    <button
                      className={`p-1.5 px-3 rounded flex items-center ${viewType === 'grid' ? (isDarkMode ? 'bg-blue-900 text-white' : 'bg-blue-100 text-blue-800') : (isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')}`}
                      onClick={() => setViewType('grid')}
                      title="Visualização em Grade"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                      </svg>
                      <span className="text-xs">Cartões</span>
                    </button>
                    <button
                      className={`p-1.5 px-3 rounded flex items-center ${viewType === 'card' ? (isDarkMode ? 'bg-blue-900 text-white' : 'bg-blue-100 text-blue-800') : (isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')}`}
                      onClick={() => setViewType('card')}
                      title="Visualização em Cartões"
                    >
                      <Grid className="h-4 w-4 mr-1" />
                      <span className="text-xs">Grade</span>
                    </button>
                  </div>

                  <div className="flex items-center">
                    <span className="text-sm mr-2 hidden md:inline">Ordenar por:</span>
                    <div className="flex flex-col items-center md:block">
                      <span className="text-sm mb-1 font-medium md:hidden">Ordenar por</span>
                      <select
                      className={`text-sm p-1.5 rounded border ${isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'}`}
                      value={sortOrder}
                      onChange={(e) => setSortOrder(e.target.value as 'relevance' | 'price_asc' | 'price_desc' | 'newest')}
                    >
                      <option value="relevance">Relevância</option>
                      <option value="price_asc">Preço: Menor para Maior</option>
                      <option value="price_desc">Preço: Maior para Menor</option>
                      <option value="newest">Mais Recentes</option>
                    </select>
                    </div>
                  </div>
                </div>

                {/* Resultados */}
                {viewType === 'list' && (
                  <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md overflow-hidden`}>
                    {filteredEquipments.map((item, index) => (
                      <div
                        key={item.id}
                        className={`p-4 ${index !== filteredEquipments.length - 1 ? `border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}` : ''}`}
                      >
                        <div className="flex flex-col">
                          {/* Imagem e título */}
                          <div className="flex flex-row mb-3">
                            <div className="w-1/3 mr-3 relative">
                              <img
                                src={item.image}
                                alt={item.title}
                                className="w-full h-24 sm:h-32 md:h-40 object-cover rounded-md"
                              />
                              <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                                Disponível
                              </div>

                              {/* Botões abaixo da imagem - apenas mobile */}
                              <div className="grid grid-cols-2 gap-1 w-full mt-2 md:hidden">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleOpenMap(item)}
                                  className="text-xs p-1 h-8 flex-1 flex justify-center items-center md:w-24"
                                >
                                  <MapPin className="w-3 h-3 mr-1" />
                                  <span>Mapa</span>
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setQuickViewEquipmentId(quickViewEquipmentId === item.id ? null : item.id)}
                                  className="text-xs p-1 h-8 flex-1 flex justify-center items-center md:w-24"
                                >
                                  <Eye className="w-3 h-3 mr-1" />
                                  <span>Ver</span>
                                </Button>
                              </div>
                            </div>
                            <div className="w-2/3">
                              <div className="flex items-center mb-1">
                                <h2 className="text-lg font-semibold truncate">{item.title}</h2>
                                <div className="flex ml-2 space-x-1">
                                  <button
                                    className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                                    onClick={() => handleToggleFavorite(item.id)}
                                    disabled={isFavoriteLoading(item.id)}
                                  >
                                    {isFavoriteLoading(item.id) ? (
                                      <div className="w-4 h-4 border-2 border-gray-300 border-t-red-500 rounded-full animate-spin"></div>
                                    ) : (
                                      <Heart
                                        className={`w-4 h-4 ${isFavorite(item.id) ? 'fill-red-500 text-red-500' : 'text-gray-400 hover:text-red-500'}`}
                                      />
                                    )}
                                  </button>
                                  <button
                                    className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                                    onClick={() => handleShare(item)}
                                  >
                                    <Share2 className="w-4 h-4 text-gray-400 hover:text-blue-500" />
                                  </button>
                                </div>
                              </div>
                              <p className={`text-sm mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                Código de Classe: {item.classCode}
                              </p>
                              {/* Rating e Distância */}
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center space-x-1">
                                  {renderStars(item.rating)}
                                  <span className="text-sm text-gray-500">({item.rating.toFixed(1)})</span>
                                </div>
                                <span className="text-sm font-medium" style={{color: '#3569b0'}}>
                                  {calculateDistance(item.location)}
                                </span>
                              </div>

                              {/* Descrição como lista */}
                              <ul className={`text-sm mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'} list-disc pl-5`}>
                                {item.description.split('. ').map((point, idx) => (
                                  point.trim() && <li key={idx}>{point.trim().replace(/\.$/, '')}</li>
                                ))}
                              </ul>

                              {/* Botões abaixo da descrição - apenas mobile */}
                              <div className="grid grid-cols-2 gap-1 w-full mt-3 mb-1 md:hidden">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => navigate(`/equipment/${item.id}`)}
                                  className="text-xs p-1 h-8 flex-1 flex justify-center items-center md:w-24"
                                >
                                  <span>Detalhes</span>
                                </Button>
                                <Button
                                  size="sm"
                                  disabled={user?.userType === 'LANDLORD'}
                                  onClick={() => handleAddToCart(item)}
                                  className={`text-xs p-1 h-8 flex-1 flex justify-center items-center md:w-24 ${
                                    user?.userType === 'LANDLORD'
                                      ? 'bg-gray-400 cursor-not-allowed'
                                      : 'bg-blue-600 hover:bg-blue-700'
                                  }`}
                                >
                                  <ShoppingCart className="w-3 h-3 mr-1" />
                                  <span>{user?.userType === 'LANDLORD' ? 'Locadores' : 'Carrinho'}</span>
                                </Button>
                              </div>

                              {/* Botões abaixo da descrição - apenas desktop */}
                              <div className="hidden md:flex flex-row flex-wrap gap-2 w-full justify-end mt-3">
                                <Button
                                  variant="outline"
                                  size="default"
                                  onClick={() => handleOpenMap(item)}
                                  className="text-sm w-40 h-10"
                                >
                                  <MapPin className="w-4 h-4 mr-1" />
                                  <span> Localização</span>
                                </Button>
                                <Button
                                  variant="outline"
                                  size="default"
                                  onClick={() => setQuickViewEquipmentId(quickViewEquipmentId === item.id ? null : item.id)}
                                  className="text-sm w-40 h-10"
                                >
                                  <Eye className="w-4 h-4 mr-1" />
                                  <span>{quickViewEquipmentId === item.id ? 'Ocultar Detalhes' : 'Vista Rápida'}</span>
                                </Button>
                                <Button
                                  variant="outline"
                                  size="default"
                                  onClick={() => navigate(`/equipment/${item.id}`)}
                                  className="text-sm w-40 h-10"
                                >
                                  <span>Ver Detalhes</span>
                                </Button>
                                <Button
                                  size="default"
                                  disabled={user?.userType === 'LANDLORD'}
                                  onClick={() => handleAddToCart(item)}
                                  className={`text-sm w-40 h-10 ${
                                    user?.userType === 'LANDLORD'
                                      ? 'bg-gray-400 cursor-not-allowed'
                                      : 'bg-blue-600 hover:bg-blue-700'
                                  }`}
                                >
                                  <ShoppingCart className="w-4 h-4 mr-1" />
                                  <span>{user?.userType === 'LANDLORD' ? 'Apenas Locatários' : 'Adicionar ao Carrinho'}</span>
                                </Button>
                              </div>
                            </div>
                          </div>

                          {/* Especificações e preços - visíveis apenas na vista rápida */}
                          {quickViewEquipmentId === item.id && (
                            <div className="mt-3">
                              <div className="mb-4">
                                <h3 className="text-sm font-medium mb-1">Especificações:</h3>
                                <ul className="text-sm grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-1">
                                  {item.specifications.map((spec, i) => (
                                    <li key={i} className="flex items-center">
                                      <div className={`w-1.5 h-1.5 rounded-full mr-2 ${isDarkMode ? 'bg-blue-400' : 'bg-blue-600'}`}></div>
                                      {spec}
                                    </li>
                                  ))}
                                </ul>
                              </div>

                              <div className="grid grid-cols-3 gap-2 mb-2 w-full sm:w-auto">
                                <div className={`text-center p-2 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                                  <p className="text-xs text-gray-500">Diária</p>
                                  <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                                    {formatPrice(item.price.daily)}
                                  </p>
                                </div>
                                <div className={`text-center p-2 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                                  <p className="text-xs text-gray-500">Semanal</p>
                                  <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                                    {formatPrice(item.price.weekly)}
                                  </p>
                                </div>
                                <div className={`text-center p-2 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                                  <p className="text-xs text-gray-500">Mensal</p>
                                  <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                                    {formatPrice(item.price.monthly)}
                                  </p>
                                </div>
                                {item.price.annual && (
                                  <div className={`text-center p-2 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'} col-span-3 mt-2`}>
                                    <p className="text-xs text-gray-500">Anual</p>
                                    <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                                      {formatPrice(item.price.annual)}
                                    </p>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Visualização em Grade */}
                {viewType === 'grid' && (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {filteredEquipments.map((item) => (
                      <div
                        key={item.id}
                        className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md overflow-hidden`}
                      >
                        <div className="relative">
                          <img
                            src={item.image}
                            alt={item.title}
                            className="w-full h-48 object-cover"
                          />
                          <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                            Disponível
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h2 className="text-lg font-semibold truncate">{item.title}</h2>
                            <div className="flex space-x-1">
                              <button
                                className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                                onClick={() => handleToggleFavorite(item.id)}
                                disabled={isFavoriteLoading(item.id)}
                              >
                                {isFavoriteLoading(item.id) ? (
                                  <div className="w-4 h-4 border-2 border-gray-300 border-t-red-500 rounded-full animate-spin"></div>
                                ) : (
                                  <Heart
                                    className={`w-4 h-4 ${isFavorite(item.id) ? 'fill-red-500 text-red-500' : 'text-gray-400 hover:text-red-500'}`}
                                  />
                                )}
                              </button>
                              <button
                                className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                                onClick={() => handleShare(item)}
                              >
                                <Share2 className="w-4 h-4 text-gray-400 hover:text-blue-500" />
                              </button>
                            </div>
                          </div>
                          <p className={`text-sm mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                            Código: {item.classCode}
                          </p>
                          {/* Rating e Distância */}
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-1">
                              {renderStars(item.rating)}
                              <span className="text-sm text-gray-500">({item.rating.toFixed(1)})</span>
                            </div>
                            <span className="text-sm font-medium" style={{color: '#3569b0'}}>
                              {calculateDistance(item.location)}
                            </span>
                          </div>

                          {/* Especificações e preços - visíveis apenas na vista rápida */}
                          {quickViewEquipmentId === item.id && (
                            <>
                              <p className={`text-sm mb-3 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                {item.description}
                              </p>
                              <div className="mb-3">
                                <h3 className="text-sm font-medium mb-1">Especificações:</h3>
                                <ul className="text-sm space-y-1">
                                  {item.specifications.slice(0, 3).map((spec, i) => (
                                    <li key={i} className="flex items-center">
                                      <div className={`w-1.5 h-1.5 rounded-full mr-2 ${isDarkMode ? 'bg-blue-400' : 'bg-blue-600'}`}></div>
                                      {spec}
                                    </li>
                                  ))}
                                  {item.specifications.length > 3 && (
                                    <li className="text-sm text-blue-500">+ {item.specifications.length - 3} mais</li>
                                  )}
                                </ul>
                              </div>

                              <div className="grid grid-cols-3 gap-2 mb-4">
                                <div className={`text-center p-2 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                                  <p className="text-xs text-gray-500">Diária</p>
                                  <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                                    {formatPrice(item.price.daily)}
                                  </p>
                                </div>
                                <div className={`text-center p-2 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                                  <p className="text-xs text-gray-500">Semanal</p>
                                  <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                                    {formatPrice(item.price.weekly)}
                                  </p>
                                </div>
                                <div className={`text-center p-2 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                                  <p className="text-xs text-gray-500">Mensal</p>
                                  <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                                    {formatPrice(item.price.monthly)}
                                  </p>
                                </div>
                              </div>
                            </>
                          )}

                          <div className="flex flex-wrap gap-2 mt-3">
                            <Button
                              variant="outline"
                              size="default"
                              onClick={() => handleOpenMap(item)}
                              className="flex-1 h-10"
                            >
                              <MapPin className="w-4 h-4 mr-1" />
                              Mapa
                            </Button>
                            <Button
                              variant="outline"
                              size="default"
                              onClick={() => setQuickViewEquipmentId(quickViewEquipmentId === item.id ? null : item.id)}
                              className="flex-1 h-10"
                            >
                              <Eye className="w-4 h-4 mr-1" />
                              {quickViewEquipmentId === item.id ? 'Ocultar' : 'Vista'}
                            </Button>
                            <Button
                              size="default"
                              onClick={() => handleAddToCart(item)}
                              className="flex-1 bg-blue-600 hover:bg-blue-700 h-10"
                            >
                              <ShoppingCart className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Visualização em Cartões */}
                {viewType === 'card' && (
                  <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4">
                    {filteredEquipments.map((item) => (
                      <div
                        key={item.id}
                        className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md overflow-hidden flex flex-col`}
                      >
                        <div className="relative">
                          <img
                            src={item.image}
                            alt={item.title}
                            className="w-full h-40 object-cover"
                          />
                          <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                            Disponível
                          </div>
                        </div>
                        <div className="p-3 flex-1 flex flex-col">
                          <div className="flex justify-between items-start mb-1">
                            <h2 className="text-base font-semibold line-clamp-2 pr-2">{item.title}</h2>
                            <div className="flex space-x-1 flex-shrink-0">
                              <button
                                className="p-0.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                                onClick={() => handleToggleFavorite(item.id)}
                                disabled={isFavoriteLoading(item.id)}
                              >
                                {isFavoriteLoading(item.id) ? (
                                  <div className="w-3.5 h-3.5 border-2 border-gray-300 border-t-red-500 rounded-full animate-spin"></div>
                                ) : (
                                  <Heart
                                    className={`w-3.5 h-3.5 ${isFavorite(item.id) ? 'fill-red-500 text-red-500' : 'text-gray-400 hover:text-red-500'}`}
                                  />
                                )}
                              </button>
                              <button
                                className="p-0.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                                onClick={() => handleShare(item)}
                              >
                                <Share2 className="w-3.5 h-3.5 text-gray-400 hover:text-blue-500" />
                              </button>
                            </div>
                          </div>
                          <p className={`text-xs mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                            Código: {item.classCode}
                          </p>
                          {/* Rating e Distância */}
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center space-x-1">
                              {renderStars(item.rating).slice(0, 5).map((star, index) =>
                                React.cloneElement(star, { key: index, className: "w-3 h-3 text-yellow-400 fill-current" })
                              )}
                              <span className="text-xs text-gray-500">({item.rating.toFixed(1)})</span>
                            </div>
                            <span className="text-xs font-medium" style={{color: '#3569b0'}}>
                              {calculateDistance(item.location)}
                            </span>
                          </div>

                          {/* Especificações e preços - visíveis apenas na vista rápida */}
                          {quickViewEquipmentId === item.id && (
                            <>
                              <p className={`text-xs mb-2 line-clamp-3 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                {item.description}
                              </p>

                              <div className="grid grid-cols-3 gap-1 mb-2 mt-auto">
                                <div className={`text-center p-1 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                                  <p className="text-[10px] text-gray-500">Diária</p>
                                  <p className={`text-xs font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                                    {formatPrice(item.price.daily)}
                                  </p>
                                </div>
                                <div className={`text-center p-1 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                                  <p className="text-[10px] text-gray-500">Semanal</p>
                                  <p className={`text-xs font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                                    {formatPrice(item.price.weekly)}
                                  </p>
                                </div>
                                <div className={`text-center p-1 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                                  <p className="text-[10px] text-gray-500">Mensal</p>
                                  <p className={`text-xs font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                                    {formatPrice(item.price.monthly)}
                                  </p>
                                </div>
                              </div>
                            </>
                          )}

                          <div className="flex gap-1 mt-auto">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleOpenMap(item)}
                              className="flex-1 h-10 text-xs px-1"
                            >
                              <MapPin className="w-4 h-4 mr-1" />
                              Mapa
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setQuickViewEquipmentId(quickViewEquipmentId === item.id ? null : item.id)}
                              className="flex-1 h-10 text-xs px-1"
                            >
                              <Eye className="w-4 h-4 mr-1" />
                              {quickViewEquipmentId === item.id ? 'Ocultar' : 'Vista'}
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => handleAddToCart(item)}
                              className="h-10 px-2 bg-blue-600 hover:bg-blue-700"
                            >
                              <ShoppingCart className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-8 text-center`}>
                <p className="text-lg mb-4">Nenhum equipamento encontrado</p>
                <p className={`mb-6 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Tente ajustar seus filtros ou pesquisar por outro termo.
                </p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedCategory(null);
                    setSelectedSubcategory(null);
                    setPriceFilter(null);
                    setPeriodFilter(null);
                  }}
                >
                  Limpar Todos os Filtros
                </Button>
              </div>
            )}

            {/* Paginação */}
            {equipment.length > 0 && totalPages > 1 && (
              <div className="flex justify-center mt-6">
                <div className="flex space-x-1">
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={currentPage <= 1}
                    onClick={() => fetchEquipment({ page: currentPage - 1 })}
                  >
                    Anterior
                  </Button>

                  {/* Páginas */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                    return (
                      <Button
                        key={pageNum}
                        variant={currentPage === pageNum ? "default" : "outline"}
                        size="sm"
                        onClick={() => fetchEquipment({ page: pageNum })}
                      >
                        {pageNum}
                      </Button>
                    );
                  })}

                  <Button
                    variant="outline"
                    size="sm"
                    disabled={currentPage >= totalPages}
                    onClick={() => fetchEquipment({ page: currentPage + 1 })}
                  >
                    Próxima
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modal do Mapa */}
      {selectedEquipmentForMap && (
        <MapModal
          isOpen={isMapModalOpen}
          onClose={() => setIsMapModalOpen(false)}
          equipmentId={selectedEquipmentForMap.id}
          equipmentTitle={selectedEquipmentForMap.title}
          isDarkMode={isDarkMode}
        />
      )}
    </div>
  );
};

export default EquipmentCatalog;
