import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus, Edit, Trash2, Save, Upload, Image, FileText, Tag,
  Eye, Calendar, User, Settings, Globe, Smartphone, Monitor
} from 'lucide-react';
import { Button } from '../components/ui/button';
import BackButton from '../components/ui/BackButton';

interface AdminContentManagerProps {
  isDarkMode: boolean;
}

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  category: string;
  tags: string[];
  image: string;
  publishDate: string;
  status: 'draft' | 'published';
  featured: boolean;
}

interface BlogCategory {
  id: string;
  name: string;
  description: string;
  color: string;
}

interface SlideContent {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  image: string;
  buttonText: string;
  buttonLink: string;
  order: number;
  active: boolean;
}

interface HomeContent {
  heroTitle: string;
  heroSubtitle: string;
  heroDescription: string;
  featuredCategories: string[];
  benefitsSection: {
    title: string;
    items: Array<{
      icon: string;
      title: string;
      description: string;
    }>;
  };
}

const AdminContentManager: React.FC<AdminContentManagerProps> = ({ isDarkMode }) => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'blog' | 'categories' | 'slideshow' | 'home'>('blog');
  const [showModal, setShowModal] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);

  // Mock data - em produção viria de uma API
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([
    {
      id: '1',
      title: 'O Futuro dos Equipamentos de Construção em Angola',
      excerpt: 'Descubra as tendências que estão moldando o setor...',
      content: '<p>Conteúdo completo do post...</p>',
      author: 'Carlos Mendes',
      category: 'industria',
      tags: ['construção', 'tecnologia', 'angola'],
      image: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?auto=format&fit=crop&w=800&q=80',
      publishDate: '2024-01-20',
      status: 'published',
      featured: true
    }
  ]);

  const [categories, setCategories] = useState<BlogCategory[]>([
    { id: 'industria', name: 'Indústria', description: 'Tendências do setor', color: '#F59E0B' },
    { id: 'equipamentos', name: 'Equipamentos', description: 'Novidades e reviews', color: '#10B981' },
    { id: 'tecnologia', name: 'Tecnologia', description: 'Inovações tecnológicas', color: '#EF4444' },
    { id: 'sustentabilidade', name: 'Sustentabilidade', description: 'Práticas sustentáveis', color: '#8B5CF6' }
  ]);

  const [slideContent, setSlideContent] = useState<SlideContent[]>([
    {
      id: '1',
      title: 'Equipamentos de Qualidade',
      subtitle: 'Para Seus Projetos',
      description: 'Alugue equipamentos de construção com a melhor qualidade e preços competitivos',
      image: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?auto=format&fit=crop&w=1200&q=80',
      buttonText: 'Ver Equipamentos',
      buttonLink: '/equipment',
      order: 1,
      active: true
    },
    {
      id: '2',
      title: 'Suporte 24/7',
      subtitle: 'Sempre Disponível',
      description: 'Nossa equipe está sempre pronta para ajudar você em qualquer momento',
      image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?auto=format&fit=crop&w=1200&q=80',
      buttonText: 'Falar Conosco',
      buttonLink: '/contact',
      order: 2,
      active: true
    }
  ]);

  const [homeContent, setHomeContent] = useState<HomeContent>({
    heroTitle: 'Aluguel de Equipamentos',
    heroSubtitle: 'Soluções Completas para Construção',
    heroDescription: 'Encontre os melhores equipamentos para seu projeto com qualidade garantida e preços competitivos.',
    featuredCategories: ['escavadeiras', 'guindastes', 'compactadores', 'geradores'],
    benefitsSection: {
      title: 'Por que escolher a YMRentals?',
      items: [
        {
          icon: 'shield',
          title: 'Equipamentos Certificados',
          description: 'Todos os equipamentos passam por rigorosa inspeção'
        },
        {
          icon: 'clock',
          title: 'Entrega Rápida',
          description: 'Entregamos em até 24 horas em Luanda'
        },
        {
          icon: 'users',
          title: 'Suporte Especializado',
          description: 'Equipe técnica qualificada para orientar você'
        },
        {
          icon: 'dollar-sign',
          title: 'Preços Competitivos',
          description: 'Os melhores preços do mercado angolano'
        }
      ]
    }
  });

  const handleSaveBlogPost = (post: BlogPost) => {
    if (editingItem) {
      setBlogPosts(blogPosts.map(p => p.id === post.id ? post : p));
    } else {
      setBlogPosts([...blogPosts, { ...post, id: Date.now().toString() }]);
    }
    setShowModal(false);
    setEditingItem(null);
  };

  const handleDeleteBlogPost = (id: string) => {
    setBlogPosts(blogPosts.filter(p => p.id !== id));
  };

  const handleSaveCategory = (category: BlogCategory) => {
    if (editingItem) {
      setCategories(categories.map(c => c.id === category.id ? category : c));
    } else {
      setCategories([...categories, { ...category, id: Date.now().toString() }]);
    }
    setShowModal(false);
    setEditingItem(null);
  };

  const handleDeleteCategory = (id: string) => {
    setCategories(categories.filter(c => c.id !== id));
  };

  const handleSaveSlide = (slide: SlideContent) => {
    if (editingItem) {
      setSlideContent(slideContent.map(s => s.id === slide.id ? slide : s));
    } else {
      setSlideContent([...slideContent, { ...slide, id: Date.now().toString() }]);
    }
    setShowModal(false);
    setEditingItem(null);
  };

  const handleDeleteSlide = (id: string) => {
    setSlideContent(slideContent.filter(s => s.id !== id));
  };

  const renderBlogTab = () => (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Gerenciar Posts do Blog
        </h2>
        <Button
          onClick={() => {
            setEditingItem(null);
            setShowModal(true);
          }}
          className="text-white"
          style={{backgroundColor: '#3569b0'}}
        >
          <Plus className="w-4 h-4 mr-2" />
          Novo Post
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6">
        {blogPosts.map((post) => (
          <div
            key={post.id}
            className={`p-6 rounded-lg shadow-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {post.title}
                  </h3>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    post.status === 'published' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {post.status === 'published' ? 'Publicado' : 'Rascunho'}
                  </span>
                  {post.featured && (
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                      Destaque
                    </span>
                  )}
                </div>
                <p className={`text-sm mb-3 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {post.excerpt}
                </p>
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  <div className="flex items-center space-x-1">
                    <User className="w-4 h-4" />
                    <span>{post.author}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-4 h-4" />
                    <span>{new Date(post.publishDate).toLocaleDateString('pt-AO')}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Tag className="w-4 h-4" />
                    <span>{categories.find(c => c.id === post.category)?.name}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate(`/blog/${post.id}`)}
                >
                  <Eye className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setEditingItem(post);
                    setShowModal(true);
                  }}
                >
                  <Edit className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteBlogPost(post.id)}
                  className="text-red-500 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderCategoriesTab = () => (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Gerenciar Categorias
        </h2>
        <Button
          onClick={() => {
            setEditingItem(null);
            setShowModal(true);
          }}
          className="text-white"
          style={{backgroundColor: '#3569b0'}}
        >
          <Plus className="w-4 h-4 mr-2" />
          Nova Categoria
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {categories.map((category) => (
          <div
            key={category.id}
            className={`p-6 rounded-lg shadow-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}
          >
            <div className="flex items-center justify-between mb-4">
              <div
                className="w-4 h-4 rounded-full"
                style={{backgroundColor: category.color}}
              />
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setEditingItem(category);
                    setShowModal(true);
                  }}
                >
                  <Edit className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteCategory(category.id)}
                  className="text-red-500 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <h3 className={`text-lg font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              {category.name}
            </h3>
            <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {category.description}
            </p>
          </div>
        ))}
      </div>
    </div>
  );

  const renderSlideshowTab = () => (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Gerenciar Slideshow
        </h2>
        <Button
          onClick={() => {
            setEditingItem(null);
            setShowModal(true);
          }}
          className="text-white"
          style={{backgroundColor: '#3569b0'}}
        >
          <Plus className="w-4 h-4 mr-2" />
          Novo Slide
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6">
        {slideContent.map((slide) => (
          <div
            key={slide.id}
            className={`p-6 rounded-lg shadow-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}
          >
            <div className="flex items-start space-x-6">
              <img
                src={slide.image}
                alt={slide.title}
                className="w-32 h-20 object-cover rounded-lg"
              />
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {slide.title}
                  </h3>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    slide.active 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {slide.active ? 'Ativo' : 'Inativo'}
                  </span>
                </div>
                <p className={`text-sm mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {slide.subtitle}
                </p>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {slide.description}
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setEditingItem(slide);
                    setShowModal(true);
                  }}
                >
                  <Edit className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteSlide(slide.id)}
                  className="text-red-500 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderHomeTab = () => (
    <div>
      <h2 className={`text-2xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
        Gerenciar Conteúdo da Home
      </h2>

      <div className="space-y-8">
        {/* Hero Section */}
        <div className={`p-6 rounded-lg shadow-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Seção Hero
          </h3>
          <div className="space-y-4">
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Título Principal
              </label>
              <input
                type="text"
                value={homeContent.heroTitle}
                onChange={(e) => setHomeContent({...homeContent, heroTitle: e.target.value})}
                className={`w-full px-3 py-2 border rounded-lg ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Subtítulo
              </label>
              <input
                type="text"
                value={homeContent.heroSubtitle}
                onChange={(e) => setHomeContent({...homeContent, heroSubtitle: e.target.value})}
                className={`w-full px-3 py-2 border rounded-lg ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Descrição
              </label>
              <textarea
                rows={3}
                value={homeContent.heroDescription}
                onChange={(e) => setHomeContent({...homeContent, heroDescription: e.target.value})}
                className={`w-full px-3 py-2 border rounded-lg ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>
          </div>
        </div>

        {/* Benefícios */}
        <div className={`p-6 rounded-lg shadow-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Seção de Benefícios
          </h3>
          <div className="space-y-4">
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Título da Seção
              </label>
              <input
                type="text"
                value={homeContent.benefitsSection.title}
                onChange={(e) => setHomeContent({
                  ...homeContent,
                  benefitsSection: {
                    ...homeContent.benefitsSection,
                    title: e.target.value
                  }
                })}
                className={`w-full px-3 py-2 border rounded-lg ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>
            <div className="space-y-4">
              {homeContent.benefitsSection.items.map((item, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border rounded-lg">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Título
                    </label>
                    <input
                      type="text"
                      value={item.title}
                      onChange={(e) => {
                        const newItems = [...homeContent.benefitsSection.items];
                        newItems[index] = {...item, title: e.target.value};
                        setHomeContent({
                          ...homeContent,
                          benefitsSection: {
                            ...homeContent.benefitsSection,
                            items: newItems
                          }
                        });
                      }}
                      className={`w-full px-3 py-2 border rounded-lg ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>
                  <div className="md:col-span-2">
                    <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Descrição
                    </label>
                    <input
                      type="text"
                      value={item.description}
                      onChange={(e) => {
                        const newItems = [...homeContent.benefitsSection.items];
                        newItems[index] = {...item, description: e.target.value};
                        setHomeContent({
                          ...homeContent,
                          benefitsSection: {
                            ...homeContent.benefitsSection,
                            items: newItems
                          }
                        });
                      }}
                      className={`w-full px-3 py-2 border rounded-lg ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <Button
            onClick={() => {
              // Aqui salvaria as alterações na API
              console.log('Saving home content:', homeContent);
              alert('Conteúdo da home salvo com sucesso!');
            }}
            className="text-white"
            style={{backgroundColor: '#3569b0'}}
          >
            <Save className="w-4 h-4 mr-2" />
            Salvar Alterações
          </Button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen max-w-full overflow-x-hidden">
      <div className={`container mx-auto px-4 py-8 pt-24 max-w-7xl ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
        <div className="flex items-center space-x-4 mb-8">
          <BackButton isDarkMode={isDarkMode} />
          <div>
            <h1 className={`text-4xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Gerenciamento de Conteúdo
            </h1>
            <p className={`text-lg ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Gerencie posts do blog, categorias, slideshow e conteúdo da home
            </p>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mb-8">
          {[
            { id: 'blog', label: 'Blog Posts', icon: FileText },
            { id: 'categories', label: 'Categorias', icon: Tag },
            { id: 'slideshow', label: 'Slideshow', icon: Image },
            { id: 'home', label: 'Home Page', icon: Monitor }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                activeTab === tab.id
                  ? isDarkMode
                    ? 'bg-blue-900/30 text-blue-400'
                    : 'bg-blue-50 text-blue-600'
                  : isDarkMode
                  ? 'text-gray-400 hover:text-gray-300'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-8`}>
          {activeTab === 'blog' && renderBlogTab()}
          {activeTab === 'categories' && renderCategoriesTab()}
          {activeTab === 'slideshow' && renderSlideshowTab()}
          {activeTab === 'home' && renderHomeTab()}
        </div>
      </div>
    </div>
  );
};

export default AdminContentManager;
