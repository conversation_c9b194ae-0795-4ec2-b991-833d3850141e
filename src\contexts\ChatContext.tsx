import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import apiService from '../services/api';
import { io, Socket } from 'socket.io-client';

interface Message {
  id: string;
  content: string;
  senderId: string;
  receiverId: string;
  conversationId: string;
  createdAt: string;
  isRead: boolean;
  sender?: {
    id: string;
    fullName: string;
    profilePicture?: string;
  };
}

interface Conversation {
  id: string;
  participants: {
    id: string;
    fullName: string;
    profilePicture?: string;
  }[];
  lastMessage?: Message;
  unreadCount: number;
  createdAt: string;
  updatedAt: string;
}

interface ChatContextType {
  conversations: Conversation[];
  messages: Record<string, Message[]>;
  activeConversation: string | null;
  loading: boolean;
  error: string | null;
  unreadCount: number;
  isConnected: boolean;
  fetchConversations: () => Promise<void>;
  fetchMessages: (conversationId: string) => Promise<void>;
  sendMessage: (conversationId: string, content: string) => Promise<void>;
  createConversation: (participantId: string) => Promise<string>;
  setActiveConversation: (conversationId: string | null) => void;
  markAsRead: (conversationId: string) => Promise<void>;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export const useChat = () => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};

interface ChatProviderProps {
  children: ReactNode;
}

export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [messages, setMessages] = useState<Record<string, Message[]>>({});
  const [activeConversation, setActiveConversation] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isConnected, setIsConnected] = useState(false);
  const [socket, setSocket] = useState<Socket | null>(null);

  // Conectar WebSocket quando usuário estiver autenticado
  useEffect(() => {
    if (isAuthenticated && user) {
      const token = localStorage.getItem('authToken') || localStorage.getItem('accessToken');
      if (token) {
        connectWebSocket();
        fetchConversations();
      }
    } else {
      disconnectWebSocket();
    }

    return () => {
      disconnectWebSocket();
    };
  }, [isAuthenticated, user]);

  const connectWebSocket = () => {
    try {
      const token = localStorage.getItem('authToken') || localStorage.getItem('accessToken');
      if (!token) {
        console.log('No token available for WebSocket connection');
        return;
      }

      // Conectar usando Socket.IO
      const socketInstance = io('http://localhost:3000/chat', {
        auth: {
          token: token
        },
        transports: ['websocket', 'polling']
      });

      socketInstance.on('connect', () => {
        console.log('Socket.IO conectado');
        setIsConnected(true);
      });

      socketInstance.on('disconnect', () => {
        console.log('Socket.IO desconectado');
        setIsConnected(false);
      });

      socketInstance.on('connect_error', (error) => {
        console.error('Erro na conexão Socket.IO:', error);
        setIsConnected(false);
      });

      // Escutar eventos de mensagens
      socketInstance.on('new_message', (data) => {
        handleWebSocketMessage({ type: 'new_message', message: data });
      });

      socketInstance.on('message_read', (data) => {
        handleWebSocketMessage({ type: 'message_read', ...data });
      });

      setSocket(socketInstance);
    } catch (error) {
      console.error('Erro ao conectar Socket.IO:', error);
    }
  };

  const disconnectWebSocket = () => {
    if (socket) {
      socket.disconnect();
      setSocket(null);
    }
    setIsConnected(false);
  };

  const handleWebSocketMessage = (data: any) => {
    switch (data.type) {
      case 'new_message':
        const newMessage = data.message;
        setMessages(prev => ({
          ...prev,
          [newMessage.conversationId]: [
            ...(prev[newMessage.conversationId] || []),
            newMessage
          ]
        }));
        
        // Atualizar última mensagem na conversa
        setConversations(prev => 
          prev.map(conv => 
            conv.id === newMessage.conversationId 
              ? { ...conv, lastMessage: newMessage, unreadCount: conv.unreadCount + 1 }
              : conv
          )
        );
        
        // Atualizar contador de não lidas
        if (newMessage.senderId !== user?.id) {
          setUnreadCount(prev => prev + 1);
        }
        break;
        
      case 'message_read':
        // Marcar mensagens como lidas
        setMessages(prev => ({
          ...prev,
          [data.conversationId]: prev[data.conversationId]?.map(msg => 
            msg.senderId === user?.id ? { ...msg, isRead: true } : msg
          ) || []
        }));
        break;
    }
  };

  const fetchConversations = async () => {
    try {
      setLoading(true);
      const response = await apiService.getConversations();

      if (response && Array.isArray(response)) {
        // Transformar dados do backend para o formato esperado pelo frontend
        const transformedConversations: Conversation[] = response.map((chat: any) => {
          const participants = [
            {
              id: chat.User_Chat_initiatorIdToUser.id,
              fullName: chat.User_Chat_initiatorIdToUser.fullName,
              profilePicture: chat.User_Chat_initiatorIdToUser.profilePicture
            },
            {
              id: chat.User_Chat_receiverIdToUser.id,
              fullName: chat.User_Chat_receiverIdToUser.fullName,
              profilePicture: chat.User_Chat_receiverIdToUser.profilePicture
            }
          ];

          const lastMessage = chat.Message && chat.Message.length > 0 ?
            chat.Message[chat.Message.length - 1] : null;

          return {
            id: chat.id,
            participants,
            lastMessage: lastMessage ? {
              id: lastMessage.id,
              content: lastMessage.content,
              senderId: lastMessage.senderId,
              receiverId: participants.find(p => p.id !== lastMessage.senderId)?.id || '',
              conversationId: chat.id,
              createdAt: lastMessage.createdAt,
              isRead: true // Por enquanto, assumir como lida
            } : undefined,
            unreadCount: 0, // Por enquanto, sem contagem de não lidas
            createdAt: chat.createdAt,
            updatedAt: chat.createdAt
          };
        });

        setConversations(transformedConversations);
        const totalUnread = transformedConversations.reduce((acc: number, conv: Conversation) => acc + conv.unreadCount, 0);
        setUnreadCount(totalUnread);
      } else {
        setConversations([]);
        setUnreadCount(0);
      }
    } catch (err: any) {
      console.log('Erro ao carregar conversas:', err.message);
      setConversations([]);
      setUnreadCount(0);
      // Não definir como erro para não quebrar a UI
    } finally {
      setLoading(false);
    }
  };

  const fetchMessages = async (conversationId: string) => {
    try {
      const response = await apiService.getMessages(conversationId);
      setMessages(prev => ({
        ...prev,
        [conversationId]: response
      }));
    } catch (err: any) {
      console.log(`Conversa ${conversationId} não encontrada ou sem mensagens`);
      // Não definir como erro se a conversa não existir
      setMessages(prev => ({
        ...prev,
        [conversationId]: []
      }));
    }
  };

  const sendMessage = async (conversationId: string, content: string) => {
    try {
      const message = await apiService.sendMessage(conversationId, content);
      
      // Adicionar mensagem localmente
      setMessages(prev => ({
        ...prev,
        [conversationId]: [...(prev[conversationId] || []), message]
      }));

      // Atualizar última mensagem na conversa
      setConversations(prev => 
        prev.map(conv => 
          conv.id === conversationId 
            ? { ...conv, lastMessage: message }
            : conv
        )
      );

      // Enviar via Socket.IO se conectado
      if (socket && socket.connected) {
        socket.emit('send_message', {
          conversationId,
          content
        });
      }

      return message;
    } catch (err: any) {
      setError(err.message || 'Erro ao enviar mensagem');
      throw err;
    }
  };

  const createConversation = async (participantId: string): Promise<string> => {
    try {
      const conversation = await apiService.createConversation(participantId);
      setConversations(prev => [conversation, ...prev]);
      return conversation.id;
    } catch (err: any) {
      setError(err.message || 'Erro ao criar conversa');
      throw err;
    }
  };

  const markAsRead = async (conversationId: string) => {
    try {
      await apiService.markMessagesAsRead(conversationId);
      
      // Atualizar contador local
      setConversations(prev => 
        prev.map(conv => 
          conv.id === conversationId 
            ? { ...conv, unreadCount: 0 }
            : conv
        )
      );

      // Recalcular total de não lidas
      const newUnreadCount = conversations
        .filter(conv => conv.id !== conversationId)
        .reduce((acc, conv) => acc + conv.unreadCount, 0);
      setUnreadCount(newUnreadCount);

      // Notificar via Socket.IO
      if (socket && socket.connected) {
        socket.emit('mark_read', {
          conversationId
        });
      }
    } catch (err: any) {
      setError(err.message || 'Erro ao marcar como lida');
    }
  };

  const value: ChatContextType = {
    conversations,
    messages,
    activeConversation,
    loading,
    error,
    unreadCount,
    isConnected,
    fetchConversations,
    fetchMessages,
    sendMessage,
    createConversation,
    setActiveConversation,
    markAsRead,
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
};
