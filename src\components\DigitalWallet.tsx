import React, { useState } from 'react';
import { 
  Wallet, 
  Plus, 
  Minus, 
  ArrowUpRight, 
  ArrowDownLeft, 
  CreditCard, 
  Banknote,
  TrendingUp,
  Eye,
  EyeOff,
  Star
} from 'lucide-react';
import { Button } from './ui/button';

interface Transaction {
  id: string;
  type: 'deposit' | 'withdrawal' | 'payment' | 'refund' | 'priority_payment';
  amount: number;
  description: string;
  date: string;
  status: 'completed' | 'pending' | 'failed';
}

interface DigitalWalletProps {
  isDarkMode: boolean;
}

const DigitalWallet: React.FC<DigitalWalletProps> = ({ isDarkMode }) => {
  const [balance, setBalance] = useState(1250000); // Saldo em Kwanza
  const [showBalance, setShowBalance] = useState(true);
  const [depositAmount, setDepositAmount] = useState('');
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [activeTab, setActiveTab] = useState<'overview' | 'deposit' | 'withdraw' | 'history'>('overview');

  const [transactions, setTransactions] = useState<Transaction[]>([
    {
      id: '1',
      type: 'deposit',
      amount: 500000,
      description: 'Dep<PERSON><PERSON> via transferência bancária',
      date: '2024-01-15T10:30:00',
      status: 'completed'
    },
    {
      id: '2',
      type: 'payment',
      amount: -250000,
      description: 'Aluguel Escavadeira CAT 320 - 5 dias',
      date: '2024-01-14T14:20:00',
      status: 'completed'
    },
    {
      id: '3',
      type: 'priority_payment',
      amount: -50000,
      description: 'Pagamento de prioridade - Pedido #1234',
      date: '2024-01-13T09:15:00',
      status: 'completed'
    },
    {
      id: '4',
      type: 'deposit',
      amount: 1000000,
      description: 'Depósito inicial',
      date: '2024-01-10T16:45:00',
      status: 'completed'
    },
    {
      id: '5',
      type: 'refund',
      amount: 50000,
      description: 'Reembolso - Cancelamento de pedido',
      date: '2024-01-09T11:30:00',
      status: 'completed'
    }
  ]);

  // Função para formatar preço
  const formatPrice = (price: number): string => {
    return Math.abs(price).toLocaleString('pt-AO') + ' Kz';
  };

  // Função para fazer depósito
  const handleDeposit = () => {
    const amount = parseFloat(depositAmount);
    if (amount > 0) {
      const newTransaction: Transaction = {
        id: Date.now().toString(),
        type: 'deposit',
        amount: amount,
        description: 'Depósito via carteira digital',
        date: new Date().toISOString(),
        status: 'completed'
      };
      
      setTransactions(prev => [newTransaction, ...prev]);
      setBalance(prev => prev + amount);
      setDepositAmount('');
    }
  };

  // Função para fazer saque
  const handleWithdraw = () => {
    const amount = parseFloat(withdrawAmount);
    if (amount > 0 && amount <= balance) {
      const newTransaction: Transaction = {
        id: Date.now().toString(),
        type: 'withdrawal',
        amount: -amount,
        description: 'Saque via carteira digital',
        date: new Date().toISOString(),
        status: 'completed'
      };
      
      setTransactions(prev => [newTransaction, ...prev]);
      setBalance(prev => prev - amount);
      setWithdrawAmount('');
    }
  };

  // Função para pagar prioridade
  const payForPriority = () => {
    const priorityFee = 25000; // Taxa de prioridade
    if (balance >= priorityFee) {
      const newTransaction: Transaction = {
        id: Date.now().toString(),
        type: 'priority_payment',
        amount: -priorityFee,
        description: 'Pagamento de prioridade no aluguel',
        date: new Date().toISOString(),
        status: 'completed'
      };
      
      setTransactions(prev => [newTransaction, ...prev]);
      setBalance(prev => prev - priorityFee);
      
      alert('Prioridade adquirida com sucesso! Seu pedido será processado primeiro.');
    } else {
      alert('Saldo insuficiente para adquirir prioridade.');
    }
  };

  // Função para obter ícone da transação
  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'deposit': return <ArrowDownLeft className="w-4 h-4 text-green-500" />;
      case 'withdrawal': return <ArrowUpRight className="w-4 h-4 text-red-500" />;
      case 'payment': return <CreditCard className="w-4 h-4 text-blue-500" />;
      case 'refund': return <ArrowDownLeft className="w-4 h-4 text-green-500" />;
      case 'priority_payment': return <Star className="w-4 h-4 text-yellow-500" />;
      default: return <Wallet className="w-4 h-4 text-gray-500" />;
    }
  };

  // Função para obter cor da transação
  const getTransactionColor = (amount: number) => {
    return amount > 0 ? 'text-green-600' : 'text-red-600';
  };

  return (
    <div className={`${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'} min-h-screen p-6`}>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Carteira Digital</h1>
          <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Gerencie seus depósitos e pagamentos em Kwanza
          </p>
        </div>

        {/* Card do saldo */}
        <div className={`${isDarkMode ? 'bg-gradient-to-r from-blue-900 to-blue-800' : 'bg-gradient-to-r from-blue-600 to-blue-700'} p-6 rounded-lg shadow-lg mb-8 text-white`}>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <Wallet className="w-6 h-6" />
              <span className="text-lg font-medium">Saldo Disponível</span>
            </div>
            <button
              onClick={() => setShowBalance(!showBalance)}
              className="p-2 hover:bg-white/20 rounded-full transition-colors"
            >
              {showBalance ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
            </button>
          </div>
          <div className="text-3xl font-bold mb-2">
            {showBalance ? formatPrice(balance) : '••••••• Kz'}
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <TrendingUp className="w-4 h-4" />
              <span className="text-sm">+12% este mês</span>
            </div>
          </div>
        </div>

        {/* Navegação por abas */}
        <div className="mb-6">
          <div className="flex space-x-1 bg-gray-200 dark:bg-gray-800 p-1 rounded-lg">
            {[
              { key: 'overview', label: 'Visão Geral', icon: Wallet },
              { key: 'deposit', label: 'Depositar', icon: Plus },
              { key: 'withdraw', label: 'Sacar', icon: Minus },
              { key: 'history', label: 'Histórico', icon: CreditCard }
            ].map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                  activeTab === tab.key
                    ? 'bg-white dark:bg-gray-700 shadow-sm'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
                style={{
                  backgroundColor: activeTab === tab.key ? '#3569b0' : undefined,
                  color: activeTab === tab.key ? 'white' : undefined
                }}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Conteúdo das abas */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Ações rápidas */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold">Depósito Rápido</h3>
                  <Plus className="w-5 h-5 text-green-500" />
                </div>
                <p className="text-sm text-gray-500 mb-4">Adicione fundos à sua carteira</p>
                <Button 
                  onClick={() => setActiveTab('deposit')}
                  className="w-full"
                  style={{backgroundColor: '#3569b0'}}
                >
                  Depositar
                </Button>
              </div>

              <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold">Prioridade</h3>
                  <Star className="w-5 h-5 text-yellow-500" />
                </div>
                <p className="text-sm text-gray-500 mb-4">Pague para ter prioridade</p>
                <Button 
                  onClick={payForPriority}
                  className="w-full bg-yellow-600 hover:bg-yellow-700"
                >
                  25.000 Kz
                </Button>
              </div>

              <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold">Saque</h3>
                  <Minus className="w-5 h-5 text-red-500" />
                </div>
                <p className="text-sm text-gray-500 mb-4">Retire seus fundos</p>
                <Button 
                  onClick={() => setActiveTab('withdraw')}
                  variant="outline"
                  className="w-full"
                >
                  Sacar
                </Button>
              </div>
            </div>

            {/* Transações recentes */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md`}>
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold">Transações Recentes</h3>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {transactions.slice(0, 5).map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getTransactionIcon(transaction.type)}
                        <div>
                          <p className="font-medium">{transaction.description}</p>
                          <p className="text-sm text-gray-500">
                            {new Date(transaction.date).toLocaleDateString('pt-AO')}
                          </p>
                        </div>
                      </div>
                      <div className={`font-semibold ${getTransactionColor(transaction.amount)}`}>
                        {transaction.amount > 0 ? '+' : ''}{formatPrice(transaction.amount)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'deposit' && (
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
            <h3 className="text-lg font-semibold mb-4">Fazer Depósito</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Valor (Kz)</label>
                <input
                  type="number"
                  value={depositAmount}
                  onChange={(e) => setDepositAmount(e.target.value)}
                  placeholder="Digite o valor"
                  className={`w-full p-3 border rounded-md ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'}`}
                />
              </div>
              <div className="flex space-x-2">
                {[50000, 100000, 250000, 500000].map(amount => (
                  <button
                    key={amount}
                    onClick={() => setDepositAmount(amount.toString())}
                    className={`px-3 py-2 text-sm border rounded-md ${isDarkMode ? 'border-gray-600 hover:bg-gray-700' : 'border-gray-300 hover:bg-gray-50'}`}
                  >
                    {formatPrice(amount)}
                  </button>
                ))}
              </div>
              <Button 
                onClick={handleDeposit}
                className="w-full"
                style={{backgroundColor: '#3569b0'}}
                disabled={!depositAmount || parseFloat(depositAmount) <= 0}
              >
                <Plus className="w-4 h-4 mr-2" />
                Depositar
              </Button>
            </div>
          </div>
        )}

        {activeTab === 'withdraw' && (
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
            <h3 className="text-lg font-semibold mb-4">Fazer Saque</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Valor (Kz)</label>
                <input
                  type="number"
                  value={withdrawAmount}
                  onChange={(e) => setWithdrawAmount(e.target.value)}
                  placeholder="Digite o valor"
                  max={balance}
                  className={`w-full p-3 border rounded-md ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'}`}
                />
                <p className="text-sm text-gray-500 mt-1">
                  Saldo disponível: {formatPrice(balance)}
                </p>
              </div>
              <Button 
                onClick={handleWithdraw}
                variant="outline"
                className="w-full border-red-300 text-red-600 hover:bg-red-50"
                disabled={!withdrawAmount || parseFloat(withdrawAmount) <= 0 || parseFloat(withdrawAmount) > balance}
              >
                <Minus className="w-4 h-4 mr-2" />
                Sacar
              </Button>
            </div>
          </div>
        )}

        {activeTab === 'history' && (
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md`}>
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold">Histórico de Transações</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {transactions.map((transaction) => (
                  <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-md border-gray-200 dark:border-gray-700">
                    <div className="flex items-center space-x-3">
                      {getTransactionIcon(transaction.type)}
                      <div>
                        <p className="font-medium">{transaction.description}</p>
                        <p className="text-sm text-gray-500">
                          {new Date(transaction.date).toLocaleString('pt-AO')}
                        </p>
                      </div>
                    </div>
                    <div className={`font-semibold ${getTransactionColor(transaction.amount)}`}>
                      {transaction.amount > 0 ? '+' : ''}{formatPrice(transaction.amount)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DigitalWallet;
