import React from 'react';
import { Button } from '../components/ui/button';
import { Camera, Upload, X } from 'lucide-react';

interface CreateListingProps {
  isDarkMode: boolean;
}

const CreateListing: React.FC<CreateListingProps> = ({ isDarkMode }) => {
  return (
    <div className="container mx-auto px-4 py-8 pt-24">
      <h1 className={`text-2xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}><PERSON><PERSON><PERSON></h1>

      <div className={`rounded-lg shadow-lg p-6 mb-8 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
        <h2 className={`text-xl font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Informações do Equipamento</h2>

        <div className="space-y-6">
          <div>
            <label htmlFor="title" className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Título do Anúncio*
            </label>
            <input
              type="text"
              id="title"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                isDarkMode
                  ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:ring-blue-500'
                  : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:ring-blue-500'
              }`}
              placeholder="Ex: Retroescavadeira CAT 420F2 em excelente estado"
            />
          </div>

          <div>
            <label htmlFor="category" className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Categoria*
            </label>
            <select
              id="category"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                isDarkMode
                  ? 'bg-gray-700 border-gray-600 text-white focus:ring-blue-500'
                  : 'bg-white border-gray-300 text-gray-900 focus:ring-blue-500'
              }`}
            >
              <option value="">Selecione uma categoria</option>
              <option value="industrial">Industrial</option>
              <option value="construction">Construção</option>
              <option value="electrical">Elétricos</option>
              <option value="hydraulic">Hidráulica</option>
              <option value="tools">Ferramentas</option>
            </select>
          </div>

          <div>
            <label htmlFor="description" className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Descrição*
            </label>
            <textarea
              id="description"
              rows={5}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                isDarkMode
                  ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:ring-blue-500'
                  : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:ring-blue-500'
              }`}
              placeholder="Descreva o equipamento em detalhes, incluindo estado, características, etc."
            ></textarea>
          </div>

          <div>
            <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Fotos do Equipamento*
            </label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className={`border-2 border-dashed rounded-lg p-4 flex flex-col items-center justify-center h-32 cursor-pointer ${
                isDarkMode
                  ? 'border-gray-600 hover:border-blue-500'
                  : 'border-gray-300 hover:border-blue-500'
              }`}>
                <Camera className={`w-8 h-8 mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-400'}`} />
                <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Adicionar foto</span>
              </div>

              {/* Preview de foto */}
              <div className={`relative border rounded-lg overflow-hidden h-32 ${
                isDarkMode ? 'border-gray-600' : 'border-gray-200'
              }`}>
                <img
                  src="https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=600"
                  alt="Preview"
                  className="w-full h-full object-cover"
                />
                <button className={`absolute top-1 right-1 rounded-full p-1 shadow-md ${
                  isDarkMode ? 'bg-gray-700' : 'bg-white'
                }`}>
                  <X className="w-4 h-4 text-red-500" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className={`rounded-lg shadow-lg p-6 mb-8 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
        <h2 className={`text-xl font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Preço e Disponibilidade</h2>

        <div className="space-y-6">
          <div>
            <label htmlFor="price" className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Preço por Dia (Kz)*
            </label>
            <input
              type="number"
              id="price"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                isDarkMode
                  ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:ring-blue-500'
                  : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:ring-blue-500'
              }`}
              placeholder="Ex: 250000"
            />
          </div>

          <div>
            <label htmlFor="location" className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Localização*
            </label>
            <input
              type="text"
              id="location"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                isDarkMode
                  ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:ring-blue-500'
                  : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:ring-blue-500'
              }`}
              placeholder="Ex: Luanda, Talatona"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="available"
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="available" className={`ml-2 block text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Disponível para aluguel imediato
            </label>
          </div>
        </div>
      </div>

      <div className={`rounded-lg shadow-lg p-6 mb-8 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
        <h2 className={`text-xl font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Especificações Técnicas</h2>

        <div className="space-y-6">
          <div>
            <label htmlFor="year" className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Ano
            </label>
            <input
              type="number"
              id="year"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                isDarkMode
                  ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:ring-blue-500'
                  : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:ring-blue-500'
              }`}
              placeholder="Ex: 2020"
            />
          </div>

          <div>
            <label htmlFor="hours" className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Horímetro
            </label>
            <input
              type="number"
              id="hours"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                isDarkMode
                  ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:ring-blue-500'
                  : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:ring-blue-500'
              }`}
              placeholder="Ex: 2500"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="insurance"
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="insurance" className={`ml-2 block text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Seguro incluso
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="support"
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="support" className={`ml-2 block text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Suporte 24/7
            </label>
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-4">
        <Button variant="outline">Cancelar</Button>
        <Button
          className="text-white"
          style={{backgroundColor: '#3569b0'}}
          onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2a5490'}
          onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3569b0'}
        >
          Publicar Anúncio
        </Button>
      </div>
    </div>
  );
};

export default CreateListing;
