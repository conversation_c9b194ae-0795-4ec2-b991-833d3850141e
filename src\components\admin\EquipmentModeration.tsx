import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { Eye, Check, X, Search, Filter, Calendar, User, MapPin, DollarSign, Package, Mail } from 'lucide-react';
import { apiService } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

interface Equipment {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  pricePeriod: string;
  images: string[];
  moderationStatus: string;
  createdAt: string;
  owner: {
    id: string;
    fullName: string;
    email: string;
    companyName?: string;
  };
  Address?: {
    city: string;
    province: string;
  };
}

export default function EquipmentModeration() {
  const { user } = useAuth();
  const [equipments, setEquipments] = useState<Equipment[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedEquipment, setSelectedEquipment] = useState<Equipment | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [processing, setProcessing] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    search: '',
    status: 'PENDING',
    category: '',
    dateFrom: '',
    dateTo: '',
    page: 1,
    limit: 12
  });
  const [stats, setStats] = useState<any>(null);

  useEffect(() => {
    fetchEquipments();
    fetchModerationStats();
  }, [filters]);

  const fetchEquipments = async () => {
    try {
      setLoading(true);
      let data;

      if (filters.status === 'PENDING') {
        data = await apiService.getModerationPendingEquipment(filters);
      } else {
        // Buscar todos os equipamentos com filtro de status
        data = await apiService.getAllEquipments(filters);
      }

      setEquipments(data.data || data);
    } catch (error: any) {
      console.error('Erro ao carregar equipamentos:', error);
      alert('Erro ao carregar equipamentos');
    } finally {
      setLoading(false);
    }
  };

  const fetchModerationStats = async () => {
    try {
      const data = await apiService.getModerationStats();
      setStats(data);
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error);
    }
  };

  const handleApprove = async (equipmentId: string) => {
    try {
      setProcessing(equipmentId);
      await apiService.approveEquipment(equipmentId);
      
      // Remover da lista
      setEquipments(prev => prev.filter(eq => eq.id !== equipmentId));
      setSelectedEquipment(null);
      
      alert('Equipamento aprovado com sucesso!');
      fetchModerationStats();
    } catch (error: any) {
      console.error('Erro ao aprovar equipamento:', error);
      alert('Erro ao aprovar equipamento');
    } finally {
      setProcessing(null);
    }
  };

  const handleReject = async (equipmentId: string) => {
    if (!rejectionReason.trim()) {
      alert('Por favor, forneça uma razão para a rejeição');
      return;
    }

    try {
      setProcessing(equipmentId);
      await apiService.rejectEquipment(equipmentId, rejectionReason);
      
      // Remover da lista
      setEquipments(prev => prev.filter(eq => eq.id !== equipmentId));
      setSelectedEquipment(null);
      setRejectionReason('');
      
      alert('Equipamento rejeitado com sucesso!');
      fetchModerationStats();
    } catch (error: any) {
      console.error('Erro ao rejeitar equipamento:', error);
      alert('Erro ao rejeitar equipamento');
    } finally {
      setProcessing(null);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { label: 'Pendente', className: 'bg-yellow-100 text-yellow-800' },
      APPROVED: { label: 'Aprovado', className: 'bg-green-100 text-green-800' },
      REJECTED: { label: 'Rejeitado', className: 'bg-red-100 text-red-800' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING;
    return <Badge className={config.className}>{config.label}</Badge>;
  };

  const formatPrice = (price: number, period: string) => {
    const periodMap = {
      HOURLY: '/hora',
      DAILY: '/dia',
      WEEKLY: '/semana',
      MONTHLY: '/mês'
    };
    return `${price.toLocaleString('pt-AO')} Kz${periodMap[period as keyof typeof periodMap] || ''}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header com Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">{stats?.pending || 0}</div>
            <div className="text-sm text-gray-600">Pendentes</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{stats?.approved || 0}</div>
            <div className="text-sm text-gray-600">Aprovados</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">{stats?.rejected || 0}</div>
            <div className="text-sm text-gray-600">Rejeitados</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{stats?.myTotal || 0}</div>
            <div className="text-sm text-gray-600">Minhas Moderações</div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros Avançados */}
      <Card>
        <CardContent className="p-4">
          <div className="space-y-4">
            {/* Primeira linha - Busca e Status */}
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Buscar por nome, descrição ou proprietário..."
                    value={filters.search}
                    onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value, page: 1 }))}
                    className="pl-10"
                  />
                </div>
              </div>
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value, page: 1 }))}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[150px]"
              >
                <option value="PENDING">🟡 Pendentes</option>
                <option value="APPROVED">✅ Aprovados</option>
                <option value="REJECTED">❌ Rejeitados</option>
                <option value="">📋 Todos</option>
              </select>
            </div>

            {/* Segunda linha - Filtros adicionais */}
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Categoria..."
                  value={filters.category}
                  onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value, page: 1 }))}
                />
              </div>
              <div className="flex gap-2">
                <Input
                  type="date"
                  placeholder="Data inicial"
                  value={filters.dateFrom}
                  onChange={(e) => setFilters(prev => ({ ...prev, dateFrom: e.target.value, page: 1 }))}
                  className="min-w-[140px]"
                />
                <Input
                  type="date"
                  placeholder="Data final"
                  value={filters.dateTo}
                  onChange={(e) => setFilters(prev => ({ ...prev, dateTo: e.target.value, page: 1 }))}
                  className="min-w-[140px]"
                />
              </div>
              <Button
                variant="outline"
                onClick={() => setFilters({
                  search: '',
                  status: 'PENDING',
                  category: '',
                  dateFrom: '',
                  dateTo: '',
                  page: 1,
                  limit: 12
                })}
                className="min-w-[100px]"
              >
                Limpar
              </Button>
            </div>

            {/* Contador de resultados */}
            <div className="text-sm text-gray-600">
              {equipments.length} equipamento(s) encontrado(s)
              {filters.status === 'PENDING' && ' pendente(s) de moderação'}
              {filters.status === 'APPROVED' && ' aprovado(s)'}
              {filters.status === 'REJECTED' && ' rejeitado(s)'}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Lista de Equipamentos */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {equipments.map((equipment) => (
          <Card key={equipment.id} className="overflow-hidden">
            <div className="aspect-video bg-gray-100 relative">
              {equipment.images && equipment.images.length > 0 ? (
                <img
                  src={equipment.images[0]}
                  alt={equipment.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400">
                  Sem imagem
                </div>
              )}
              <div className="absolute top-2 right-2">
                {getStatusBadge(equipment.moderationStatus)}
              </div>
            </div>
            
            <CardContent className="p-4">
              <h3 className="font-semibold text-lg mb-2 line-clamp-1">{equipment.name}</h3>
              
              <div className="space-y-2 text-sm text-gray-600 mb-4">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <span>{equipment.owner.companyName || equipment.owner.fullName}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  <span>{formatPrice(equipment.price, equipment.pricePeriod)}</span>
                </div>
                
                {equipment.Address && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    <span>{equipment.Address.city}, {equipment.Address.province}</span>
                  </div>
                )}
                
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>{new Date(equipment.createdAt).toLocaleDateString('pt-AO')}</span>
                </div>
              </div>

              <div className="flex gap-2">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => setSelectedEquipment(equipment)}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      Ver Detalhes
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle className="flex items-center gap-2">
                        <Package className="h-5 w-5" />
                        Detalhes do Equipamento
                        {selectedEquipment && (
                          <div className="ml-auto">
                            {getStatusBadge(selectedEquipment.moderationStatus)}
                          </div>
                        )}
                      </DialogTitle>
                    </DialogHeader>
                    {selectedEquipment && (
                      <EquipmentDetails
                        equipment={selectedEquipment}
                        onApprove={handleApprove}
                        onReject={handleReject}
                        rejectionReason={rejectionReason}
                        setRejectionReason={setRejectionReason}
                        processing={processing}
                      />
                    )}
                  </DialogContent>
                </Dialog>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {equipments.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="text-gray-500">
              {filters.status === 'PENDING' 
                ? 'Nenhum equipamento pendente de moderação' 
                : 'Nenhum equipamento encontrado'
              }
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Componente para detalhes do equipamento
function EquipmentDetails({
  equipment,
  onApprove,
  onReject,
  rejectionReason,
  setRejectionReason,
  processing
}: {
  equipment: Equipment;
  onApprove: (id: string) => void;
  onReject: (id: string) => void;
  rejectionReason: string;
  setRejectionReason: (reason: string) => void;
  processing: string | null;
}) {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  return (
    <div className="space-y-6 p-6">
      {/* Galeria de Imagens Melhorada */}
      {equipment.images && equipment.images.length > 0 ? (
        <div className="space-y-4">
          {/* Imagem Principal */}
          <div className="relative">
            <img
              src={equipment.images[selectedImageIndex]}
              alt={`${equipment.name} - Principal`}
              className="w-full h-80 object-cover rounded-lg shadow-lg"
            />
            <div className="absolute top-4 left-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
              {selectedImageIndex + 1} de {equipment.images.length}
            </div>
          </div>

          {/* Miniaturas */}
          {equipment.images.length > 1 && (
            <div className="flex gap-2 overflow-x-auto pb-2">
              {equipment.images.map((image, index) => (
                <img
                  key={index}
                  src={image}
                  alt={`${equipment.name} - ${index + 1}`}
                  className={`w-20 h-20 object-cover rounded-lg cursor-pointer transition-all ${
                    selectedImageIndex === index
                      ? 'ring-2 ring-blue-500 opacity-100'
                      : 'opacity-70 hover:opacity-100'
                  }`}
                  onClick={() => setSelectedImageIndex(index)}
                />
              ))}
            </div>
          )}
        </div>
      ) : (
        <div className="w-full h-80 bg-gray-100 rounded-lg flex items-center justify-center">
          <div className="text-center text-gray-500">
            <Package className="h-16 w-16 mx-auto mb-2" />
            <p>Nenhuma imagem disponível</p>
          </div>
        </div>
      )}

      {/* Informações em Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Card do Equipamento */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">📦 Informações do Equipamento</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="font-medium">Nome:</span>
              <span className="text-right">{equipment.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Categoria:</span>
              <span className="text-right">{equipment.category}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Preço:</span>
              <span className="text-right font-semibold text-green-600">
                {equipment.price.toLocaleString('pt-AO')} Kz/{equipment.pricePeriod}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Data de Criação:</span>
              <span className="text-right">{new Date(equipment.createdAt).toLocaleDateString('pt-AO')}</span>
            </div>
            {equipment.Address && (
              <div className="flex justify-between">
                <span className="font-medium">Localização:</span>
                <span className="text-right">{equipment.Address.city}, {equipment.Address.province}</span>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Card do Proprietário */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">👤 Informações do Proprietário</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="font-medium">Nome:</span>
              <span className="text-right">{equipment.owner.fullName}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Email:</span>
              <span className="text-right">{equipment.owner.email}</span>
            </div>
            {equipment.owner.companyName && (
              <div className="flex justify-between">
                <span className="font-medium">Empresa:</span>
                <span className="text-right">{equipment.owner.companyName}</span>
              </div>
            )}
            <div className="pt-2">
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => window.open(`mailto:${equipment.owner.email}`, '_blank')}
              >
                <Mail className="h-4 w-4 mr-2" />
                Contactar Proprietário
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Descrição Detalhada */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">📝 Descrição Completa</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
            {equipment.description || 'Nenhuma descrição fornecida.'}
          </p>
        </CardContent>
      </Card>

      {/* Ações de moderação */}
      {equipment.moderationStatus === 'PENDING' && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="text-lg text-orange-800">⚖️ Ações de Moderação</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2 text-gray-700">
                Observações da Moderação
              </label>
              <Textarea
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                placeholder="Adicione observações sobre a aprovação ou motivo da rejeição..."
                rows={4}
                className="resize-none"
              />
              <div className="text-xs text-gray-500 mt-1">
                * Obrigatório apenas para rejeição
              </div>
            </div>

            <div className="flex gap-4 pt-4">
              <Button
                onClick={() => onApprove(equipment.id)}
                disabled={processing === equipment.id}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white py-3"
                size="lg"
              >
                <Check className="h-5 w-5 mr-2" />
                {processing === equipment.id ? 'Aprovando...' : 'Aprovar Equipamento'}
              </Button>

              <Button
                onClick={() => onReject(equipment.id)}
                disabled={processing === equipment.id}
                variant="destructive"
                className="flex-1 py-3"
                size="lg"
              >
                <X className="h-5 w-5 mr-2" />
                {processing === equipment.id ? 'Rejeitando...' : 'Rejeitar Equipamento'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Histórico de Moderação (para equipamentos já moderados) */}
      {equipment.moderationStatus !== 'PENDING' && (
        <Card className={equipment.moderationStatus === 'APPROVED' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
          <CardHeader>
            <CardTitle className={`text-lg ${equipment.moderationStatus === 'APPROVED' ? 'text-green-800' : 'text-red-800'}`}>
              📋 Histórico de Moderação
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="font-medium">Status:</span>
                <span className={equipment.moderationStatus === 'APPROVED' ? 'text-green-600' : 'text-red-600'}>
                  {equipment.moderationStatus === 'APPROVED' ? '✅ Aprovado' : '❌ Rejeitado'}
                </span>
              </div>
              {equipment.moderatedAt && (
                <div className="flex justify-between">
                  <span className="font-medium">Data da Moderação:</span>
                  <span>{new Date(equipment.moderatedAt).toLocaleString('pt-AO')}</span>
                </div>
              )}
              {equipment.rejectionReason && (
                <div className="mt-3">
                  <span className="font-medium">Motivo da Rejeição:</span>
                  <p className="mt-1 text-sm text-gray-700 bg-white p-3 rounded border">
                    {equipment.rejectionReason}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
