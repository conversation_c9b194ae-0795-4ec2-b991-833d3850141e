# YMRentals - Plataforma de Aluguel de Equipamentos

## 🚀 Visão Geral

YMRentals é uma plataforma completa para aluguel de equipamentos industriais e de construção em Angola. A aplicação conecta locadores e locatários de forma segura e eficiente, oferecendo uma experiência moderna e intuitiva.

## ✨ Funcionalidades Implementadas

### 🎨 Interface e UX
- ✅ **Design Responsivo**: Interface adaptável para desktop, tablet e mobile
- ✅ **Dark Mode**: Tema escuro com cores da marca YMR (Azul: #3569b0, Vermelho: #e31c24)
- ✅ **Sistema de Rating**: Avaliações com estrelas nos cards dos equipamentos
- ✅ **Navegação Intuitiva**: Menu hamburguer otimizado e navegação fluida

### 🔐 Autenticação e Segurança
- ✅ **Sistema de Login/Registro**: Fluxo completo com validação
- ✅ **Validação de Dados**: Verificação de idade (18+), NIF, telefone angolano
- ✅ **Políticas de Privacidade**: Páginas completas de termos e privacidade
- ✅ **Proteção de Dados**: Implementação de medidas de segurança

### 🏗️ Gestão de Equipamentos
- ✅ **Catálogo Completo**: Visualização em lista, grade e cartões
- ✅ **Sistema de Filtros**: Busca avançada por categoria, preço, localização
- ✅ **Favoritos**: Sistema de marcação de equipamentos favoritos
- ✅ **Detalhes Completos**: Informações detalhadas de cada equipamento

### 💼 Sistema Administrativo
- ✅ **Back Office Completo**: Dashboard para administradores
- ✅ **Validação de Pedidos**: Sistema de prevenção de conflitos
- ✅ **Lista de Espera**: Gerenciamento de prioridades manual
- ✅ **Monitoramento**: Acompanhamento de atividades dos usuários

### 💰 Sistema Financeiro
- ✅ **Carteira Digital**: Gestão de depósitos em Kwanza
- ✅ **Sistema de Pontos**: Recompensas por avaliações e atividades
- ✅ **Pagamento de Prioridade**: Opção de pagar para ter prioridade
- ✅ **Promoção de Anúncios**: Sistema completo de promoção paga

### 💬 Comunicação
- ✅ **Chat Seguro**: Sistema de mensagens com limitações de segurança
- ✅ **Prevenção de Contato Externo**: Bloqueio automático de telefones/emails
- ✅ **Sistema de Denúncias**: Relatório de conversas inadequadas

### 📊 Analytics e Relatórios
- ✅ **Dashboard de Performance**: Métricas de uso e conversão
- ✅ **Relatórios de Promoção**: Analytics de anúncios promovidos
- ✅ **Estatísticas de Usuário**: Acompanhamento de atividades

## 🛠️ Tecnologias Utilizadas

### Frontend
- **React 18** com TypeScript
- **Tailwind CSS** para estilização
- **Lucide React** para ícones
- **React Router** para navegação
- **Vite** como bundler

### Componentes Principais
- **Layout Responsivo** com header e menu lateral
- **Sistema de Temas** (Light/Dark mode)
- **Componentes Reutilizáveis** (Button, Input, Modal)
- **Gerenciamento de Estado** com React Hooks

## 📁 Estrutura do Projeto

```
ymrentals/
├── src/
│   ├── components/          # Componentes reutilizáveis
│   │   ├── ui/             # Componentes base (Button, Input)
│   │   ├── Layout.tsx      # Layout principal
│   │   ├── ChatSystem.tsx  # Sistema de chat
│   │   ├── DigitalWallet.tsx # Carteira digital
│   │   ├── OrderValidation.tsx # Validação de pedidos
│   │   ├── WaitingList.tsx # Lista de espera
│   │   ├── PointsSystem.tsx # Sistema de pontos
│   │   └── AdPromotion.tsx # Promoção de anúncios
│   ├── pages/              # Páginas da aplicação
│   │   ├── Home.tsx        # Página inicial
│   │   ├── EquipmentCatalog.tsx # Catálogo de equipamentos
│   │   ├── Auth.tsx        # Autenticação
│   │   ├── AdminDashboard.tsx # Dashboard admin
│   │   ├── PrivacyPolicy.tsx # Política de privacidade
│   │   └── TermsOfService.tsx # Termos de uso
│   ├── data/               # Dados mockados
│   │   └── ymrentalMockData.ts
│   ├── App.tsx             # Componente principal
│   └── main.tsx           # Ponto de entrada
├── docs/                   # Documentação
│   └── LoadBalancer.md    # Documentação do Load Balance
├── public/                 # Assets públicos
└── README.md              # Este arquivo
```

## 🚀 Como Executar

### Pré-requisitos
- Node.js 18+ 
- npm ou yarn

### Instalação
```bash
# Clone o repositório
git clone https://github.com/ymrentals/ymrentals.git

# Entre no diretório
cd ymrentals

# Instale as dependências
npm install

# Execute em modo de desenvolvimento
npm run dev

# Build para produção
npm run build
```

### Acesso
- **Aplicação**: http://localhost:5173
- **Admin Dashboard**: http://localhost:5173/admin
- **Políticas**: http://localhost:5173/privacy e http://localhost:5173/terms

## 🎯 Funcionalidades por Página

### 🏠 Página Inicial (`/`)
- Hero section com busca
- Categorias em destaque
- Equipamentos populares
- Estatísticas da plataforma

### 🔍 Catálogo (`/equipment`)
- Filtros avançados (categoria, preço, localização)
- Três modos de visualização (lista, grade, cartões)
- Sistema de favoritos
- Paginação e ordenação

### 👤 Autenticação (`/auth`)
- Login e registro em etapas
- Validação de dados angolanos
- Integração com Google/Apple (mockado)
- Recuperação de senha

### ⚙️ Dashboard Admin (`/admin`)
- **Visão Geral**: Estatísticas e atividades
- **Pedidos**: Aprovação e rejeição de solicitações
- **Validação**: Sistema de prevenção de conflitos
- **Lista de Espera**: Gerenciamento de prioridades
- **Pontos**: Sistema de recompensas
- **Promoções**: Gestão de anúncios promovidos

### 💳 Carteira Digital
- Depósitos e saques em Kwanza
- Histórico de transações
- Pagamento de prioridade
- Integração com sistema de pontos

### 💬 Sistema de Chat
- Comunicação segura entre usuários
- Bloqueio automático de informações de contato
- Sistema de denúncias
- Histórico de conversas

## 🔒 Segurança Implementada

### Proteção de Dados
- Validação de entrada em todos os formulários
- Sanitização de dados do usuário
- Prevenção de XSS e injection

### Chat Seguro
- Bloqueio automático de:
  - Números de telefone
  - Endereços de email
  - Links externos
  - Referências a redes sociais
- Sistema de alertas e denúncias

### Políticas de Privacidade
- Documentação completa de coleta de dados
- Direitos do usuário (LGPD compliance)
- Procedimentos de segurança
- Contatos para exercer direitos

## 💰 Sistema de Monetização

### Taxas da Plataforma
- **Taxa de serviço**: 5% por transação
- **Taxa de processamento**: 2.5%
- **Taxa de prioridade**: 25.000 Kz

### Promoção de Anúncios
- **Destaque Básico**: 15.000 Kz (7 dias, 2x visibilidade)
- **Destaque Premium**: 35.000 Kz (15 dias, 5x visibilidade)
- **Destaque VIP**: 75.000 Kz (30 dias, 10x visibilidade)

### Sistema de Pontos
- **Avaliações**: 50 pontos por avaliação 5 estrelas
- **Aluguéis**: 100 pontos por aluguel concluído
- **Indicações**: 150 pontos por amigo indicado

## 📈 Próximos Passos

### Desenvolvimento Backend
- [ ] API REST completa
- [ ] Banco de dados PostgreSQL
- [ ] Sistema de autenticação JWT
- [ ] Integração com gateways de pagamento

### Funcionalidades Avançadas
- [ ] Notificações push
- [ ] Geolocalização e mapas
- [ ] Sistema de avaliações completo
- [ ] Integração com redes sociais

### Infraestrutura
- [ ] Deploy em produção
- [ ] Load balancer (documentação em `/docs/LoadBalancer.md`)
- [ ] CDN para assets
- [ ] Monitoramento e logs

### Mobile
- [ ] App React Native
- [ ] Notificações push nativas
- [ ] Funcionalidades offline

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 📞 Contato

**YMRentals Team**
- Email: <EMAIL>
- Telefone: +244 999 999 999
- Endereço: Luanda, Kilamba, Bloco X, Edifício 35, 3º andar, porta 36

---

**Desenvolvido com ❤️ para Angola** 🇦🇴
