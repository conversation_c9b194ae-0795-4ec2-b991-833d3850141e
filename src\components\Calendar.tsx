import React, { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from './ui/button';

interface CalendarProps {
  onDateSelect?: (date: Date) => void;
  minDate?: Date;
  maxDate?: Date;
  initialDate?: Date;
  isDarkMode?: boolean;
}

const Calendar: React.FC<CalendarProps> = ({
  onDateSelect,
  minDate = new Date(),
  maxDate,
  initialDate = new Date(),
  isDarkMode = false,
}) => {
  const [currentDate, setCurrentDate] = useState(initialDate);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  // Get current month and year
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();

  // Get days in month
  const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
  
  // Get first day of month
  const firstDayOfMonth = new Date(currentYear, currentMonth, 1).getDay();

  // Month names
  const monthNames = [
    'Janeiro', 'Fevereiro', 'Mar<PERSON><PERSON>', 'Abril', '<PERSON><PERSON>', 'Jun<PERSON>',
    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
  ];

  // Day names
  const dayNames = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];

  // Navigate to previous month
  const prevMonth = () => {
    setCurrentDate(new Date(currentYear, currentMonth - 1, 1));
  };

  // Navigate to next month
  const nextMonth = () => {
    setCurrentDate(new Date(currentYear, currentMonth + 1, 1));
  };

  // Handle date selection
  const handleDateSelect = (day: number) => {
    const selectedDate = new Date(currentYear, currentMonth, day);
    setSelectedDate(selectedDate);
    if (onDateSelect) {
      onDateSelect(selectedDate);
    }
  };

  // Check if date is selectable
  const isDateSelectable = (day: number) => {
    const date = new Date(currentYear, currentMonth, day);
    
    if (minDate && date < minDate) {
      return false;
    }
    
    if (maxDate && date > maxDate) {
      return false;
    }
    
    return true;
  };

  // Check if date is selected
  const isDateSelected = (day: number) => {
    if (!selectedDate) return false;
    
    return (
      selectedDate.getDate() === day &&
      selectedDate.getMonth() === currentMonth &&
      selectedDate.getFullYear() === currentYear
    );
  };

  // Generate calendar days
  const calendarDays = [];
  
  // Add empty cells for days before the first day of the month
  for (let i = 0; i < firstDayOfMonth; i++) {
    calendarDays.push(<div key={`empty-${i}`} className="h-10"></div>);
  }
  
  // Add days of the month
  for (let day = 1; day <= daysInMonth; day++) {
    const isSelectable = isDateSelectable(day);
    const isSelected = isDateSelected(day);
    
    calendarDays.push(
      <button
        key={`day-${day}`}
        onClick={() => isSelectable && handleDateSelect(day)}
        disabled={!isSelectable}
        className={`h-10 w-10 rounded-full flex items-center justify-center transition-colors ${
          isSelected
            ? 'bg-cyan-500 text-white'
            : isSelectable
            ? `${isDarkMode ? 'hover:bg-gray-700 text-white' : 'hover:bg-gray-100 text-gray-800'}`
            : `${isDarkMode ? 'text-gray-600' : 'text-gray-400'} cursor-not-allowed`
        }`}
      >
        {day}
      </button>
    );
  }

  return (
    <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-neutral-800 text-white' : 'bg-white text-gray-800'}`}>
      {/* Calendar header */}
      <div className="flex justify-between items-center mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={prevMonth}
          className={isDarkMode ? 'text-white hover:bg-gray-700' : 'text-gray-800 hover:bg-gray-100'}
        >
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <h3 className="font-medium">
          {monthNames[currentMonth]} {currentYear}
        </h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={nextMonth}
          className={isDarkMode ? 'text-white hover:bg-gray-700' : 'text-gray-800 hover:bg-gray-100'}
        >
          <ChevronRight className="h-5 w-5" />
        </Button>
      </div>
      
      {/* Day names */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {dayNames.map((day) => (
          <div key={day} className="h-10 flex items-center justify-center text-sm font-medium text-gray-500">
            {day}
          </div>
        ))}
      </div>
      
      {/* Calendar grid */}
      <div className="grid grid-cols-7 gap-1">
        {calendarDays}
      </div>
    </div>
  );
};

export default Calendar;
