const API_BASE_URL = 'http://localhost:3000';

class AdminApiService {
  private getAuthHeaders() {
    const token = localStorage.getItem('authToken');
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    };
  }

  private async handleResponse(response: Response) {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }
    return response.json();
  }

  // ===== DASHBOARD =====
  async getDashboardData() {
    const response = await fetch(`${API_BASE_URL}/admin/dashboard`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getStats() {
    const response = await fetch(`${API_BASE_URL}/admin/stats`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getRecentActivities() {
    const response = await fetch(`${API_BASE_URL}/admin/recent-activities`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // ===== USER MANAGEMENT =====
  async getUsers(params: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    userType?: string;
  } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await fetch(`${API_BASE_URL}/admin/users?${queryParams}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getUserDetails(userId: string) {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async updateUser(userId: string, updateData: any) {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(updateData),
    });
    return this.handleResponse(response);
  }

  async toggleUserBlock(userId: string, blocked: boolean, reason?: string) {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}/block`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ blocked, reason }),
    });
    return this.handleResponse(response);
  }

  async deleteUser(userId: string) {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // ===== LANDLORD VALIDATION =====
  async getPendingLandlords() {
    const response = await fetch(`${API_BASE_URL}/admin/pending-landlords`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async validateLandlord(landlordId: string, approved: boolean, reason?: string) {
    const response = await fetch(`${API_BASE_URL}/admin/validate-landlord/${landlordId}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ approved, reason }),
    });
    return this.handleResponse(response);
  }

  // ===== MODERATOR MANAGEMENT =====
  async getModerators() {
    const response = await fetch(`${API_BASE_URL}/admin/moderators`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async createModerator(moderatorData: {
    email: string;
    password: string;
    fullName: string;
    phoneNumber: string;
    dateOfBirth: string;
    role: 'MODERATOR' | 'MODERATOR_MANAGER';
  }) {
    const response = await fetch(`${API_BASE_URL}/admin/moderators`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(moderatorData),
    });
    return this.handleResponse(response);
  }

  async updateModerator(moderatorId: string, updateData: any) {
    const response = await fetch(`${API_BASE_URL}/admin/moderators/${moderatorId}`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(updateData),
    });
    return this.handleResponse(response);
  }

  async deleteModerator(moderatorId: string) {
    const response = await fetch(`${API_BASE_URL}/admin/moderators/${moderatorId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // ===== EQUIPMENT MODERATION =====
  async getPendingEquipment() {
    const response = await fetch(`${API_BASE_URL}/moderation/pending-equipment`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async validateEquipment(equipmentId: string, approved: boolean, reason?: string) {
    const response = await fetch(`${API_BASE_URL}/moderation/validate-equipment/${equipmentId}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ approved, reason }),
    });
    return this.handleResponse(response);
  }

  // ===== CATEGORIES =====
  async getCategories(includeInactive = false) {
    const response = await fetch(`${API_BASE_URL}/categories?includeInactive=${includeInactive}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async createCategory(categoryData: {
    name: string;
    description: string;
    image?: string;
  }) {
    const response = await fetch(`${API_BASE_URL}/categories`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(categoryData),
    });
    return this.handleResponse(response);
  }

  async updateCategory(categoryId: string, updateData: any) {
    const response = await fetch(`${API_BASE_URL}/categories/${categoryId}`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(updateData),
    });
    return this.handleResponse(response);
  }

  async deleteCategory(categoryId: string) {
    const response = await fetch(`${API_BASE_URL}/categories/${categoryId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // ===== NOTIFICATIONS =====
  async getNotifications(params: {
    page?: number;
    limit?: number;
    type?: string;
    isRead?: boolean;
  } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await fetch(`${API_BASE_URL}/notifications?${queryParams}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async markNotificationAsRead(notificationId: string) {
    const response = await fetch(`${API_BASE_URL}/notifications/${notificationId}/read`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async markAllNotificationsAsRead() {
    const response = await fetch(`${API_BASE_URL}/notifications/mark-all-read`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // ===== CONTENT MANAGEMENT =====
  async getContent(key?: string) {
    const url = key ? `${API_BASE_URL}/content/${key}` : `${API_BASE_URL}/content`;
    const response = await fetch(url, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async updateContent(key: string, contentData: {
    title: string;
    content: string;
    isActive: boolean;
  }) {
    const response = await fetch(`${API_BASE_URL}/content/${key}`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(contentData),
    });
    return this.handleResponse(response);
  }

  async createContent(contentData: {
    key: string;
    title: string;
    content: string;
    isActive: boolean;
  }) {
    const response = await fetch(`${API_BASE_URL}/content`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(contentData),
    });
    return this.handleResponse(response);
  }
}

export const adminApi = new AdminApiService();
export default adminApi;
