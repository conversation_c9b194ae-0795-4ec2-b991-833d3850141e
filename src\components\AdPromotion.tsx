import React, { useState } from 'react';
import { 
  TrendingUp, 
  Eye, 
  Star, 
  Calendar, 
  CreditCard, 
  Target,
  BarChart3,
  Zap,
  Crown,
  ArrowUp,
  CheckCircle,
  Clock
} from 'lucide-react';
import { Button } from './ui/button';

interface PromotionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: number; // em dias
  features: string[];
  boost: number; // multiplicador de visibilidade
  priority: 'basic' | 'premium' | 'vip';
  color: string;
  icon: string;
}

interface ActivePromotion {
  id: string;
  equipmentId: string;
  equipmentName: string;
  planId: string;
  planName: string;
  startDate: string;
  endDate: string;
  totalViews: number;
  totalClicks: number;
  totalInquiries: number;
  amountPaid: number;
  status: 'active' | 'expired' | 'paused';
}

interface AdPromotionProps {
  isDarkMode: boolean;
}

const AdPromotion: React.FC<AdPromotionProps> = ({ isDarkMode }) => {
  const [selectedEquipment, setSelectedEquipment] = useState('');
  const [selectedPlan, setSelectedPlan] = useState('');
  const [activeTab, setActiveTab] = useState<'promote' | 'active' | 'analytics'>('promote');

  const [promotionPlans] = useState<PromotionPlan[]>([
    {
      id: 'basic',
      name: 'Destaque Básico',
      description: 'Apareça nas primeiras posições por 7 dias',
      price: 15000,
      duration: 7,
      features: [
        'Posição destacada nos resultados',
        'Badge "Em Destaque"',
        'Relatório básico de performance',
        '2x mais visibilidade'
      ],
      boost: 2,
      priority: 'basic',
      color: 'blue',
      icon: 'star'
    },
    {
      id: 'premium',
      name: 'Destaque Premium',
      description: 'Máxima visibilidade por 15 dias',
      price: 35000,
      duration: 15,
      features: [
        'Topo dos resultados garantido',
        'Badge "Premium"',
        'Destaque na página inicial',
        'Relatório detalhado',
        '5x mais visibilidade',
        'Suporte prioritário'
      ],
      boost: 5,
      priority: 'premium',
      color: 'purple',
      icon: 'crown'
    },
    {
      id: 'vip',
      name: 'Destaque VIP',
      description: 'Exposição máxima por 30 dias',
      price: 75000,
      duration: 30,
      features: [
        'Posição #1 garantida',
        'Badge "VIP"',
        'Banner na página inicial',
        'Push notifications',
        'Relatório completo',
        '10x mais visibilidade',
        'Gerente de conta dedicado'
      ],
      boost: 10,
      priority: 'vip',
      color: 'gold',
      icon: 'zap'
    }
  ]);

  const [activePromotions, setActivePromotions] = useState<ActivePromotion[]>([
    {
      id: '1',
      equipmentId: 'eq1',
      equipmentName: 'Escavadeira CAT 320',
      planId: 'premium',
      planName: 'Destaque Premium',
      startDate: '2024-01-10',
      endDate: '2024-01-25',
      totalViews: 2450,
      totalClicks: 180,
      totalInquiries: 25,
      amountPaid: 35000,
      status: 'active'
    },
    {
      id: '2',
      equipmentId: 'eq2',
      equipmentName: 'Gerador 100kVA',
      planId: 'basic',
      planName: 'Destaque Básico',
      startDate: '2024-01-08',
      endDate: '2024-01-15',
      totalViews: 890,
      totalClicks: 65,
      totalInquiries: 8,
      amountPaid: 15000,
      status: 'expired'
    }
  ]);

  // Lista de equipamentos do usuário (mockada)
  const userEquipments = [
    { id: 'eq1', name: 'Escavadeira CAT 320' },
    { id: 'eq2', name: 'Gerador 100kVA' },
    { id: 'eq3', name: 'Compressor de Ar' },
    { id: 'eq4', name: 'Betoneira 400L' }
  ];

  // Função para promover anúncio
  const promoteAd = () => {
    if (!selectedEquipment || !selectedPlan) {
      alert('Selecione um equipamento e um plano');
      return;
    }

    const plan = promotionPlans.find(p => p.id === selectedPlan);
    const equipment = userEquipments.find(e => e.id === selectedEquipment);
    
    if (!plan || !equipment) return;

    const endDate = new Date();
    endDate.setDate(endDate.getDate() + plan.duration);

    const newPromotion: ActivePromotion = {
      id: Date.now().toString(),
      equipmentId: selectedEquipment,
      equipmentName: equipment.name,
      planId: selectedPlan,
      planName: plan.name,
      startDate: new Date().toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
      totalViews: 0,
      totalClicks: 0,
      totalInquiries: 0,
      amountPaid: plan.price,
      status: 'active'
    };

    setActivePromotions(prev => [newPromotion, ...prev]);
    setSelectedEquipment('');
    setSelectedPlan('');
    
    alert(`Anúncio promovido com sucesso! Plano: ${plan.name}`);
  };

  // Função para formatar preço
  const formatPrice = (price: number): string => {
    return price.toLocaleString('pt-AO') + ' Kz';
  };

  // Função para obter ícone do plano
  const getPlanIcon = (iconType: string) => {
    switch (iconType) {
      case 'star': return <Star className="w-6 h-6" />;
      case 'crown': return <Crown className="w-6 h-6" />;
      case 'zap': return <Zap className="w-6 h-6" />;
      default: return <Star className="w-6 h-6" />;
    }
  };

  // Função para obter cor do plano
  const getPlanColor = (priority: string) => {
    switch (priority) {
      case 'basic': return 'border-blue-500 bg-blue-50 dark:bg-blue-900/20';
      case 'premium': return 'border-purple-500 bg-purple-50 dark:bg-purple-900/20';
      case 'vip': return 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
      default: return 'border-gray-500 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  // Função para obter cor do status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'expired': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'paused': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  // Calcular estatísticas gerais
  const totalInvestment = activePromotions.reduce((sum, promo) => sum + promo.amountPaid, 0);
  const totalViews = activePromotions.reduce((sum, promo) => sum + promo.totalViews, 0);
  const totalInquiries = activePromotions.reduce((sum, promo) => sum + promo.totalInquiries, 0);
  const conversionRate = totalViews > 0 ? ((totalInquiries / totalViews) * 100).toFixed(1) : '0';

  return (
    <div className={`${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'} min-h-screen p-6`}>
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Promoção de Anúncios</h1>
          <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Aumente a visibilidade dos seus equipamentos e receba mais pedidos
          </p>
        </div>

        {/* Estatísticas gerais */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Investimento Total</p>
                <p className="text-2xl font-bold">{formatPrice(totalInvestment)}</p>
              </div>
              <CreditCard className="w-8 h-8 text-blue-500" style={{color: '#3569b0'}} />
            </div>
          </div>
          
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total de Visualizações</p>
                <p className="text-2xl font-bold">{totalViews.toLocaleString()}</p>
              </div>
              <Eye className="w-8 h-8 text-green-500" />
            </div>
          </div>
          
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Consultas Recebidas</p>
                <p className="text-2xl font-bold">{totalInquiries}</p>
              </div>
              <Target className="w-8 h-8 text-purple-500" />
            </div>
          </div>
          
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Taxa de Conversão</p>
                <p className="text-2xl font-bold">{conversionRate}%</p>
              </div>
              <BarChart3 className="w-8 h-8 text-orange-500" />
            </div>
          </div>
        </div>

        {/* Navegação por abas */}
        <div className="mb-6">
          <div className="flex space-x-1 bg-gray-200 dark:bg-gray-800 p-1 rounded-lg">
            {[
              { key: 'promote', label: 'Promover Anúncio', icon: TrendingUp },
              { key: 'active', label: 'Promoções Ativas', icon: Clock },
              { key: 'analytics', label: 'Análises', icon: BarChart3 }
            ].map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                  activeTab === tab.key
                    ? 'bg-white dark:bg-gray-700 shadow-sm'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
                style={{
                  backgroundColor: activeTab === tab.key ? '#3569b0' : undefined,
                  color: activeTab === tab.key ? 'white' : undefined
                }}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Conteúdo das abas */}
        {activeTab === 'promote' && (
          <div className="space-y-8">
            {/* Seleção de equipamento */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
              <h3 className="text-lg font-semibold mb-4">Selecione o Equipamento</h3>
              <select
                value={selectedEquipment}
                onChange={(e) => setSelectedEquipment(e.target.value)}
                className={`w-full p-3 border rounded-md ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'}`}
              >
                <option value="">Escolha um equipamento</option>
                {userEquipments.map(equipment => (
                  <option key={equipment.id} value={equipment.id}>
                    {equipment.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Planos de promoção */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Escolha seu Plano</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {promotionPlans.map(plan => (
                  <div
                    key={plan.id}
                    onClick={() => setSelectedPlan(plan.id)}
                    className={`cursor-pointer p-6 rounded-lg border-2 transition-all ${
                      selectedPlan === plan.id 
                        ? getPlanColor(plan.priority) + ' border-opacity-100' 
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                    } ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}
                  >
                    <div className="text-center mb-4">
                      {getPlanIcon(plan.icon)}
                      <h4 className="text-xl font-bold mt-2">{plan.name}</h4>
                      <p className="text-gray-500 text-sm">{plan.description}</p>
                    </div>
                    
                    <div className="text-center mb-4">
                      <span className="text-3xl font-bold">{formatPrice(plan.price)}</span>
                      <p className="text-sm text-gray-500">por {plan.duration} dias</p>
                    </div>
                    
                    <ul className="space-y-2 mb-4">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-center text-sm">
                          <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    
                    <div className="text-center">
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                        plan.priority === 'basic' ? 'bg-blue-100 text-blue-800' :
                        plan.priority === 'premium' ? 'bg-purple-100 text-purple-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        <ArrowUp className="w-3 h-3 mr-1" />
                        {plan.boost}x mais visibilidade
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Botão de promoção */}
            <div className="text-center">
              <Button
                onClick={promoteAd}
                disabled={!selectedEquipment || !selectedPlan}
                className="px-8 py-3 text-lg"
                style={{backgroundColor: '#3569b0'}}
              >
                <TrendingUp className="w-5 h-5 mr-2" />
                Promover Anúncio
              </Button>
            </div>
          </div>
        )}

        {activeTab === 'active' && (
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md overflow-hidden`}>
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold">Promoções Ativas e Históricas</h3>
            </div>
            
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Equipamento
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Plano
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Período
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Visualizações
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Consultas
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Investimento
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {activePromotions.map((promotion) => (
                    <tr key={promotion.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium">{promotion.equipmentName}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm">{promotion.planName}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm">
                          {new Date(promotion.startDate).toLocaleDateString('pt-AO')} - {new Date(promotion.endDate).toLocaleDateString('pt-AO')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-sm">
                          <Eye className="w-4 h-4 mr-1 text-gray-400" />
                          {promotion.totalViews.toLocaleString()}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-sm">
                          <Target className="w-4 h-4 mr-1 text-gray-400" />
                          {promotion.totalInquiries}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium">{formatPrice(promotion.amountPaid)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(promotion.status)}`}>
                          {promotion.status === 'active' ? 'Ativo' : promotion.status === 'expired' ? 'Expirado' : 'Pausado'}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
              <h3 className="text-lg font-semibold mb-4">Performance por Plano</h3>
              <div className="space-y-4">
                {promotionPlans.map(plan => {
                  const planPromotions = activePromotions.filter(p => p.planId === plan.id);
                  const planViews = planPromotions.reduce((sum, p) => sum + p.totalViews, 0);
                  const planInquiries = planPromotions.reduce((sum, p) => sum + p.totalInquiries, 0);
                  const planConversion = planViews > 0 ? ((planInquiries / planViews) * 100).toFixed(1) : '0';
                  
                  return (
                    <div key={plan.id} className="flex items-center justify-between p-3 border rounded border-gray-200 dark:border-gray-700">
                      <div className="flex items-center space-x-3">
                        {getPlanIcon(plan.icon)}
                        <div>
                          <p className="font-medium">{plan.name}</p>
                          <p className="text-sm text-gray-500">{planPromotions.length} promoções</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{planConversion}%</p>
                        <p className="text-sm text-gray-500">conversão</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
            
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
              <h3 className="text-lg font-semibold mb-4">ROI por Equipamento</h3>
              <div className="space-y-4">
                {userEquipments.map(equipment => {
                  const equipmentPromotions = activePromotions.filter(p => p.equipmentId === equipment.id);
                  const totalInvestment = equipmentPromotions.reduce((sum, p) => sum + p.amountPaid, 0);
                  const totalInquiries = equipmentPromotions.reduce((sum, p) => sum + p.totalInquiries, 0);
                  
                  return (
                    <div key={equipment.id} className="flex items-center justify-between p-3 border rounded border-gray-200 dark:border-gray-700">
                      <div>
                        <p className="font-medium">{equipment.name}</p>
                        <p className="text-sm text-gray-500">{formatPrice(totalInvestment)} investido</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{totalInquiries}</p>
                        <p className="text-sm text-gray-500">consultas</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdPromotion;
