import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';

interface MapLocation {
  id: string;
  name: string;
  count: number;
  lat: number;
  lng: number;
  type: 'province' | 'municipality';
  parentId?: string;
}

interface MapWithPinsProps {
  isDarkMode: boolean;
  selectedProvince: string | null;
  onProvinceSelect: (provinceId: string) => void;
  onMunicipalitySelect: (municipalityId: string) => void;
  onBackClick: () => void;
}

const MapWithPins: React.FC<MapWithPinsProps> = ({
  isDarkMode,
  selectedProvince,
  onProvinceSelect,
  onMunicipalitySelect,
  onBackClick
}) => {
  // Coordenadas reais das províncias de Angola
  const provinces: MapLocation[] = [
    { id: 'luanda', name: 'Luanda', count: 145, lat: -8.8368, lng: 13.2343, type: 'province' },
    { id: 'benguela', name: '<PERSON><PERSON><PERSON>', count: 87, lat: -12.5763, lng: 13.4055, type: 'province' },
    { id: 'huambo', name: '<PERSON><PERSON><PERSON>', count: 56, lat: -12.7761, lng: 15.7392, type: 'province' },
    { id: 'huila', name: 'Huíla', count: 42, lat: -14.9226, lng: 14.6569, type: 'province' },
    { id: 'cabinda', name: 'Cabinda', count: 23, lat: -5.5496, lng: 12.1955, type: 'province' },
    { id: 'malanje', name: 'Malanje', count: 35, lat: -9.5402, lng: 16.3534, type: 'province' },
    { id: 'uige', name: 'Uíge', count: 28, lat: -7.6087, lng: 15.0613, type: 'province' },
    { id: 'zaire', name: 'Zaire', count: 19, lat: -6.1349, lng: 12.9992, type: 'province' },
    { id: 'cuanza-norte', name: 'Cuanza Norte', count: 25, lat: -9.2366, lng: 14.6574, type: 'province' },
    { id: 'cuanza-sul', name: 'Cuanza Sul', count: 31, lat: -10.7031, lng: 15.0613, type: 'province' },
    { id: 'lunda-norte', name: 'Lunda Norte', count: 22, lat: -8.4192, lng: 20.7369, type: 'province' },
    { id: 'lunda-sul', name: 'Lunda Sul', count: 18, lat: -10.2864, lng: 20.0371, type: 'province' },
    { id: 'moxico', name: 'Moxico', count: 15, lat: -13.4557, lng: 20.3799, type: 'province' },
    { id: 'bie', name: 'Bié', count: 27, lat: -12.5763, lng: 17.6747, type: 'province' },
    { id: 'namibe', name: 'Namibe', count: 20, lat: -15.1968, lng: 12.1522, type: 'province' },
    { id: 'cunene', name: 'Cunene', count: 16, lat: -17.0657, lng: 15.7333, type: 'province' },
    { id: 'cuando-cubango', name: 'Cuando Cubango', count: 12, lat: -16.7428, lng: 18.3358, type: 'province' },
    { id: 'bengo', name: 'Bengo', count: 24, lat: -8.7526, lng: 13.7169, type: 'province' },
  ];

  // Coordenadas reais dos municípios
  const municipalities: MapLocation[] = [
    // Luanda
    { id: 'luanda-city', name: 'Luanda', count: 78, lat: -8.8368, lng: 13.2343, type: 'municipality', parentId: 'luanda' },
    { id: 'viana', name: 'Viana', count: 32, lat: -8.9075, lng: 13.3630, type: 'municipality', parentId: 'luanda' },
    { id: 'cacuaco', name: 'Cacuaco', count: 18, lat: -8.7785, lng: 13.3722, type: 'municipality', parentId: 'luanda' },
    { id: 'belas', name: 'Belas', count: 17, lat: -9.0686, lng: 13.1607, type: 'municipality', parentId: 'luanda' },
    { id: 'icolo-bengo', name: 'Icolo e Bengo', count: 12, lat: -9.0848, lng: 13.7169, type: 'municipality', parentId: 'luanda' },

    // Benguela
    { id: 'benguela-city', name: 'Benguela', count: 45, lat: -12.5763, lng: 13.4055, type: 'municipality', parentId: 'benguela' },
    { id: 'lobito', name: 'Lobito', count: 32, lat: -12.3481, lng: 13.5456, type: 'municipality', parentId: 'benguela' },
    { id: 'catumbela', name: 'Catumbela', count: 10, lat: -12.4300, lng: 13.5500, type: 'municipality', parentId: 'benguela' },

    // Huambo
    { id: 'huambo-city', name: 'Huambo', count: 38, lat: -12.7761, lng: 15.7392, type: 'municipality', parentId: 'huambo' },
    { id: 'caala', name: 'Caála', count: 18, lat: -12.8522, lng: 15.5606, type: 'municipality', parentId: 'huambo' },

    // Cabinda
    { id: 'cabinda-city', name: 'Cabinda', count: 15, lat: -5.5496, lng: 12.1955, type: 'municipality', parentId: 'cabinda' },
  ];

  // Filtrar locais com base na seleção
  const displayLocations = selectedProvince
    ? municipalities.filter(m => m.parentId === selectedProvince)
    : provinces;

  // Função para converter coordenadas geográficas para posição na tela
  const geoToPixel = (lat: number, lng: number) => {
    // Limites aproximados do mapa de Angola ajustados para o iframe
    const mapBounds = {
      north: -5.0, // Latitude norte
      south: -18.0, // Latitude sul
      west: 11.0, // Longitude oeste
      east: 24.5 // Longitude leste
    };

    // Calcular a posição percentual no mapa
    const x = ((lng - mapBounds.west) / (mapBounds.east - mapBounds.west)) * 100;
    const y = ((lat - mapBounds.north) / (mapBounds.south - mapBounds.north)) * 100;

    return { x, y };
  };

  return (
    <div className={`relative rounded-lg overflow-hidden ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h3 className="text-lg font-semibold">
          {selectedProvince
            ? `Equipamentos em ${provinces.find(p => p.id === selectedProvince)?.name}`
            : 'Pesquisar no Mapa'}
        </h3>
        {selectedProvince && (
          <button
            onClick={onBackClick}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            Voltar ao mapa de Angola
          </button>
        )}
      </div>

      <div className="relative" style={{ height: '600px' }}>
        {/* Mapa de Angola com pins */}
        <div className="relative w-full h-full">
          {/* Mapa interativo */}
          <iframe
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d8099881.771693946!2d12.8015811!3d-13.0568368!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1a51f24ecaad8b27%3A0x590a289d0d4a4e3d!2sAngola!5e0!3m2!1spt-PT!2sao!4v1710371547387!5m2!1spt-PT!2sao"
            width="100%"
            height="100%"
            style={{ border: 0 }}
            allowFullScreen
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
          ></iframe>

          {/* Overlay para pins */}
          <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
            {displayLocations.map((location) => {
              const position = geoToPixel(location.lat, location.lng);
              return (
                <div
                  key={location.id}
                  className="absolute transform -translate-x-1/2 -translate-y-1/2 pointer-events-auto cursor-pointer"
                  style={{
                    left: `${position.x}%`,
                    top: `${position.y}%`,
                    zIndex: 1000
                  }}
                  onClick={() => {
                    if (location.type === 'province') {
                      onProvinceSelect(location.id);
                    } else {
                      onMunicipalitySelect(location.id);
                    }
                  }}
                >
                  <div className="flex flex-col items-center">
                    <div className={`flex items-center justify-center w-12 h-12 rounded-full ${isDarkMode ? 'bg-blue-600' : 'bg-blue-500'} text-white font-semibold shadow-lg text-lg`}>
                      {location.count}
                    </div>
                    <div className={`mt-1 px-2 py-1 rounded text-sm font-medium ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'} shadow`}>
                      {location.name}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          Clique em uma província para ver os equipamentos disponíveis em cada município.
        </p>
      </div>
    </div>
  );
};

export default MapWithPins;
