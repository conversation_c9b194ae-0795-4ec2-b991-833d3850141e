import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from './AuthContext';
import apiService from '../services/api';

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR';
  data?: any;
  isRead: boolean;
  createdAt: string;
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  isConnected: boolean;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;
  loadNotifications: (page?: number, limit?: number) => Promise<void>;
  loading: boolean;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const { isAuthenticated, user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isConnected, setIsConnected] = useState(false);
  const [loading, setLoading] = useState(false);
  const [socket, setSocket] = useState<Socket | null>(null);

  // Conectar ao WebSocket quando o usuário estiver autenticado
  useEffect(() => {
    if (isAuthenticated && user) {
      const token = localStorage.getItem('authToken') || localStorage.getItem('accessToken');
      if (token) {
        const newSocket = io('http://localhost:3000/notifications', {
          auth: { token },
          transports: ['websocket', 'polling'],
        });

        newSocket.on('connect', () => {
          console.log('Conectado ao sistema de notificações');
          setIsConnected(true);
        });

        newSocket.on('disconnect', () => {
          console.log('Desconectado do sistema de notificações');
          setIsConnected(false);
        });

        newSocket.on('new_notification', (notification: Notification) => {
          console.log('Nova notificação recebida:', notification);
          setNotifications(prev => [notification, ...prev]);
          setUnreadCount(prev => prev + 1);
          
          // Mostrar notificação do browser se permitido
          if (Notification.permission === 'granted') {
            new Notification(notification.title, {
              body: notification.message,
              icon: '/favicon.ico',
            });
          }
        });

        newSocket.on('notification_marked_read', ({ notificationId }) => {
          setNotifications(prev =>
            prev.map(notif =>
              notif.id === notificationId ? { ...notif, isRead: true } : notif
            )
          );
          setUnreadCount(prev => Math.max(0, prev - 1));
        });

        newSocket.on('unread_count', ({ count }) => {
          setUnreadCount(count);
        });

        setSocket(newSocket);

        // Carregar notificações iniciais
        loadNotifications();
        loadUnreadCount();

        return () => {
          newSocket.disconnect();
          setSocket(null);
          setIsConnected(false);
        };
      }
    } else {
      // Limpar estado quando não autenticado
      setNotifications([]);
      setUnreadCount(0);
      setIsConnected(false);
      if (socket) {
        socket.disconnect();
        setSocket(null);
      }
    }
  }, [isAuthenticated, user]);

  // Solicitar permissão para notificações do browser
  useEffect(() => {
    if (isAuthenticated && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, [isAuthenticated]);

  const loadNotifications = async (page = 1, limit = 20) => {
    if (!isAuthenticated) return;
    
    try {
      setLoading(true);
      const response = await apiService.getNotifications(page, limit);
      setNotifications(response.data || []);
    } catch (error) {
      console.error('Erro ao carregar notificações:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadUnreadCount = async () => {
    if (!isAuthenticated) return;
    
    try {
      const response = await apiService.getUnreadNotificationsCount();
      setUnreadCount(response.count || 0);
    } catch (error) {
      console.error('Erro ao carregar contagem de não lidas:', error);
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      await apiService.markNotificationAsRead(notificationId);
      
      setNotifications(prev =>
        prev.map(notif =>
          notif.id === notificationId ? { ...notif, isRead: true } : notif
        )
      );
      
      setUnreadCount(prev => Math.max(0, prev - 1));
      
      // Notificar via WebSocket
      if (socket) {
        socket.emit('mark_notification_read', { notificationId });
      }
    } catch (error) {
      console.error('Erro ao marcar notificação como lida:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      await apiService.markAllNotificationsAsRead();
      
      setNotifications(prev =>
        prev.map(notif => ({ ...notif, isRead: true }))
      );
      
      setUnreadCount(0);
    } catch (error) {
      console.error('Erro ao marcar todas como lidas:', error);
    }
  };

  const deleteNotification = async (notificationId: string) => {
    try {
      await apiService.deleteNotification(notificationId);
      
      const notification = notifications.find(n => n.id === notificationId);
      if (notification && !notification.isRead) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
      
      setNotifications(prev => prev.filter(notif => notif.id !== notificationId));
    } catch (error) {
      console.error('Erro ao deletar notificação:', error);
    }
  };

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    isConnected,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    loadNotifications,
    loading,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};
