import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowLeft,
  Search,
  ChevronDown,
  ChevronRight,
  MessageCircle,
  Phone,
  Mail,
  Book,
  HelpCircle,
  Settings,
  CreditCard,
  Shield,
  Users,
  Truck
} from 'lucide-react';
import { Button } from '../components/ui/button';

interface HelpProps {
  isDarkMode: boolean;
}

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
}

const Help: React.FC<HelpProps> = ({ isDarkMode }) => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);
  const [activeCategory, setActiveCategory] = useState<string>('all');

  const faqItems: FAQItem[] = [
    {
      id: '1',
      question: 'Como faço para alugar um equipamento?',
      answer: 'Para alugar um equipamento, navegue pelo catálogo, selecione o equipamento desejado, escolha as datas e clique em "Alugar". Você será direcionado para o processo de pagamento e confirmação.',
      category: 'rental'
    },
    {
      id: '2',
      question: 'Quais são as formas de pagamento aceitas?',
      answer: 'Aceitamos transferência bancária, pagamento via multicaixa, e cartões de crédito/débito. O pagamento deve ser feito antes da entrega do equipamento.',
      category: 'payment'
    },
    {
      id: '3',
      question: 'Como posso cancelar uma reserva?',
      answer: 'Você pode cancelar uma reserva até 24 horas antes da data de início. Acesse "Minhas Rendas" e clique em "Cancelar" na reserva desejada. Reembolsos seguem nossa política de cancelamento.',
      category: 'rental'
    },
    {
      id: '4',
      question: 'O que está incluído no aluguel?',
      answer: 'O aluguel inclui o equipamento, seguro básico, suporte técnico 24/7 e entrega/recolha (quando aplicável). Combustível e operador são cobrados separadamente.',
      category: 'rental'
    },
    {
      id: '5',
      question: 'Como funciona a entrega dos equipamentos?',
      answer: 'Oferecemos entrega em Luanda e arredores. O custo varia conforme a distância. A entrega é agendada conforme disponibilidade e confirmada 24h antes.',
      category: 'delivery'
    },
    {
      id: '6',
      question: 'Posso estender o período de aluguel?',
      answer: 'Sim, você pode solicitar extensão através do app ou entrando em contato conosco. A extensão está sujeita à disponibilidade do equipamento.',
      category: 'rental'
    },
    {
      id: '7',
      question: 'O que fazer em caso de problema com o equipamento?',
      answer: 'Entre em contato imediatamente através do suporte 24/7. Temos equipe técnica disponível para resolver problemas rapidamente.',
      category: 'support'
    },
    {
      id: '8',
      question: 'Como me tornar um locador na plataforma?',
      answer: 'Cadastre-se como locador, envie documentação do equipamento, passe pela verificação e comece a receber pedidos de aluguel.',
      category: 'account'
    }
  ];

  const categories = [
    { key: 'all', label: 'Todas', icon: Book },
    { key: 'rental', label: 'Aluguel', icon: Settings },
    { key: 'payment', label: 'Pagamento', icon: CreditCard },
    { key: 'delivery', label: 'Entrega', icon: Truck },
    { key: 'support', label: 'Suporte', icon: HelpCircle },
    { key: 'account', label: 'Conta', icon: Users }
  ];

  const filteredFAQs = faqItems.filter(item => {
    const matchesCategory = activeCategory === 'all' || item.category === activeCategory;
    const matchesSearch = item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.answer.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const toggleFAQ = (id: string) => {
    setExpandedFAQ(expandedFAQ === id ? null : id);
  };

  return (
    <div className="container mx-auto px-4 py-8 pt-24">
      {/* Botão de Voltar */}
      <div className="mb-6">
        <Button
          variant="outline"
          onClick={() => navigate(-1)}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Voltar</span>
        </Button>
      </div>

      {/* Header */}
      <div className="text-center mb-8">
        <h1 className={`text-3xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Central de Ajuda
        </h1>
        <p className={`text-lg ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          Encontre respostas para suas dúvidas sobre a YMRentals
        </p>
      </div>

      {/* Busca */}
      <div className="max-w-2xl mx-auto mb-8">
        <div className="relative">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="Buscar por dúvidas..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`w-full pl-12 pr-4 py-3 border rounded-lg text-lg ${
              isDarkMode 
                ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400' 
                : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
            }`}
          />
        </div>
      </div>

      {/* Contato Rápido */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <div className={`p-6 rounded-lg text-center ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
          <div className="w-12 h-12 mx-auto mb-4 rounded-full flex items-center justify-center" style={{backgroundColor: '#3569b0'}}>
            <Phone className="w-6 h-6 text-white" />
          </div>
          <h3 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Telefone</h3>
          <p className={`text-sm mb-3 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Suporte 24/7 disponível
          </p>
          <Button variant="outline" size="sm">
            +244 923 456 789
          </Button>
        </div>

        <div className={`p-6 rounded-lg text-center ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
          <div className="w-12 h-12 mx-auto mb-4 rounded-full flex items-center justify-center" style={{backgroundColor: '#3569b0'}}>
            <MessageCircle className="w-6 h-6 text-white" />
          </div>
          <h3 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Chat Online</h3>
          <p className={`text-sm mb-3 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Resposta em minutos
          </p>
          <Button 
            size="sm"
            className="text-white"
            style={{backgroundColor: '#3569b0'}}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2a5490'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3569b0'}
          >
            Iniciar Chat
          </Button>
        </div>

        <div className={`p-6 rounded-lg text-center ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
          <div className="w-12 h-12 mx-auto mb-4 rounded-full flex items-center justify-center" style={{backgroundColor: '#3569b0'}}>
            <Mail className="w-6 h-6 text-white" />
          </div>
          <h3 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Email</h3>
          <p className={`text-sm mb-3 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Resposta em até 24h
          </p>
          <Button variant="outline" size="sm">
            <EMAIL>
          </Button>
        </div>
      </div>

      {/* Categorias */}
      <div className="mb-6">
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => {
            const IconComponent = category.icon;
            return (
              <button
                key={category.key}
                onClick={() => setActiveCategory(category.key)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeCategory === category.key
                    ? 'text-white'
                    : isDarkMode
                    ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
                style={activeCategory === category.key ? {backgroundColor: '#3569b0'} : {}}
              >
                <IconComponent className="w-4 h-4" />
                <span>{category.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* FAQ */}
      <div className="max-w-4xl mx-auto">
        <h2 className={`text-2xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Perguntas Frequentes
        </h2>

        {filteredFAQs.length > 0 ? (
          <div className="space-y-4">
            {filteredFAQs.map((faq) => (
              <div
                key={faq.id}
                className={`rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} shadow-sm`}
              >
                <button
                  onClick={() => toggleFAQ(faq.id)}
                  className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <span className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {faq.question}
                  </span>
                  {expandedFAQ === faq.id ? (
                    <ChevronDown className="w-5 h-5 text-gray-400" />
                  ) : (
                    <ChevronRight className="w-5 h-5 text-gray-400" />
                  )}
                </button>
                
                {expandedFAQ === faq.id && (
                  <div className="px-6 pb-4">
                    <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'} leading-relaxed`}>
                      {faq.answer}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className={`text-center py-12 ${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg`}>
            <HelpCircle className="w-16 h-16 mx-auto mb-4 text-gray-400" />
            <h3 className={`text-lg font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Nenhuma pergunta encontrada
            </h3>
            <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Tente ajustar sua busca ou entre em contato conosco.
            </p>
          </div>
        )}
      </div>

      {/* Ainda precisa de ajuda? */}
      <div className={`mt-12 p-8 rounded-lg text-center ${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
        <h3 className={`text-xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Ainda precisa de ajuda?
        </h3>
        <p className={`mb-6 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          Nossa equipe está sempre pronta para ajudar você
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button 
            className="text-white"
            style={{backgroundColor: '#3569b0'}}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2a5490'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3569b0'}
            onClick={() => navigate('/contact')}
          >
            <MessageCircle className="w-4 h-4 mr-2" />
            Entrar em Contato
          </Button>
          <Button variant="outline">
            <Phone className="w-4 h-4 mr-2" />
            Ligar Agora
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Help;
