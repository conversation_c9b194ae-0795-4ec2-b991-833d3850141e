import React from 'react';
import { Shield, CheckCircle, Phone, Truck } from 'lucide-react';

interface TrustSignalsProps {
  isDarkMode: boolean;
}

/**
 * Componente que exibe os sinais de confiança da plataforma
 * Mostra quatro cards com ícones e descrições dos principais diferenciais de segurança
 */
const TrustSignals: React.FC<TrustSignalsProps> = ({ isDarkMode }) => {
  // Array de sinais de confiança para facilitar manutenção e expansão
  const trustSignals = [
    {
      icon: <Shield className={`h-12 w-12 mb-4`} style={{color: '#3569b0'}} />,
      title: "Equipamentos Seguros",
      description: "Todos os equipamentos passam por rigorosas inspeções de segurança"
    },
    {
      icon: <CheckCircle className={`h-12 w-12 mb-4`} style={{color: '#3569b0'}} />,
      title: "Garantia de Qualidade",
      description: "Garantimos o funcionamento adequado de todos os equipamentos"
    },
    {
      icon: <Phone className={`h-12 w-12 mb-4`} style={{color: '#3569b0'}} />,
      title: "Suporte 24/7",
      description: "Equipe de suporte disponível 24 horas por dia, 7 dias por semana"
    },
    {
      icon: <Truck className={`h-12 w-12 mb-4`} style={{color: '#3569b0'}} />,
      title: "Entrega Rápida",
      description: "Entrega e retirada em todo o território nacional"
    }
  ];

  return (
    <section className={`mt-16 py-12 rounded-lg ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
      <div className="container mx-auto px-4">
        <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
          <h2 className={`text-2xl font-bold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>Confiança e Segurança</h2>
          <p className={`max-w-2xl mx-auto ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Nossos clientes confiam em nossos serviços para suas necessidades de equipamentos
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 items-center justify-items-center">
          {trustSignals.map((signal, index) => (
            <div key={index} className="flex flex-col items-center">
              {signal.icon}
              <h3 className={`text-lg font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>{signal.title}</h3>
              <p className={`text-center text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {signal.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TrustSignals;
