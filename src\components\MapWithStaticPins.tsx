import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

interface MapWithStaticPinsProps {
  isDarkMode: boolean;
}

const MapWithStaticPins: React.FC<MapWithStaticPinsProps> = ({ isDarkMode }) => {
  const navigate = useNavigate();
  const [selectedProvince, setSelectedProvince] = useState<string | null>(null);
  
  // Dados de províncias
  const provinces = [
    { id: 'luanda', name: 'Lu<PERSON>', count: 145 },
    { id: 'benguela', name: '<PERSON><PERSON><PERSON>', count: 87 },
    { id: 'huambo', name: '<PERSON><PERSON><PERSON>', count: 56 },
    { id: 'huila', name: '<PERSON><PERSON><PERSON>', count: 42 },
    { id: 'cabinda', name: '<PERSON><PERSON><PERSON>', count: 23 },
    { id: 'malanje', name: '<PERSON><PERSON><PERSON>', count: 35 },
    { id: 'uige', name: '<PERSON><PERSON><PERSON>', count: 28 },
    { id: 'zaire', name: 'Zaire', count: 19 },
    { id: 'cuanza-norte', name: 'Cuanza Norte', count: 25 },
    { id: 'cuanza-sul', name: 'Cuanza Sul', count: 31 },
    { id: 'lunda-norte', name: 'Lunda Norte', count: 22 },
    { id: 'lunda-sul', name: 'Lunda Sul', count: 18 },
    { id: 'moxico', name: 'Moxico', count: 15 },
    { id: 'bie', name: 'Bié', count: 27 },
    { id: 'namibe', name: 'Namibe', count: 20 },
    { id: 'cunene', name: 'Cunene', count: 16 },
    { id: 'cuando-cubango', name: 'Cuando Cubango', count: 12 },
    { id: 'bengo', name: 'Bengo', count: 24 }
  ];
  
  // Dados de municípios
  const municipalityData: Record<string, Array<{id: string, name: string, count: number}>> = {
    'luanda': [
      { id: 'luanda-city', name: 'Luanda', count: 78 },
      { id: 'viana', name: 'Viana', count: 32 },
      { id: 'cacuaco', name: 'Cacuaco', count: 18 },
      { id: 'belas', name: 'Belas', count: 17 }
    ],
    'benguela': [
      { id: 'benguela-city', name: 'Benguela', count: 45 },
      { id: 'lobito', name: 'Lobito', count: 32 },
      { id: 'catumbela', name: 'Catumbela', count: 10 }
    ],
    'huambo': [
      { id: 'huambo-city', name: 'Huambo', count: 38 },
      { id: 'caala', name: 'Caála', count: 18 }
    ]
  };
  
  // Coordenadas dos pins no mapa (em porcentagem)
  const pinCoordinates: Record<string, {x: number, y: number}> = {
    'luanda': { x: 58, y: 32 },
    'benguela': { x: 35, y: 55 },
    'huambo': { x: 45, y: 58 },
    'huila': { x: 45, y: 70 },
    'cabinda': { x: 25, y: 8 },
    'malanje': { x: 65, y: 35 },
    'uige': { x: 45, y: 25 },
    'zaire': { x: 32, y: 15 },
    'cuanza-norte': { x: 55, y: 38 },
    'cuanza-sul': { x: 50, y: 45 },
    'lunda-norte': { x: 78, y: 28 },
    'lunda-sul': { x: 75, y: 40 },
    'moxico': { x: 80, y: 55 },
    'bie': { x: 58, y: 55 },
    'namibe': { x: 30, y: 70 },
    'cunene': { x: 45, y: 82 },
    'cuando-cubango': { x: 70, y: 75 },
    'bengo': { x: 52, y: 28 }
  };
  
  // Coordenadas dos pins de municípios (em porcentagem)
  const municipalityCoordinates: Record<string, Record<string, {x: number, y: number}>> = {
    'luanda': {
      'luanda-city': { x: 50, y: 50 },
      'viana': { x: 60, y: 55 },
      'cacuaco': { x: 45, y: 40 },
      'belas': { x: 55, y: 60 }
    },
    'benguela': {
      'benguela-city': { x: 50, y: 50 },
      'lobito': { x: 40, y: 45 },
      'catumbela': { x: 45, y: 48 }
    },
    'huambo': {
      'huambo-city': { x: 50, y: 50 },
      'caala': { x: 40, y: 45 }
    }
  };
  
  const handleProvinceClick = (provinceId: string) => {
    setSelectedProvince(provinceId);
  };
  
  const handleMunicipalityClick = (municipalityId: string) => {
    navigate(`/equipment?location=${municipalityId}`);
  };
  
  const handleBackClick = () => {
    setSelectedProvince(null);
  };
  
  // Determinar quais pins mostrar
  const pinsToShow = selectedProvince 
    ? (municipalityData[selectedProvince] || []).map(m => ({
        id: m.id,
        name: m.name,
        count: m.count,
        coordinates: municipalityCoordinates[selectedProvince]?.[m.id] || { x: 50, y: 50 }
      }))
    : provinces.map(p => ({
        id: p.id,
        name: p.name,
        count: p.count,
        coordinates: pinCoordinates[p.id] || { x: 50, y: 50 }
      }));
  
  return (
    <div className={`relative rounded-lg overflow-hidden ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h3 className="text-lg font-semibold">
          {selectedProvince
            ? `Equipamentos em ${provinces.find(p => p.id === selectedProvince)?.name}`
            : 'Pesquisar no Mapa'}
        </h3>
        {selectedProvince && (
          <button
            onClick={handleBackClick}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            Voltar ao mapa de Angola
          </button>
        )}
      </div>
      
      <div className="relative" style={{ height: '600px' }}>
        {/* Mapa estático com pins */}
        <div className="relative w-full h-full bg-blue-50 dark:bg-gray-700">
          {/* Imagem do mapa */}
          <img 
            src={selectedProvince ? `/img/maps/${selectedProvince}.jpg` : "/img/maps/angola.jpg"} 
            alt={selectedProvince ? `Mapa de ${provinces.find(p => p.id === selectedProvince)?.name}` : "Mapa de Angola"} 
            className="w-full h-full object-cover"
          />
          
          {/* Pins */}
          {pinsToShow.map(pin => (
            <div
              key={pin.id}
              className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
              style={{
                left: `${pin.coordinates.x}%`,
                top: `${pin.coordinates.y}%`,
                zIndex: 10
              }}
              onClick={() => selectedProvince 
                ? handleMunicipalityClick(pin.id) 
                : handleProvinceClick(pin.id)
              }
            >
              <div className="flex flex-col items-center">
                <div className={`flex items-center justify-center w-12 h-12 rounded-full ${isDarkMode ? 'bg-blue-600' : 'bg-blue-500'} text-white font-semibold shadow-lg text-lg`}>
                  {pin.count}
                </div>
                <div className={`mt-1 px-2 py-1 rounded text-sm font-medium ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'} shadow`}>
                  {pin.name}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          {selectedProvince
            ? 'Clique em um município para ver os equipamentos disponíveis.'
            : 'Clique em uma província para ver os equipamentos disponíveis em cada município.'}
        </p>
      </div>
    </div>
  );
};

export default MapWithStaticPins;
