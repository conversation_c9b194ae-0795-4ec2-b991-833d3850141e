import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { MapPin, Star } from 'lucide-react';
import { Button } from './ui/button';

interface MapWithStaticImageProps {
  isDarkMode: boolean;
}

const MapWithStaticImage: React.FC<MapWithStaticImageProps> = ({ isDarkMode }) => {
  const navigate = useNavigate();
  const [selectedProvince, setSelectedProvince] = useState<string | null>(null);

  // Dados de províncias
  const provinces = [
    { id: 'luanda', name: '<PERSON><PERSON>', count: 145, lat: -8.8368, lng: 13.2343 },
    { id: 'bengue<PERSON>', name: '<PERSON><PERSON><PERSON>', count: 87, lat: -12.5763, lng: 13.4055 },
    { id: 'huambo', name: '<PERSON><PERSON><PERSON>', count: 56, lat: -12.7761, lng: 15.7392 },
    { id: 'huila', name: '<PERSON><PERSON><PERSON>', count: 42, lat: -14.9226, lng: 14.6569 },
    { id: 'cabinda', name: '<PERSON><PERSON><PERSON>', count: 23, lat: -5.5496, lng: 12.1955 },
    { id: 'malanje', name: '<PERSON>an<PERSON>', count: 35, lat: -9.5402, lng: 16.3534 },
    { id: 'uige', name: 'Uíge', count: 28, lat: -7.6087, lng: 15.0613 },
    { id: 'zaire', name: 'Zaire', count: 19, lat: -6.1349, lng: 12.9992 },
    { id: 'cuanza-norte', name: 'Cuanza Norte', count: 25, lat: -9.2366, lng: 14.6574 },
    { id: 'cuanza-sul', name: 'Cuanza Sul', count: 31, lat: -10.7031, lng: 15.0613 },
    { id: 'lunda-norte', name: 'Lunda Norte', count: 22, lat: -8.4192, lng: 20.7369 },
    { id: 'lunda-sul', name: 'Lunda Sul', count: 18, lat: -10.2864, lng: 20.0371 },
    { id: 'moxico', name: 'Moxico', count: 15, lat: -13.4557, lng: 20.3799 },
    { id: 'bie', name: 'Bié', count: 27, lat: -12.5763, lng: 17.6747 },
    { id: 'namibe', name: 'Namibe', count: 20, lat: -15.1968, lng: 12.1522 },
    { id: 'cunene', name: 'Cunene', count: 16, lat: -17.0657, lng: 15.7333 },
    { id: 'cuando-cubango', name: 'Cuando Cubango', count: 12, lat: -16.7428, lng: 18.3358 },
    { id: 'bengo', name: 'Bengo', count: 24, lat: -8.7526, lng: 13.7169 }
  ];

  // Dados de municípios
  const municipalities: Record<string, Array<{id: string, name: string, count: number, lat: number, lng: number}>> = {
    'luanda': [
      { id: 'luanda-city', name: 'Luanda', count: 78, lat: -8.8368, lng: 13.2343 },
      { id: 'viana', name: 'Viana', count: 32, lat: -8.9075, lng: 13.3630 },
      { id: 'cacuaco', name: 'Cacuaco', count: 18, lat: -8.7785, lng: 13.3722 },
      { id: 'belas', name: 'Belas', count: 17, lat: -9.0686, lng: 13.1607 }
    ],
    'benguela': [
      { id: 'benguela-city', name: 'Benguela', count: 45, lat: -12.5763, lng: 13.4055 },
      { id: 'lobito', name: 'Lobito', count: 32, lat: -12.3481, lng: 13.5456 },
      { id: 'catumbela', name: 'Catumbela', count: 10, lat: -12.4300, lng: 13.5500 }
    ],
    'huambo': [
      { id: 'huambo-city', name: 'Huambo', count: 38, lat: -12.7761, lng: 15.7392 },
      { id: 'caala', name: 'Caála', count: 18, lat: -12.8522, lng: 15.5606 }
    ],
    'huila': [
      { id: 'lubango', name: 'Lubango', count: 25, lat: -14.9226, lng: 14.6569 },
      { id: 'humpata', name: 'Humpata', count: 12, lat: -15.0500, lng: 14.6000 }
    ],
    'cabinda': [
      { id: 'cabinda-city', name: 'Cabinda', count: 15, lat: -5.5496, lng: 12.1955 },
      { id: 'cacongo', name: 'Cacongo', count: 5, lat: -5.4000, lng: 12.2000 }
    ],
    'malanje': [
      { id: 'malanje-city', name: 'Malanje', count: 20, lat: -9.5402, lng: 16.3534 },
      { id: 'cacuso', name: 'Cacuso', count: 15, lat: -9.4200, lng: 16.2800 }
    ],
    'uige': [
      { id: 'uige-city', name: 'Uíge', count: 18, lat: -7.6087, lng: 15.0613 },
      { id: 'negage', name: 'Negage', count: 10, lat: -7.7600, lng: 15.2700 }
    ],
    'zaire': [
      { id: 'mbanza-congo', name: 'Mbanza Congo', count: 12, lat: -6.1349, lng: 12.9992 },
      { id: 'soyo', name: 'Soyo', count: 7, lat: -6.1300, lng: 12.3700 }
    ],
    'cuanza-norte': [
      { id: 'ndalatando', name: 'Ndalatando', count: 15, lat: -9.2366, lng: 14.6574 },
      { id: 'golungo-alto', name: 'Golungo Alto', count: 10, lat: -9.1300, lng: 14.7800 }
    ],
    'cuanza-sul': [
      { id: 'sumbe', name: 'Sumbe', count: 20, lat: -10.7031, lng: 15.0613 },
      { id: 'porto-amboim', name: 'Porto Amboim', count: 11, lat: -10.7400, lng: 13.7600 }
    ],
    'lunda-norte': [
      { id: 'dundo', name: 'Dundo', count: 14, lat: -8.4192, lng: 20.7369 },
      { id: 'lucapa', name: 'Lucapa', count: 8, lat: -8.4200, lng: 20.7400 }
    ],
    'lunda-sul': [
      { id: 'saurimo', name: 'Saurimo', count: 12, lat: -10.2864, lng: 20.0371 },
      { id: 'cacolo', name: 'Cacolo', count: 6, lat: -10.1500, lng: 19.2600 }
    ],
    'moxico': [
      { id: 'luena', name: 'Luena', count: 10, lat: -13.4557, lng: 20.3799 },
      { id: 'luau', name: 'Luau', count: 5, lat: -13.4500, lng: 20.3800 }
    ],
    'bie': [
      { id: 'kuito', name: 'Kuito', count: 18, lat: -12.5763, lng: 17.6747 },
      { id: 'andulo', name: 'Andulo', count: 9, lat: -12.5800, lng: 17.6700 }
    ],
    'namibe': [
      { id: 'namibe-city', name: 'Namibe', count: 13, lat: -15.1968, lng: 12.1522 },
      { id: 'tombua', name: 'Tombua', count: 7, lat: -15.8000, lng: 11.8500 }
    ],
    'cunene': [
      { id: 'ondjiva', name: 'Ondjiva', count: 10, lat: -17.0657, lng: 15.7333 },
      { id: 'cuanhama', name: 'Cuanhama', count: 6, lat: -17.0600, lng: 15.7300 }
    ],
    'cuando-cubango': [
      { id: 'menongue', name: 'Menongue', count: 8, lat: -16.7428, lng: 18.3358 },
      { id: 'cuito-cuanavale', name: 'Cuito Cuanavale', count: 4, lat: -16.7400, lng: 18.3400 }
    ],
    'bengo': [
      { id: 'caxito', name: 'Caxito', count: 16, lat: -8.7526, lng: 13.7169 },
      { id: 'dande', name: 'Dande', count: 8, lat: -8.7500, lng: 13.7200 }
    ]
  };

  // Função para converter coordenadas geográficas para posição na tela
  const geoToPixel = (lat: number, lng: number) => {
    // Limites aproximados do mapa de Angola
    const mapBounds = {
      north: -5.0, // Latitude norte
      south: -18.0, // Latitude sul
      west: 11.0, // Longitude oeste
      east: 24.5 // Longitude leste
    };

    // Calcular a posição percentual no mapa
    const x = ((lng - mapBounds.west) / (mapBounds.east - mapBounds.west)) * 100;
    const y = ((lat - mapBounds.north) / (mapBounds.south - mapBounds.north)) * 100;

    return { x, y };
  };

  const handleProvinceClick = (provinceId: string) => {
    setSelectedProvince(provinceId);
  };

  const handleMunicipalityClick = (municipalityId: string) => {
    navigate(`/equipment?location=${municipalityId}`);
  };

  const handleBackClick = () => {
    setSelectedProvince(null);
  };

  // Determinar quais pins mostrar
  const pinsToShow = selectedProvince
    ? (municipalities[selectedProvince] || [])
    : provinces;

  // Dados de equipamentos para cada região
  const equipmentData: Record<string, Array<{id: string, title: string, price: string, image: string, rating: number}>> = {
    'luanda': [
      { id: 'eq1', title: 'Retroescavadeira CAT 420F2', price: '250.000 Kz/dia', image: 'https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=600', rating: 4.8 },
      { id: 'eq2', title: 'Gerador 100kVA Diesel', price: '150.000 Kz/dia', image: 'https://images.unsplash.com/photo-1586744666440-fef0dc5d0d18?auto=format&fit=crop&w=600', rating: 4.5 },
      { id: 'eq3', title: 'Empilhadeira Toyota 8FD', price: '180.000 Kz/dia', image: 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?auto=format&fit=crop&w=600', rating: 4.7 }
    ],
    'benguela': [
      { id: 'eq4', title: 'Guindaste Liebherr LTM', price: '450.000 Kz/dia', image: 'https://images.unsplash.com/photo-1541625602330-2277a4c46182?auto=format&fit=crop&w=600', rating: 4.9 },
      { id: 'eq5', title: 'Compressor de Ar Atlas Copco', price: '120.000 Kz/dia', image: 'https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=600', rating: 4.6 }
    ],
    'huambo': [
      { id: 'eq6', title: 'Plataforma Elevatória JLG', price: '200.000 Kz/dia', image: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?auto=format&fit=crop&w=600', rating: 4.7 },
      { id: 'eq7', title: 'Betoneira 400L', price: '80.000 Kz/dia', image: 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?auto=format&fit=crop&w=600', rating: 4.4 }
    ],
    'huila': [
      { id: 'eq8', title: 'Trator Agrícola John Deere', price: '300.000 Kz/dia', image: 'https://images.unsplash.com/photo-1605559911160-e31968db7b43?auto=format&fit=crop&w=600', rating: 4.8 },
      { id: 'eq9', title: 'Escavadeira Hidráulica Volvo', price: '350.000 Kz/dia', image: 'https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=600', rating: 4.7 }
    ],
    'cabinda': [
      { id: 'eq10', title: 'Gerador Industrial 200kVA', price: '280.000 Kz/dia', image: 'https://images.unsplash.com/photo-1586744666440-fef0dc5d0d18?auto=format&fit=crop&w=600', rating: 4.6 },
      { id: 'eq11', title: 'Compactador de Solo', price: '100.000 Kz/dia', image: 'https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=600', rating: 4.5 }
    ]
  };

  // Dados de equipamentos para municípios
  const municipalityEquipmentData: Record<string, Array<{id: string, title: string, price: string, image: string, rating: number}>> = {
    'luanda-city': [
      { id: 'eq1', title: 'Retroescavadeira CAT 420F2', price: '250.000 Kz/dia', image: 'https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=600', rating: 4.8 },
      { id: 'eq2', title: 'Gerador 100kVA Diesel', price: '150.000 Kz/dia', image: 'https://images.unsplash.com/photo-1586744666440-fef0dc5d0d18?auto=format&fit=crop&w=600', rating: 4.5 }
    ],
    'viana': [
      { id: 'eq3', title: 'Empilhadeira Toyota 8FD', price: '180.000 Kz/dia', image: 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?auto=format&fit=crop&w=600', rating: 4.7 }
    ],
    'benguela-city': [
      { id: 'eq4', title: 'Guindaste Liebherr LTM', price: '450.000 Kz/dia', image: 'https://images.unsplash.com/photo-1541625602330-2277a4c46182?auto=format&fit=crop&w=600', rating: 4.9 }
    ],
    'lobito': [
      { id: 'eq5', title: 'Compressor de Ar Atlas Copco', price: '120.000 Kz/dia', image: 'https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=600', rating: 4.6 }
    ]
  };

  // Determinar quais equipamentos mostrar
  const equipmentsToShow = selectedProvince
    ? equipmentData[selectedProvince] || []
    : [];

  return (
    <div className={`relative rounded-lg overflow-hidden ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h3 className="text-lg font-semibold">
          {selectedProvince
            ? `Equipamentos em ${provinces.find(p => p.id === selectedProvince)?.name}`
            : 'Pesquisar no Mapa'}
        </h3>
        {selectedProvince && (
          <button
            onClick={handleBackClick}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            Voltar ao mapa de Angola
          </button>
        )}
      </div>

      <div className="relative" style={{ height: '600px' }}>
        {/* Mapa com pins */}
        <div className="relative w-full h-full">
          {/* Mapa de fundo */}
          <div className="absolute inset-0 bg-blue-50 dark:bg-gray-700">
            <iframe
              src={selectedProvince
                ? `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d249424.6851388919!2d${provinces.find(p => p.id === selectedProvince)?.lng}!3d${provinces.find(p => p.id === selectedProvince)?.lat}!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1a51f24ecaad8b27%3A0x590a289d0d4a4e3d!2s${provinces.find(p => p.id === selectedProvince)?.name}%2C%20Angola!5e0!3m2!1spt-PT!2sao!4v1710371547387!5m2!1spt-PT!2sao`
                : "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d8099881.771693946!2d12.8015811!3d-13.0568368!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1a51f24ecaad8b27%3A0x590a289d0d4a4e3d!2sAngola!5e0!3m2!1spt-PT!2sao!4v1710371547387!5m2!1spt-PT!2sao"
              }
              width="100%"
              height="100%"
              style={{ border: 0 }}
              allowFullScreen
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              className="absolute inset-0 z-0"
            ></iframe>

            {/* Pins */}
            <div className="absolute inset-0 z-10 pointer-events-none">
              {pinsToShow.map(pin => {
                const position = geoToPixel(pin.lat, pin.lng);
                return (
                  <div
                    key={pin.id}
                    className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer pointer-events-auto"
                    style={{
                      left: `${position.x}%`,
                      top: `${position.y}%`
                    }}
                    onClick={() => selectedProvince
                      ? handleMunicipalityClick(pin.id)
                      : handleProvinceClick(pin.id)
                    }
                  >
                    <div className="flex flex-col items-center">
                      <div className={`flex items-center justify-center w-12 h-12 rounded-full ${isDarkMode ? 'bg-blue-600' : 'bg-blue-500'} text-white font-semibold shadow-lg text-lg`}>
                        {pin.count}
                      </div>
                      <div className={`mt-1 px-2 py-1 rounded text-sm font-medium ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'} shadow`}>
                        {pin.name}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Botão para ver todos os equipamentos */}
      {selectedProvince && (
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 text-center">
          <Button
            variant="outline"
            className="text-sm"
            onClick={() => navigate(`/equipment?location=${selectedProvince}`)}
          >
            Ver equipamentos em {provinces.find(p => p.id === selectedProvince)?.name}
          </Button>
        </div>
      )}

      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          {selectedProvince
            ? 'Clique em um município para ver os equipamentos disponíveis.'
            : 'Clique em uma província para ver os equipamentos disponíveis em cada município.'}
        </p>
      </div>
    </div>
  );
};

export default MapWithStaticImage;
