import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import apiService from '../../services/api';
import { 
  Grid, 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Save,
  X,
  Package,
  Eye,
  Image
} from 'lucide-react';

interface Category {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  equipmentCount: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface CategoryManagementProps {
  isDarkMode?: boolean;
}

const CategoryManagement: React.FC<CategoryManagementProps> = ({ isDarkMode = false }) => {
  const { user } = useAuth();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    imageUrl: '',
  });

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);

      const response = await fetch('http://localhost:3000/categories?includeInactive=true');

      if (response.ok) {
        const data = await response.json();

        // Mapear dados da API para o formato esperado pelo componente
        const mappedCategories = data.map((category: any) => ({
          id: category.id,
          name: category.name,
          description: category.description || '',
          imageUrl: category.image || '/img/placeholder-equipment.jpg',
          equipmentCount: category._count?.Equipment || 0,
          isActive: category.isActive,
          createdAt: category.createdAt,
          updatedAt: category.updatedAt,
        }));

        setCategories(mappedCategories);
      } else {
        // Fallback para dados mockados em caso de erro da API
        console.log('API não disponível, usando dados mockados');
        const mockCategories: Category[] = [
          {
            id: '1',
            name: 'Construção Civil',
            description: 'Equipamentos para construção e obras',
            imageUrl: '/img/placeholder-equipment.jpg',
            equipmentCount: 45,
            isActive: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-15T10:30:00Z',
          },
          {
            id: '2',
            name: 'Jardinagem',
            description: 'Ferramentas e equipamentos para jardinagem',
            imageUrl: '/img/placeholder-equipment.jpg',
            equipmentCount: 23,
            isActive: true,
            createdAt: '2024-01-02T00:00:00Z',
            updatedAt: '2024-01-14T15:20:00Z',
          },
          {
            id: '3',
            name: 'Transporte',
            description: 'Veículos e equipamentos de transporte',
            imageUrl: '/img/placeholder-equipment.jpg',
            equipmentCount: 18,
            isActive: true,
            createdAt: '2024-01-03T00:00:00Z',
            updatedAt: '2024-01-13T09:45:00Z',
          },
          {
            id: '4',
            name: 'Limpeza',
            description: 'Equipamentos de limpeza industrial',
            imageUrl: '/img/placeholder-equipment.jpg',
            equipmentCount: 12,
            isActive: false,
            createdAt: '2024-01-04T00:00:00Z',
            updatedAt: '2024-01-12T14:10:00Z',
          },
        ];
        setCategories(mockCategories);
      }
    } catch (error) {
      console.error('Erro ao carregar categorias:', error);
      // Fallback para dados mockados em caso de erro de rede
      const mockCategories: Category[] = [
        {
          id: '1',
          name: 'Construção Civil',
          description: 'Equipamentos para construção e obras',
          imageUrl: '/img/placeholder-equipment.jpg',
          equipmentCount: 45,
          isActive: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-15T10:30:00Z',
        },
        {
          id: '2',
          name: 'Jardinagem',
          description: 'Ferramentas e equipamentos para jardinagem',
          imageUrl: '/img/placeholder-equipment.jpg',
          equipmentCount: 23,
          isActive: true,
          createdAt: '2024-01-02T00:00:00Z',
          updatedAt: '2024-01-14T15:20:00Z',
        },
      ];
      setCategories(mockCategories);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCategory = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('authToken');
      if (!token) {
        throw new Error('Token de autenticação não encontrado');
      }

      const response = await fetch('http://localhost:3000/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description,
          image: formData.imageUrl,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erro ao criar categoria');
      }

      const newCategory = await response.json();

      // Recarregar categorias para obter dados atualizados
      await loadCategories();

      setFormData({ name: '', description: '', imageUrl: '' });
      setShowCreateForm(false);
      alert('Categoria criada com sucesso!');
    } catch (error) {
      console.error('Erro ao criar categoria:', error);
      alert(`Erro ao criar categoria: ${error.message}`);
    }
  };

  const handleUpdateCategory = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingCategory) return;

    try {
      setCategories(prev => 
        prev.map(cat => 
          cat.id === editingCategory.id 
            ? { ...cat, ...formData, updatedAt: new Date().toISOString() }
            : cat
        )
      );
      
      setEditingCategory(null);
      setFormData({ name: '', description: '', imageUrl: '' });
      alert('Categoria atualizada com sucesso!');
    } catch (error) {
      console.error('Erro ao atualizar categoria:', error);
      alert('Erro ao atualizar categoria');
    }
  };

  const handleDeleteCategory = async (categoryId: string) => {
    if (!confirm('Tem certeza que deseja excluir esta categoria?')) return;

    try {
      setCategories(prev => prev.filter(cat => cat.id !== categoryId));
      alert('Categoria excluída com sucesso!');
    } catch (error) {
      console.error('Erro ao excluir categoria:', error);
      alert('Erro ao excluir categoria');
    }
  };

  const handleToggleActive = async (categoryId: string) => {
    try {
      setCategories(prev => 
        prev.map(cat => 
          cat.id === categoryId 
            ? { ...cat, isActive: !cat.isActive, updatedAt: new Date().toISOString() }
            : cat
        )
      );
    } catch (error) {
      console.error('Erro ao alterar status da categoria:', error);
    }
  };

  const startEdit = (category: Category) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      description: category.description,
      imageUrl: category.imageUrl,
    });
  };

  const cancelEdit = () => {
    setEditingCategory(null);
    setFormData({ name: '', description: '', imageUrl: '' });
  };

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Gerenciamento de Categorias
          </h2>
          <p className={`mt-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Gerencie as categorias de equipamentos da plataforma
          </p>
        </div>
        
        <button
          onClick={() => setShowCreateForm(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          <Plus className="w-4 h-4" />
          <span>Nova Categoria</span>
        </button>
      </div>

      {/* Search */}
      <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Buscar categorias..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`w-full pl-10 pr-4 py-2 border rounded-lg ${
              isDarkMode 
                ? 'bg-gray-700 border-gray-600 text-white' 
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          />
        </div>
      </div>

      {/* Categories Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCategories.map((category) => (
          <div
            key={category.id}
            className={`rounded-lg shadow-sm overflow-hidden ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}
          >
            <div className="relative h-48">
              <img
                src={category.imageUrl}
                alt={category.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.currentTarget.src = '/img/placeholder-equipment.jpg';
                }}
              />
              <div className="absolute top-2 right-2">
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                  category.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {category.isActive ? 'Ativa' : 'Inativa'}
                </span>
              </div>
            </div>
            
            <div className="p-4">
              <h3 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {category.name}
              </h3>
              <p className={`text-sm mb-3 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {category.description}
              </p>
              
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-1">
                  <Package className="w-4 h-4 text-gray-400" />
                  <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {category.equipmentCount} equipamentos
                  </span>
                </div>
                <span className={`text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-500'}`}>
                  Atualizada em {new Date(category.updatedAt).toLocaleDateString()}
                </span>
              </div>
              
              <div className="flex space-x-2">
                <button
                  onClick={() => startEdit(category)}
                  className="flex-1 flex items-center justify-center space-x-1 px-3 py-2 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50"
                >
                  <Edit className="w-4 h-4" />
                  <span>Editar</span>
                </button>
                
                <button
                  onClick={() => handleToggleActive(category.id)}
                  className={`flex-1 flex items-center justify-center space-x-1 px-3 py-2 rounded-lg ${
                    category.isActive
                      ? 'text-red-600 border border-red-600 hover:bg-red-50'
                      : 'text-green-600 border border-green-600 hover:bg-green-50'
                  }`}
                >
                  <Eye className="w-4 h-4" />
                  <span>{category.isActive ? 'Desativar' : 'Ativar'}</span>
                </button>
                
                <button
                  onClick={() => handleDeleteCategory(category.id)}
                  className="px-3 py-2 text-red-600 border border-red-600 rounded-lg hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Create/Edit Form Modal */}
      {(showCreateForm || editingCategory) && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className={`w-full max-w-md p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              {editingCategory ? 'Editar Categoria' : 'Nova Categoria'}
            </h3>
            
            <form onSubmit={editingCategory ? handleUpdateCategory : handleCreateCategory} className="space-y-4">
              <div>
                <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Nome da Categoria
                </label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    isDarkMode 
                      ? 'bg-gray-700 border-gray-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Descrição
                </label>
                <textarea
                  required
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  rows={3}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    isDarkMode 
                      ? 'bg-gray-700 border-gray-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  URL da Imagem
                </label>
                <input
                  type="url"
                  value={formData.imageUrl}
                  onChange={(e) => setFormData({...formData, imageUrl: e.target.value})}
                  placeholder="/img/placeholder-equipment.jpg"
                  className={`w-full px-3 py-2 border rounded-lg ${
                    isDarkMode 
                      ? 'bg-gray-700 border-gray-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  {editingCategory ? 'Atualizar' : 'Criar'} Categoria
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowCreateForm(false);
                    cancelEdit();
                  }}
                  className={`flex-1 px-4 py-2 border rounded-lg ${
                    isDarkMode 
                      ? 'border-gray-600 text-gray-300 hover:bg-gray-700' 
                      : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Cancelar
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {filteredCategories.length === 0 && (
        <div className="text-center py-12">
          <Grid className="w-12 h-12 mx-auto mb-4 text-gray-400" />
          <h3 className={`text-lg font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Nenhuma categoria encontrada
          </h3>
          <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            {searchTerm 
              ? 'Tente ajustar o termo de busca.' 
              : 'Crie a primeira categoria para começar.'}
          </p>
        </div>
      )}
    </div>
  );
};

export default CategoryManagement;
