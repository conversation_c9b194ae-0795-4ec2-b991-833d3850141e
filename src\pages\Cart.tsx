import React from 'react';
import { Trash2, Calendar, MapPin } from 'lucide-react';
import { Button } from '../components/ui/button';
import BackButton from '../components/ui/BackButton';

interface CartProps {
  isDarkMode?: boolean;
}

export default function Cart({ isDarkMode = false }: CartProps) {
  const cartItems = [
    {
      id: 1,
      name: 'Retroescavadeira CAT 420F2',
      image: 'https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=300',
      price: 250000,
      days: 3,
      location: 'Luanda, Talatona',
      dates: '25 Mar - 28 Mar'
    },
    {
      id: 2,
      name: 'Empilhadeira Toyota 8FD',
      image: 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?auto=format&fit=crop&w=300',
      price: 180000,
      days: 2,
      location: 'Luanda, Viana',
      dates: '26 Mar - 28 Mar'
    }
  ];

  const subtotal = cartItems.reduce((acc, item) => acc + (item.price * item.days), 0);
  const serviceFee = subtotal * 0.1; // 10% service fee
  const total = subtotal + serviceFee;

  return (
    <div className={`container mx-auto px-4 py-8 pt-24 ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="flex items-center space-x-4 mb-8">
        <BackButton isDarkMode={isDarkMode} />
        <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Carrinho de Aluguel</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <div className="space-y-4">
            {cartItems.map((item) => (
              <div key={item.id} className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-4`}>
                <div className="flex gap-4">
                  <img
                    src={item.image}
                    alt={item.name}
                    className="w-24 h-24 object-cover rounded-lg"
                  />
                  <div className="flex-1">
                    <div className="flex justify-between">
                      <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{item.name}</h3>
                      <Button variant="ghost" size="icon" className="text-red-500 hover:text-red-600">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className={`mt-2 space-y-1 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        <span>{item.dates}</span>
                      </div>
                      <div className="flex items-center">
                        <MapPin className="w-4 h-4 mr-1" />
                        <span>{item.location}</span>
                      </div>
                    </div>
                    <div className="mt-2 flex justify-between items-center">
                      <span className="text-cyan-600 font-semibold">
                        {item.price.toLocaleString('pt-AO')} Kz/dia
                      </span>
                      <span className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                        {item.days} {item.days === 1 ? 'dia' : 'dias'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="lg:col-span-1">
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6 sticky top-24`}>
            <h2 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Resumo do Pedido</h2>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Subtotal</span>
                <span>{subtotal.toLocaleString('pt-AO')} Kz</span>
              </div>
              <div className="flex justify-between">
                <span className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Taxa de serviço (10%)</span>
                <span>{serviceFee.toLocaleString('pt-AO')} Kz</span>
              </div>
              <hr className={`my-4 ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`} />
              <div className="flex justify-between font-semibold">
                <span>Total</span>
                <span>{total.toLocaleString('pt-AO')} Kz</span>
              </div>
            </div>
            <Button className="w-full mt-6">
              Finalizar Pedido
            </Button>
            <p className={`text-xs text-center mt-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              Ao finalizar o pedido, você concorda com nossos termos de serviço e política de privacidade.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}