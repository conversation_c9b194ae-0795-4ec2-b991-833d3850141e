import React, { useState, useEffect } from 'react';
import { Trash2, Calendar, MapPin, Plus, Minus, ShoppingBag } from 'lucide-react';
import { Button } from '../components/ui/button';
import BackButton from '../components/ui/BackButton';
import { useNavigate } from 'react-router-dom';

interface CartItem {
  id: string;
  name: string;
  price: number;
  image: string;
  quantity: number;
  period: string;
  startDate?: string;
  endDate?: string;
}

interface CartProps {
  isDarkMode?: boolean;
}

export default function Cart({ isDarkMode = false }: CartProps) {
  const navigate = useNavigate();
  const [cartItems, setCartItems] = useState<CartItem[]>([]);

  // Carregar itens do carrinho do localStorage
  useEffect(() => {
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
      setCartItems(JSON.parse(savedCart));
    }
  }, []);

  // <PERSON>var carrinho no localStorage
  const saveCart = (items: CartItem[]) => {
    setCartItems(items);
    localStorage.setItem('cart', JSON.stringify(items));
  };

  // Atualizar quantidade
  const updateQuantity = (id: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(id);
      return;
    }

    const updatedItems = cartItems.map(item =>
      item.id === id ? { ...item, quantity: newQuantity } : item
    );
    saveCart(updatedItems);
  };

  // Remover item
  const removeItem = (id: string) => {
    const updatedItems = cartItems.filter(item => item.id !== id);
    saveCart(updatedItems);
  };

  // Calcular totais
  const subtotal = cartItems.reduce((acc, item) => acc + (item.price * item.quantity), 0);
  const serviceFee = subtotal * 0.1; // 10% taxa de serviço
  const total = subtotal + serviceFee;

  // Formatar preço
  const formatPrice = (price: number) => {
    return price.toLocaleString('pt-AO') + ' Kz';
  };

  // Se carrinho vazio
  if (cartItems.length === 0) {
    return (
      <div className={`container mx-auto px-4 py-8 pt-24 ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
        <div className="text-center py-20">
          <ShoppingBag className={`w-24 h-24 mx-auto mb-6 ${isDarkMode ? 'text-gray-600' : 'text-gray-400'}`} />
          <h1 className="text-3xl font-bold mb-4">Seu carrinho está vazio</h1>
          <p className={`text-lg mb-8 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Adicione alguns equipamentos ao seu carrinho para continuar
          </p>
          <Button
            onClick={() => navigate('/equipment')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3"
          >
            Explorar Equipamentos
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`container mx-auto px-4 py-8 pt-24 ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="flex items-center space-x-4 mb-8">
        <BackButton isDarkMode={isDarkMode} />
        <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Carrinho de Aluguel</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <div className="space-y-4">
            {cartItems.map((item) => (
              <div key={item.id} className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-4`}>
                <div className="flex gap-4">
                  <img
                    src={item.image}
                    alt={item.name}
                    className="w-24 h-24 object-cover rounded-lg"
                  />
                  <div className="flex-1">
                    <div className="flex justify-between">
                      <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{item.name}</h3>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="text-red-500 hover:text-red-600"
                        onClick={() => removeItem(item.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className={`mt-2 space-y-1 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        <span>
                          Período: {item.period === 'daily' ? 'Diário' : item.period === 'weekly' ? 'Semanal' : 'Mensal'}
                        </span>
                      </div>
                      {item.startDate && item.endDate && (
                        <div className="flex items-center">
                          <MapPin className="w-4 h-4 mr-1" />
                          <span>{item.startDate} até {item.endDate}</span>
                        </div>
                      )}
                    </div>
                    <div className="mt-2 flex justify-between items-center">
                      <span className="text-cyan-600 font-semibold">
                        {formatPrice(item.price)}
                      </span>
                      <div className="flex items-center gap-3">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          className="w-8 h-8 p-0"
                        >
                          <Minus className="w-4 h-4" />
                        </Button>

                        <span className="w-8 text-center font-medium">{item.quantity}</span>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          className="w-8 h-8 p-0"
                        >
                          <Plus className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="lg:col-span-1">
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6 sticky top-24`}>
            <h2 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Resumo do Pedido</h2>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  Subtotal ({cartItems.length} {cartItems.length === 1 ? 'item' : 'itens'})
                </span>
                <span>{formatPrice(subtotal)}</span>
              </div>
              <div className="flex justify-between">
                <span className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Taxa de serviço (10%)</span>
                <span>{formatPrice(serviceFee)}</span>
              </div>
              <hr className={`my-4 ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`} />
              <div className="flex justify-between font-semibold">
                <span>Total</span>
                <span className="text-blue-600">{formatPrice(total)}</span>
              </div>
            </div>
            <Button
              className="w-full mt-6 bg-blue-600 hover:bg-blue-700 text-white"
              onClick={() => {
                // Verificar se está logado
                const token = localStorage.getItem('authToken');
                if (!token) {
                  navigate('/login');
                  return;
                }
                // Aqui seria a navegação para checkout
                alert('Funcionalidade de checkout será implementada em breve!');
              }}
            >
              Finalizar Pedido
            </Button>
            <Button
              variant="outline"
              className="w-full mt-3"
              onClick={() => navigate('/equipment')}
            >
              Continuar Comprando
            </Button>
            <p className={`text-xs text-center mt-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              Ao finalizar o pedido, você concorda com nossos termos de serviço e política de privacidade.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}