import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNotifications } from '../contexts/NotificationContext';
import { 
  Bell, 
  User, 
  Settings, 
  LogOut, 
  Menu, 
  X,
  Shield,
  Users,
  Package,
  Building,
  FileText,
  BarChart3,
  MessageSquare
} from 'lucide-react';

interface AdminLayoutProps {
  children: React.ReactNode;
  isDarkMode: boolean;
  activeSection: string;
  onSectionChange: (section: string) => void;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ 
  children, 
  isDarkMode, 
  activeSection, 
  onSectionChange 
}) => {
  const { user, logout } = useAuth();
  const { unreadCount } = useNotifications();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Definir menu baseado no role do usuário
  const getMenuItems = () => {
    const baseItems = [
      { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
      { id: 'notifications', label: 'Notifica<PERSON>ões', icon: Bell, badge: unreadCount },
      { id: 'messages', label: 'Mensagens', icon: MessageSquare },
      { id: 'profile', label: 'Perfil', icon: User },
    ];

    // Itens específicos por role
    if (user?.role === 'ADMIN') {
      return [
        ...baseItems.slice(0, 1), // Dashboard
        { id: 'users', label: 'Gerenciar Usuários', icon: Users },
        { id: 'landlords', label: 'Validar Locadores', icon: Building },
        { id: 'moderators', label: 'Gerenciar Moderadores', icon: Shield },
        { id: 'equipment', label: 'Moderar Equipamentos', icon: Package },
        { id: 'bi-validation', label: 'Validar Documentos BI', icon: FileText },
        { id: 'payment-validation', label: 'Validar Pagamentos', icon: FileText },
        { id: 'categories', label: 'Gerenciar Categorias', icon: FileText },
        { id: 'content-management', label: 'Conteúdo Dinâmico', icon: FileText },
        { id: 'reports', label: 'Relatórios Completos', icon: BarChart3 },
        { id: 'system-config', label: 'Configurações Sistema', icon: Settings },
        ...baseItems.slice(1), // Notificações, Mensagens, Perfil
      ];
    } else if (user?.role === 'MODERATOR_MANAGER') {
      return [
        ...baseItems.slice(0, 1), // Dashboard
        { id: 'landlords', label: 'Validar Locadores', icon: Building },
        { id: 'moderators', label: 'Gerenciar Moderadores', icon: Shield },
        { id: 'equipment', label: 'Moderar Equipamentos', icon: Package },
        { id: 'bi-validation', label: 'Validar Documentos BI', icon: FileText },
        { id: 'payment-validation', label: 'Validar Pagamentos', icon: FileText },
        { id: 'categories', label: 'Gerenciar Categorias', icon: FileText },
        { id: 'reports', label: 'Relatórios Avançados', icon: BarChart3 },
        ...baseItems.slice(1), // Notificações, Mensagens, Perfil
      ];
    } else if (user?.role === 'MODERATOR') {
      return [
        ...baseItems.slice(0, 1), // Dashboard
        { id: 'equipment', label: 'Moderar Equipamentos', icon: Package },
        { id: 'bi-validation', label: 'Validar Documentos BI', icon: FileText },
        { id: 'payment-validation', label: 'Validar Pagamentos', icon: FileText },
        ...baseItems.slice(1), // Notificações, Mensagens, Perfil
      ];
    }

    return baseItems;
  };

  const menuItems = getMenuItems();

  const getRoleTitle = () => {
    switch (user?.role) {
      case 'ADMIN': return 'Administrador';
      case 'MODERATOR_MANAGER': return 'Moderador Gerencial';
      case 'MODERATOR': return 'Moderador';
      default: return 'Usuário';
    }
  };

  return (
    <div className={`min-h-screen flex ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } lg:translate-x-0 lg:relative lg:flex lg:flex-col`}>
        <div className={`flex flex-col h-full ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <img src="/img/ymr.png" alt="YMRentals" className="h-8" />
              <div>
                <h2 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  YM Rentals
                </h2>
                <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  {getRoleTitle()}
                </p>
              </div>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden p-1 rounded-md hover:bg-gray-100"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* User Info */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="w-5 h-5 text-blue-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className={`font-medium truncate ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {user?.fullName || 'Usuário'}
                </p>
                <p className={`text-sm truncate ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  {user?.email}
                </p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2">
            {menuItems.map((item) => {
              const IconComponent = item.icon;
              const isActive = activeSection === item.id;
              
              return (
                <button
                  key={item.id}
                  onClick={() => {
                    onSectionChange(item.id);
                    setSidebarOpen(false);
                  }}
                  className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                    isActive
                      ? 'bg-blue-600 text-white'
                      : isDarkMode
                      ? 'text-gray-300 hover:bg-gray-700 hover:text-white'
                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                  }`}
                >
                  <IconComponent className="w-5 h-5" />
                  <span className="flex-1">{item.label}</span>
                  {item.badge && item.badge > 0 && (
                    <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                      {item.badge}
                    </span>
                  )}
                </button>
              );
            })}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200 space-y-2">
            <button
              onClick={() => onSectionChange('settings')}
              className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                activeSection === 'settings'
                  ? 'bg-blue-600 text-white'
                  : isDarkMode
                  ? 'text-gray-300 hover:bg-gray-700 hover:text-white'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              <Settings className="w-5 h-5" />
              <span>Configurações</span>
            </button>
            <button
              onClick={logout}
              className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                isDarkMode
                  ? 'text-red-400 hover:bg-red-900/20 hover:text-red-300'
                  : 'text-red-600 hover:bg-red-50 hover:text-red-700'
              }`}
            >
              <LogOut className="w-5 h-5" />
              <span>Sair</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Mobile Header */}
        <div className={`lg:hidden flex items-center justify-between p-4 ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
          <button
            onClick={() => setSidebarOpen(true)}
            className={`p-2 rounded-md ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
          >
            <Menu className="w-5 h-5" />
          </button>
          <h1 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            YM Rentals - {getRoleTitle()}
          </h1>
          <div className="w-9"></div> {/* Spacer */}
        </div>

        {/* Content */}
        <main className="flex-1 p-4 lg:p-6 overflow-auto">
          {children}
        </main>
      </div>

      {/* Overlay for mobile */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default AdminLayout;
