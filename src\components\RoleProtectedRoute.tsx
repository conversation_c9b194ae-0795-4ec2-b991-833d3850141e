import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface RoleProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles: ('USER' | 'MODERATOR' | 'MODERATOR_MANAGER' | 'ADMIN')[];
  requireApproved?: boolean;
  fallbackPath?: string;
}

const RoleProtectedRoute: React.FC<RoleProtectedRouteProps> = ({
  children,
  allowedRoles,
  requireApproved = true,
  fallbackPath = '/'
}) => {
  const { user, isAuthenticated, isLoading } = useAuth();

  // Aguardar carregamento
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Verificar se está autenticado
  if (!isAuthenticated || !user) {
    return <Navigate to="/auth" replace />;
  }

  // Verificar se a conta está aprovada (se necessário)
  if (requireApproved && user.accountStatus !== 'APPROVED') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center">
          <div className="mb-4">
            <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Conta Pendente de Aprovação
          </h2>
          <p className="text-gray-600 mb-4">
            {user.accountStatus === 'PENDING' && 
              'Sua conta está sendo analisada pela nossa equipe. Você receberá um email quando for aprovada.'
            }
            {user.accountStatus === 'REJECTED' && 
              'Sua conta foi rejeitada. Entre em contato conosco para mais informações.'
            }
            {user.accountStatus === 'SUSPENDED' && 
              'Sua conta foi suspensa. Entre em contato conosco para mais informações.'
            }
          </p>
          <button
            onClick={() => window.location.href = '/contact'}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Entrar em Contato
          </button>
        </div>
      </div>
    );
  }

  // Hierarquia de roles: USER < MODERATOR < MODERATOR_MANAGER < ADMIN
  const roleHierarchy = {
    USER: 1,
    MODERATOR: 2,
    MODERATOR_MANAGER: 3,
    ADMIN: 4,
  };

  // Verificar se tem a role necessária ou superior na hierarquia
  const userRole = user.role || 'USER';
  const userRoleLevel = roleHierarchy[userRole] || 1;
  const hasPermission = allowedRoles.some(allowedRole => {
    const requiredLevel = roleHierarchy[allowedRole] || 1;
    return userRoleLevel >= requiredLevel;
  });

  if (!hasPermission) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center">
          <div className="mb-4">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
              </svg>
            </div>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Acesso Negado
          </h2>
          <p className="text-gray-600 mb-4">
            Você não tem permissão para acessar esta página.
          </p>
          <button
            onClick={() => window.location.href = fallbackPath}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Voltar ao Início
          </button>
        </div>
      </div>
    );
  }

  // Se passou por todas as verificações, renderizar o conteúdo
  return <>{children}</>;
};

export default RoleProtectedRoute;
