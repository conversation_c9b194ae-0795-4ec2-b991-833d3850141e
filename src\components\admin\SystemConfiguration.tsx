import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { 
  Settings, 
  Save, 
  RefreshCw, 
  Shield, 
  Mail, 
  Globe, 
  Database,
  AlertTriangle,
  CheckCircle,
  Loader2,
  Lock,
  Key,
  Server
} from 'lucide-react';

interface SystemConfigurationProps {
  isDarkMode: boolean;
}

interface SystemConfig {
  siteName: string;
  siteDescription: string;
  contactEmail: string;
  supportEmail: string;
  maxFileSize: number;
  allowedFileTypes: string[];
  emailNotifications: boolean;
  smsNotifications: boolean;
  maintenanceMode: boolean;
  registrationEnabled: boolean;
  autoApproveEquipment: boolean;
  autoApproveLandlords: boolean;
  maxEquipmentPerUser: number;
  sessionTimeout: number;
  passwordMinLength: number;
  requireEmailVerification: boolean;
  requirePhoneVerification: boolean;
}

const SystemConfiguration: React.FC<SystemConfigurationProps> = ({ isDarkMode }) => {
  const { user } = useAuth();
  const [config, setConfig] = useState<SystemConfig>({
    siteName: 'YM Rentals',
    siteDescription: 'Plataforma de aluguel de equipamentos em Angola',
    contactEmail: '<EMAIL>',
    supportEmail: '<EMAIL>',
    maxFileSize: 10, // MB
    allowedFileTypes: ['jpg', 'jpeg', 'png', 'pdf'],
    emailNotifications: true,
    smsNotifications: false,
    maintenanceMode: false,
    registrationEnabled: true,
    autoApproveEquipment: false,
    autoApproveLandlords: false,
    maxEquipmentPerUser: 50,
    sessionTimeout: 24, // hours
    passwordMinLength: 6,
    requireEmailVerification: true,
    requirePhoneVerification: false,
  });
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [activeTab, setActiveTab] = useState('general');

  // Verificar se é admin
  if (user?.role !== 'ADMIN') {
    return (
      <div className="text-center py-12">
        <Lock className={`w-12 h-12 mx-auto mb-4 ${isDarkMode ? 'text-gray-600' : 'text-gray-400'}`} />
        <h3 className={`text-lg font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Acesso Restrito
        </h3>
        <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          Apenas administradores podem acessar as configurações do sistema
        </p>
      </div>
    );
  }

  const showMessage = (type: 'success' | 'error', text: string) => {
    setMessage({ type, text });
    setTimeout(() => setMessage(null), 5000);
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      // Aqui seria feita a chamada para a API para salvar as configurações
      // await adminApi.updateSystemConfig(config);
      
      // Simular delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      showMessage('success', 'Configurações salvas com sucesso!');
    } catch (error: any) {
      console.error('Erro ao salvar configurações:', error);
      showMessage('error', error.message || 'Erro ao salvar configurações');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    if (confirm('Tem certeza que deseja restaurar as configurações padrão?')) {
      setConfig({
        siteName: 'YM Rentals',
        siteDescription: 'Plataforma de aluguel de equipamentos em Angola',
        contactEmail: '<EMAIL>',
        supportEmail: '<EMAIL>',
        maxFileSize: 10,
        allowedFileTypes: ['jpg', 'jpeg', 'png', 'pdf'],
        emailNotifications: true,
        smsNotifications: false,
        maintenanceMode: false,
        registrationEnabled: true,
        autoApproveEquipment: false,
        autoApproveLandlords: false,
        maxEquipmentPerUser: 50,
        sessionTimeout: 24,
        passwordMinLength: 6,
        requireEmailVerification: true,
        requirePhoneVerification: false,
      });
      showMessage('success', 'Configurações restauradas para os valores padrão');
    }
  };

  const tabs = [
    { id: 'general', name: 'Geral', icon: Settings },
    { id: 'security', name: 'Segurança', icon: Shield },
    { id: 'notifications', name: 'Notificações', icon: Mail },
    { id: 'system', name: 'Sistema', icon: Server },
  ];

  const renderGeneralTab = () => (
    <div className="space-y-6">
      <div>
        <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
          Nome do Site
        </label>
        <input
          type="text"
          value={config.siteName}
          onChange={(e) => setConfig({ ...config, siteName: e.target.value })}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            isDarkMode 
              ? 'bg-gray-700 border-gray-600 text-white' 
              : 'bg-white border-gray-300 text-gray-900'
          }`}
        />
      </div>

      <div>
        <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
          Descrição do Site
        </label>
        <textarea
          value={config.siteDescription}
          onChange={(e) => setConfig({ ...config, siteDescription: e.target.value })}
          rows={3}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            isDarkMode 
              ? 'bg-gray-700 border-gray-600 text-white' 
              : 'bg-white border-gray-300 text-gray-900'
          }`}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Email de Contato
          </label>
          <input
            type="email"
            value={config.contactEmail}
            onChange={(e) => setConfig({ ...config, contactEmail: e.target.value })}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              isDarkMode 
                ? 'bg-gray-700 border-gray-600 text-white' 
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          />
        </div>

        <div>
          <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Email de Suporte
          </label>
          <input
            type="email"
            value={config.supportEmail}
            onChange={(e) => setConfig({ ...config, supportEmail: e.target.value })}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              isDarkMode 
                ? 'bg-gray-700 border-gray-600 text-white' 
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Tamanho Máximo de Arquivo (MB)
          </label>
          <input
            type="number"
            value={config.maxFileSize}
            onChange={(e) => setConfig({ ...config, maxFileSize: parseInt(e.target.value) })}
            min="1"
            max="100"
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              isDarkMode 
                ? 'bg-gray-700 border-gray-600 text-white' 
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          />
        </div>

        <div>
          <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Máximo de Equipamentos por Usuário
          </label>
          <input
            type="number"
            value={config.maxEquipmentPerUser}
            onChange={(e) => setConfig({ ...config, maxEquipmentPerUser: parseInt(e.target.value) })}
            min="1"
            max="1000"
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              isDarkMode 
                ? 'bg-gray-700 border-gray-600 text-white' 
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          />
        </div>
      </div>
    </div>
  );

  const renderSecurityTab = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Tempo Limite da Sessão (horas)
          </label>
          <input
            type="number"
            value={config.sessionTimeout}
            onChange={(e) => setConfig({ ...config, sessionTimeout: parseInt(e.target.value) })}
            min="1"
            max="168"
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              isDarkMode 
                ? 'bg-gray-700 border-gray-600 text-white' 
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          />
        </div>

        <div>
          <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Comprimento Mínimo da Senha
          </label>
          <input
            type="number"
            value={config.passwordMinLength}
            onChange={(e) => setConfig({ ...config, passwordMinLength: parseInt(e.target.value) })}
            min="4"
            max="20"
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              isDarkMode 
                ? 'bg-gray-700 border-gray-600 text-white' 
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          />
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Verificação de Email Obrigatória
            </h4>
            <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Usuários devem verificar email antes de usar a plataforma
            </p>
          </div>
          <input
            type="checkbox"
            checked={config.requireEmailVerification}
            onChange={(e) => setConfig({ ...config, requireEmailVerification: e.target.checked })}
            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          />
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h4 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Verificação de Telefone Obrigatória
            </h4>
            <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Usuários devem verificar telefone antes de usar a plataforma
            </p>
          </div>
          <input
            type="checkbox"
            checked={config.requirePhoneVerification}
            onChange={(e) => setConfig({ ...config, requirePhoneVerification: e.target.checked })}
            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          />
        </div>
      </div>
    </div>
  );

  const renderNotificationsTab = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Notificações por Email
            </h4>
            <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Enviar notificações importantes por email
            </p>
          </div>
          <input
            type="checkbox"
            checked={config.emailNotifications}
            onChange={(e) => setConfig({ ...config, emailNotifications: e.target.checked })}
            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          />
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h4 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Notificações por SMS
            </h4>
            <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Enviar notificações críticas por SMS
            </p>
          </div>
          <input
            type="checkbox"
            checked={config.smsNotifications}
            onChange={(e) => setConfig({ ...config, smsNotifications: e.target.checked })}
            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          />
        </div>
      </div>
    </div>
  );

  const renderSystemTab = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Modo de Manutenção
            </h4>
            <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Desabilitar acesso público ao site
            </p>
          </div>
          <input
            type="checkbox"
            checked={config.maintenanceMode}
            onChange={(e) => setConfig({ ...config, maintenanceMode: e.target.checked })}
            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          />
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h4 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Registro Habilitado
            </h4>
            <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Permitir novos registros de usuários
            </p>
          </div>
          <input
            type="checkbox"
            checked={config.registrationEnabled}
            onChange={(e) => setConfig({ ...config, registrationEnabled: e.target.checked })}
            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          />
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h4 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Auto-aprovar Equipamentos
            </h4>
            <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Aprovar automaticamente novos equipamentos
            </p>
          </div>
          <input
            type="checkbox"
            checked={config.autoApproveEquipment}
            onChange={(e) => setConfig({ ...config, autoApproveEquipment: e.target.checked })}
            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          />
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h4 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Auto-aprovar Locadores
            </h4>
            <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Aprovar automaticamente novos locadores
            </p>
          </div>
          <input
            type="checkbox"
            checked={config.autoApproveLandlords}
            onChange={(e) => setConfig({ ...config, autoApproveLandlords: e.target.checked })}
            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          />
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Configurações do Sistema
          </h2>
          <p className={`mt-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Configure parâmetros avançados da plataforma
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleReset}
            className={`flex items-center space-x-2 px-4 py-2 border rounded-lg transition-colors ${
              isDarkMode 
                ? 'border-gray-600 text-gray-300 hover:bg-gray-700' 
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            <RefreshCw className="w-4 h-4" />
            <span>Restaurar</span>
          </button>
          <button
            onClick={handleSave}
            disabled={saving}
            className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {saving ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Save className="w-4 h-4" />
            )}
            <span>{saving ? 'Salvando...' : 'Salvar'}</span>
          </button>
        </div>
      </div>

      {/* Message */}
      {message && (
        <div className={`p-4 rounded-lg flex items-center space-x-2 ${
          message.type === 'success' 
            ? 'bg-green-100 text-green-800 border border-green-200' 
            : 'bg-red-100 text-red-800 border border-red-200'
        }`}>
          {message.type === 'success' ? (
            <CheckCircle className="w-5 h-5" />
          ) : (
            <AlertTriangle className="w-5 h-5" />
          )}
          <span>{message.text}</span>
        </div>
      )}

      {/* Tabs */}
      <div className={`border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <nav className="flex space-x-8">
          {tabs.map((tab) => {
            const IconComponent = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : `border-transparent ${isDarkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'}`
                }`}
              >
                <IconComponent className="w-4 h-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className={`p-6 rounded-lg shadow-sm ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
        {activeTab === 'general' && renderGeneralTab()}
        {activeTab === 'security' && renderSecurityTab()}
        {activeTab === 'notifications' && renderNotificationsTab()}
        {activeTab === 'system' && renderSystemTab()}
      </div>
    </div>
  );
};

export default SystemConfiguration;
