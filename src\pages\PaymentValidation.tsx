import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Button } from '../components/ui/button';
import BackButton from '../components/ui/BackButton';
import {
  FileText, CheckCircle, XCircle, Eye, Calendar, User,
  CreditCard, AlertCircle, Loader2, Clock, DollarSign
} from 'lucide-react';
import apiService from '../services/api';

interface PaymentValidationProps {
  isDarkMode?: boolean;
}

interface PendingRental {
  id: string;
  totalAmount: number;
  startDate: string;
  endDate: string;
  paymentReceipt: string;
  paymentReceiptStatus: string;
  createdAt: string;
  equipment: {
    id: string;
    name: string;
    images: string[];
  };
  renter: {
    id: string;
    fullName: string;
    email: string;
    profilePicture?: string;
  };
  owner: {
    id: string;
    fullName: string;
    email: string;
  };
}

export default function PaymentValidation({ isDarkMode = false }: PaymentValidationProps) {
  const { user } = useAuth();
  const [pendingRentals, setPendingRentals] = useState<PendingRental[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [selectedReceipt, setSelectedReceipt] = useState<string | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectionModal, setShowRejectionModal] = useState(false);
  const [currentRentalId, setCurrentRentalId] = useState<string | null>(null);

  useEffect(() => {
    fetchPendingRentals();
  }, []);

  const fetchPendingRentals = async () => {
    try {
      setLoading(true);
      const data = await apiService.getPendingPaymentReceipts();
      setPendingRentals(data);
    } catch (error: any) {
      setError(error.message || 'Erro ao carregar comprovativos pendentes');
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (rentalId: string) => {
    try {
      setProcessingId(rentalId);
      await apiService.validatePaymentReceipt(rentalId, true);
      
      // Remover da lista
      setPendingRentals(prev => prev.filter(rental => rental.id !== rentalId));
      
      // Mostrar sucesso
      alert('Comprovativo aprovado com sucesso!');
    } catch (error: any) {
      setError(error.message || 'Erro ao aprovar comprovativo');
    } finally {
      setProcessingId(null);
    }
  };

  const handleReject = (rentalId: string) => {
    setCurrentRentalId(rentalId);
    setShowRejectionModal(true);
  };

  const confirmReject = async () => {
    if (!currentRentalId || !rejectionReason.trim()) {
      setError('Motivo da rejeição é obrigatório');
      return;
    }

    try {
      setProcessingId(currentRentalId);
      await apiService.validatePaymentReceipt(currentRentalId, false, rejectionReason);
      
      // Remover da lista
      setPendingRentals(prev => prev.filter(rental => rental.id !== currentRentalId));
      
      // Fechar modal e limpar
      setShowRejectionModal(false);
      setRejectionReason('');
      setCurrentRentalId(null);
      
      alert('Comprovativo rejeitado com sucesso!');
    } catch (error: any) {
      setError(error.message || 'Erro ao rejeitar comprovativo');
    } finally {
      setProcessingId(null);
    }
  };

  const openReceiptModal = (receiptUrl: string) => {
    setSelectedReceipt(receiptUrl);
  };

  const closeReceiptModal = () => {
    setSelectedReceipt(null);
  };

  if (!user || !['MODERATOR', 'MANAGER', 'ADMIN'].includes(user.role || '')) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className={`text-xl font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Acesso Negado
          </h2>
          <p className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>
            Apenas moderadores podem acessar esta página.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="container mx-auto px-4 py-8 pt-24">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-6">
            <BackButton isDarkMode={isDarkMode} />
          </div>

          <div className="mb-8">
            <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Validação de Comprovativos
            </h1>
            <p className={`mt-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Analise e valide os comprovativos de pagamento enviados pelos locatários
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
              <div className="flex items-center">
                <AlertCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">{error}</span>
              </div>
            </div>
          )}

          {/* Loading State */}
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
              <span className={`ml-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Carregando comprovativos...
              </span>
            </div>
          ) : pendingRentals.length === 0 ? (
            <div className={`text-center py-12 ${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow`}>
              <FileText className={`w-16 h-16 mx-auto mb-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-300'}`} />
              <h3 className={`text-lg font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Nenhum comprovativo pendente
              </h3>
              <p className={isDarkMode ? 'text-gray-400' : 'text-gray-600'}>
                Todos os comprovativos foram processados.
              </p>
            </div>
          ) : (
            <div className="grid gap-6">
              {pendingRentals.map((rental) => (
                <div
                  key={rental.id}
                  className={`rounded-lg shadow p-6 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <img
                        src={rental.equipment.images[0] || '/placeholder-equipment.jpg'}
                        alt={rental.equipment.name}
                        className="w-16 h-16 rounded-lg object-cover"
                      />
                      <div>
                        <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {rental.equipment.name}
                        </h3>
                        <div className="flex items-center mt-1">
                          <User className="w-4 h-4 mr-1 text-gray-400" />
                          <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                            {rental.renter.fullName}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4 text-yellow-500" />
                      <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                        Pendente
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className={`p-3 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                      <div className="flex items-center mb-1">
                        <Calendar className="w-4 h-4 mr-1 text-blue-500" />
                        <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                          Período
                        </span>
                      </div>
                      <p className={`text-sm ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {new Date(rental.startDate).toLocaleDateString('pt-BR')} até{' '}
                        {new Date(rental.endDate).toLocaleDateString('pt-BR')}
                      </p>
                    </div>

                    <div className={`p-3 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                      <div className="flex items-center mb-1">
                        <DollarSign className="w-4 h-4 mr-1 text-green-500" />
                        <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                          Valor Total
                        </span>
                      </div>
                      <p className={`text-sm font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {rental.totalAmount.toLocaleString()} Kz
                      </p>
                    </div>

                    <div className={`p-3 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                      <div className="flex items-center mb-1">
                        <User className="w-4 h-4 mr-1 text-purple-500" />
                        <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                          Proprietário
                        </span>
                      </div>
                      <p className={`text-sm ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {rental.owner.fullName}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <Button
                      onClick={() => openReceiptModal(rental.paymentReceipt)}
                      variant="outline"
                      className="flex items-center space-x-2"
                    >
                      <Eye className="w-4 h-4" />
                      <span>Ver Comprovativo</span>
                    </Button>

                    <div className="flex space-x-3">
                      <Button
                        onClick={() => handleReject(rental.id)}
                        disabled={processingId === rental.id}
                        variant="outline"
                        className="flex items-center space-x-2 text-red-600 border-red-600 hover:bg-red-50"
                      >
                        <XCircle className="w-4 h-4" />
                        <span>Rejeitar</span>
                      </Button>

                      <Button
                        onClick={() => handleApprove(rental.id)}
                        disabled={processingId === rental.id}
                        className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white"
                      >
                        {processingId === rental.id ? (
                          <Loader2 className="w-4 h-4 animate-spin" />
                        ) : (
                          <CheckCircle className="w-4 h-4" />
                        )}
                        <span>Aprovar</span>
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Receipt Modal */}
      {selectedReceipt && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className={`max-w-4xl w-full max-h-[90vh] overflow-y-auto rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Comprovativo de Pagamento
                </h3>
                <Button onClick={closeReceiptModal} variant="outline" size="sm">
                  Fechar
                </Button>
              </div>
              
              <div className="text-center">
                {selectedReceipt.toLowerCase().includes('.pdf') ? (
                  <iframe
                    src={selectedReceipt}
                    className="w-full h-96 border rounded"
                    title="Comprovativo PDF"
                  />
                ) : (
                  <img
                    src={selectedReceipt}
                    alt="Comprovativo"
                    className="max-w-full h-auto rounded"
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Rejection Modal */}
      {showRejectionModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className={`max-w-md w-full rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <div className="p-6">
              <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Rejeitar Comprovativo
              </h3>
              
              <div className="mb-4">
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Motivo da rejeição *
                </label>
                <textarea
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  rows={4}
                  className={`w-full px-3 py-2 border rounded-md ${isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'}`}
                  placeholder="Explique o motivo da rejeição..."
                />
              </div>

              <div className="flex justify-end space-x-3">
                <Button
                  onClick={() => {
                    setShowRejectionModal(false);
                    setRejectionReason('');
                    setCurrentRentalId(null);
                  }}
                  variant="outline"
                >
                  Cancelar
                </Button>
                
                <Button
                  onClick={confirmReject}
                  disabled={!rejectionReason.trim() || processingId === currentRentalId}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  {processingId === currentRentalId ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Rejeitando...
                    </>
                  ) : (
                    'Confirmar Rejeição'
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
