import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Check, X, AlertCircle, Mail } from 'lucide-react';
import { Button } from '../components/ui/button';

interface VerifyEmailProps {
  isDarkMode?: boolean;
}

export default function VerifyEmail({ isDarkMode = false }: VerifyEmailProps) {
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    if (token) {
      verifyEmail(token);
    } else {
      setStatus('error');
      setMessage('Token de verificação inválido');
    }
  }, [token]);

  const verifyEmail = async (verificationToken: string) => {
    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:3000'}/users/verify-email/${verificationToken}`, {
        method: 'POST',
      });

      if (response.ok) {
        setStatus('success');
        setMessage('Email verificado com sucesso!');
      } else {
        const errorData = await response.json();
        setStatus('error');
        setMessage(errorData.message || 'Erro ao verificar email');
      }
    } catch (error) {
      setStatus('error');
      setMessage('Erro ao conectar com o servidor');
    }
  };

  return (
    <div className={`min-h-screen flex items-center justify-center ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
      <div className={`max-w-md w-full mx-4 p-8 rounded-lg shadow-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
        <div className="text-center">
          {status === 'loading' && (
            <>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-500 mx-auto mb-4"></div>
              <h2 className={`text-xl font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Verificando Email
              </h2>
              <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Aguarde enquanto verificamos seu email...
              </p>
            </>
          )}

          {status === 'success' && (
            <>
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Check className="w-8 h-8 text-green-600" />
              </div>
              <h2 className={`text-xl font-semibold mb-2 text-green-600`}>
                Email Verificado!
              </h2>
              <p className={`mb-6 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {message}
              </p>
              <p className={`text-sm mb-6 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                Seu email foi verificado com sucesso. Agora você pode acessar todas as funcionalidades da plataforma.
              </p>
              <Button
                onClick={() => navigate('/profile')}
                className="w-full bg-cyan-500 hover:bg-cyan-600 text-white"
              >
                Ir para o Perfil
              </Button>
            </>
          )}

          {status === 'error' && (
            <>
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <X className="w-8 h-8 text-red-600" />
              </div>
              <h2 className={`text-xl font-semibold mb-2 text-red-600`}>
                Erro na Verificação
              </h2>
              <p className={`mb-6 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {message}
              </p>
              <div className="space-y-3">
                <Button
                  onClick={() => navigate('/profile')}
                  className="w-full bg-cyan-500 hover:bg-cyan-600 text-white"
                >
                  Voltar ao Perfil
                </Button>
                <Button
                  onClick={() => window.location.reload()}
                  variant="outline"
                  className="w-full"
                >
                  Tentar Novamente
                </Button>
              </div>
            </>
          )}
        </div>

        <div className={`mt-8 pt-6 border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex items-center justify-center text-sm">
            <Mail className={`w-4 h-4 mr-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
            <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              Problemas? Entre em contato conosco
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
