import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import apiService from '../services/api';

interface Equipment {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  images: string[];
  moderationStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
  moderatedBy?: string;
  moderatedAt?: string;
  rejectionReason?: string;
  createdAt: string;
  owner: {
    id: string;
    fullName: string;
    email: string;
    companyName?: string;
    companyType?: string;
  };
}

interface ModerationStats {
  pending: number;
  approved: number;
  rejected: number;
  myApproved: number;
  myRejected: number;
  myTotal: number;
}

interface ModerationContextType {
  pendingEquipment: Equipment[];
  moderationHistory: Equipment[];
  stats: ModerationStats | null;
  loading: boolean;
  error: string | null;
  fetchPendingEquipment: () => Promise<void>;
  fetchModerationHistory: () => Promise<void>;
  fetchStats: () => Promise<void>;
  approveEquipment: (id: string) => Promise<void>;
  rejectEquipment: (id: string, reason: string) => Promise<void>;
  getEquipmentDetails: (id: string) => Promise<Equipment | null>;
}

const ModerationContext = createContext<ModerationContextType | undefined>(undefined);

export const useModeration = () => {
  const context = useContext(ModerationContext);
  if (context === undefined) {
    throw new Error('useModeration must be used within a ModerationProvider');
  }
  return context;
};

interface ModerationProviderProps {
  children: ReactNode;
}

export const ModerationProvider: React.FC<ModerationProviderProps> = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const [pendingEquipment, setPendingEquipment] = useState<Equipment[]>([]);
  const [moderationHistory, setModerationHistory] = useState<Equipment[]>([]);
  const [stats, setStats] = useState<ModerationStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Verificar se usuário é moderador
  const isModerator = user?.role && ['MODERATOR', 'MANAGER', 'ADMIN'].includes(user.role);

  useEffect(() => {
    if (isAuthenticated && isModerator) {
      fetchPendingEquipment();
      fetchStats();
    }
  }, [isAuthenticated, isModerator]);

  const fetchPendingEquipment = async (): Promise<void> => {
    try {
      setLoading(true);
      const response = await apiService.getPendingEquipment();
      setPendingEquipment(response.data || []);
    } catch (err: any) {
      setError(err.message || 'Erro ao carregar equipamentos pendentes');
    } finally {
      setLoading(false);
    }
  };

  const fetchModerationHistory = async (): Promise<void> => {
    try {
      setLoading(true);
      const response = await apiService.getModerationHistory();
      setModerationHistory(response.data || []);
    } catch (err: any) {
      setError(err.message || 'Erro ao carregar histórico de moderação');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async (): Promise<void> => {
    try {
      const response = await apiService.getModerationStats();
      setStats(response);
    } catch (err: any) {
      setError(err.message || 'Erro ao carregar estatísticas');
    }
  };

  const approveEquipment = async (id: string): Promise<void> => {
    try {
      await apiService.approveEquipment(id);
      
      // Remover da lista de pendentes
      setPendingEquipment(prev => prev.filter(eq => eq.id !== id));
      
      // Atualizar estatísticas
      fetchStats();
    } catch (err: any) {
      setError(err.message || 'Erro ao aprovar equipamento');
      throw err;
    }
  };

  const rejectEquipment = async (id: string, reason: string): Promise<void> => {
    try {
      await apiService.rejectEquipment(id, reason);
      
      // Remover da lista de pendentes
      setPendingEquipment(prev => prev.filter(eq => eq.id !== id));
      
      // Atualizar estatísticas
      fetchStats();
    } catch (err: any) {
      setError(err.message || 'Erro ao rejeitar equipamento');
      throw err;
    }
  };

  const getEquipmentDetails = async (id: string): Promise<Equipment | null> => {
    try {
      const response = await apiService.getEquipmentForModeration(id);
      return response;
    } catch (err: any) {
      setError(err.message || 'Erro ao carregar detalhes do equipamento');
      return null;
    }
  };

  const value: ModerationContextType = {
    pendingEquipment,
    moderationHistory,
    stats,
    loading,
    error,
    fetchPendingEquipment,
    fetchModerationHistory,
    fetchStats,
    approveEquipment,
    rejectEquipment,
    getEquipmentDetails,
  };

  return (
    <ModerationContext.Provider value={value}>
      {children}
    </ModerationContext.Provider>
  );
};
