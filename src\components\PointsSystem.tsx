import React, { useState } from 'react';
import { 
  Star, 
  Gift, 
  Trophy, 
  Target, 
  TrendingUp, 
  Award,
  Zap,
  Crown,
  Medal,
  Coins
} from 'lucide-react';
import { Button } from './ui/button';

interface PointsTransaction {
  id: string;
  type: 'earned' | 'spent' | 'bonus';
  points: number;
  description: string;
  date: string;
  category: 'rating' | 'rental' | 'referral' | 'bonus' | 'redemption';
}

interface Reward {
  id: string;
  name: string;
  description: string;
  pointsCost: number;
  type: 'discount' | 'priority' | 'upgrade' | 'cashback';
  value: string;
  available: boolean;
  icon: string;
}

interface PointsSystemProps {
  isDarkMode: boolean;
}

const PointsSystem: React.FC<PointsSystemProps> = ({ isDarkMode }) => {
  const [currentPoints, setCurrentPoints] = useState(1250);
  const [totalEarned, setTotalEarned] = useState(3450);
  const [userLevel, setUserLevel] = useState('Gold');
  const [activeTab, setActiveTab] = useState<'overview' | 'earn' | 'rewards' | 'history'>('overview');

  const [transactions, setTransactions] = useState<PointsTransaction[]>([
    {
      id: '1',
      type: 'earned',
      points: 50,
      description: 'Avaliação 5 estrelas - Escavadeira CAT 320',
      date: '2024-01-15T10:30:00',
      category: 'rating'
    },
    {
      id: '2',
      type: 'earned',
      points: 100,
      description: 'Aluguel concluído - Gerador 100kVA',
      date: '2024-01-14T14:20:00',
      category: 'rental'
    },
    {
      id: '3',
      type: 'spent',
      points: -200,
      description: 'Desconto de 10% aplicado',
      date: '2024-01-13T09:15:00',
      category: 'redemption'
    },
    {
      id: '4',
      type: 'earned',
      points: 150,
      description: 'Indicação de novo usuário',
      date: '2024-01-12T16:45:00',
      category: 'referral'
    },
    {
      id: '5',
      type: 'bonus',
      points: 300,
      description: 'Bônus de boas-vindas',
      date: '2024-01-10T11:30:00',
      category: 'bonus'
    }
  ]);

  const [rewards, setRewards] = useState<Reward[]>([
    {
      id: '1',
      name: 'Desconto 5%',
      description: 'Desconto de 5% no próximo aluguel',
      pointsCost: 100,
      type: 'discount',
      value: '5%',
      available: true,
      icon: 'gift'
    },
    {
      id: '2',
      name: 'Desconto 10%',
      description: 'Desconto de 10% no próximo aluguel',
      pointsCost: 200,
      type: 'discount',
      value: '10%',
      available: true,
      icon: 'gift'
    },
    {
      id: '3',
      name: 'Prioridade Premium',
      description: 'Prioridade máxima na fila por 30 dias',
      pointsCost: 500,
      type: 'priority',
      value: '30 dias',
      available: true,
      icon: 'crown'
    },
    {
      id: '4',
      name: 'Upgrade Gratuito',
      description: 'Upgrade gratuito para equipamento superior',
      pointsCost: 300,
      type: 'upgrade',
      value: '1x',
      available: true,
      icon: 'trophy'
    },
    {
      id: '5',
      name: 'Cashback 50.000 Kz',
      description: 'Crédito de 50.000 Kz na carteira digital',
      pointsCost: 1000,
      type: 'cashback',
      value: '50.000 Kz',
      available: true,
      icon: 'coins'
    },
    {
      id: '6',
      name: 'Desconto 20%',
      description: 'Desconto de 20% no próximo aluguel',
      pointsCost: 800,
      type: 'discount',
      value: '20%',
      available: false,
      icon: 'gift'
    }
  ]);

  // Função para resgatar recompensa
  const redeemReward = (rewardId: string) => {
    const reward = rewards.find(r => r.id === rewardId);
    if (!reward || !reward.available || currentPoints < reward.pointsCost) {
      return;
    }

    // Deduzir pontos
    setCurrentPoints(prev => prev - reward.pointsCost);

    // Adicionar transação
    const newTransaction: PointsTransaction = {
      id: Date.now().toString(),
      type: 'spent',
      points: -reward.pointsCost,
      description: `Resgate: ${reward.name}`,
      date: new Date().toISOString(),
      category: 'redemption'
    };

    setTransactions(prev => [newTransaction, ...prev]);

    alert(`Recompensa "${reward.name}" resgatada com sucesso!`);
  };

  // Função para simular ganho de pontos por avaliação
  const simulateRatingPoints = () => {
    const points = 50;
    setCurrentPoints(prev => prev + points);
    setTotalEarned(prev => prev + points);

    const newTransaction: PointsTransaction = {
      id: Date.now().toString(),
      type: 'earned',
      points: points,
      description: 'Avaliação 5 estrelas concedida',
      date: new Date().toISOString(),
      category: 'rating'
    };

    setTransactions(prev => [newTransaction, ...prev]);
  };

  // Função para obter ícone da recompensa
  const getRewardIcon = (iconType: string) => {
    switch (iconType) {
      case 'gift': return <Gift className="w-6 h-6" />;
      case 'crown': return <Crown className="w-6 h-6" />;
      case 'trophy': return <Trophy className="w-6 h-6" />;
      case 'coins': return <Coins className="w-6 h-6" />;
      default: return <Gift className="w-6 h-6" />;
    }
  };

  // Função para obter cor da transação
  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'earned': return 'text-green-600';
      case 'spent': return 'text-red-600';
      case 'bonus': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  // Função para obter ícone da transação
  const getTransactionIcon = (category: string) => {
    switch (category) {
      case 'rating': return <Star className="w-4 h-4 text-yellow-500" />;
      case 'rental': return <Target className="w-4 h-4 text-blue-500" />;
      case 'referral': return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'bonus': return <Zap className="w-4 h-4 text-purple-500" />;
      case 'redemption': return <Gift className="w-4 h-4 text-red-500" />;
      default: return <Star className="w-4 h-4 text-gray-500" />;
    }
  };

  // Calcular progresso para próximo nível
  const getNextLevelProgress = () => {
    const levels = {
      'Bronze': 0,
      'Silver': 1000,
      'Gold': 2500,
      'Platinum': 5000,
      'Diamond': 10000
    };
    
    const currentLevelPoints = levels[userLevel as keyof typeof levels] || 0;
    const nextLevel = Object.entries(levels).find(([_, points]) => points > totalEarned);
    
    if (!nextLevel) return { progress: 100, nextLevel: 'Diamond', pointsNeeded: 0 };
    
    const [nextLevelName, nextLevelPoints] = nextLevel;
    const progress = ((totalEarned - currentLevelPoints) / (nextLevelPoints - currentLevelPoints)) * 100;
    
    return {
      progress: Math.min(progress, 100),
      nextLevel: nextLevelName,
      pointsNeeded: nextLevelPoints - totalEarned
    };
  };

  const levelProgress = getNextLevelProgress();

  return (
    <div className={`${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'} min-h-screen p-6`}>
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Sistema de Pontos</h1>
          <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Ganhe pontos e troque por recompensas incríveis
          </p>
        </div>

        {/* Card de pontos principais */}
        <div className={`${isDarkMode ? 'bg-gradient-to-r from-blue-900 to-purple-900' : 'bg-gradient-to-r from-blue-600 to-purple-600'} p-8 rounded-lg shadow-lg mb-8 text-white`}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="flex justify-center mb-2">
                <Star className="w-12 h-12" />
              </div>
              <h3 className="text-lg font-medium mb-1">Pontos Atuais</h3>
              <p className="text-3xl font-bold">{currentPoints.toLocaleString()}</p>
            </div>
            
            <div className="text-center">
              <div className="flex justify-center mb-2">
                <Award className="w-12 h-12" />
              </div>
              <h3 className="text-lg font-medium mb-1">Nível Atual</h3>
              <p className="text-3xl font-bold">{userLevel}</p>
            </div>
            
            <div className="text-center">
              <div className="flex justify-center mb-2">
                <Trophy className="w-12 h-12" />
              </div>
              <h3 className="text-lg font-medium mb-1">Total Ganho</h3>
              <p className="text-3xl font-bold">{totalEarned.toLocaleString()}</p>
            </div>
          </div>
          
          {/* Progresso para próximo nível */}
          <div className="mt-6">
            <div className="flex justify-between items-center mb-2">
              <span>Progresso para {levelProgress.nextLevel}</span>
              <span>{levelProgress.pointsNeeded > 0 ? `${levelProgress.pointsNeeded} pontos restantes` : 'Nível máximo!'}</span>
            </div>
            <div className="w-full bg-white/20 rounded-full h-2">
              <div 
                className="bg-white h-2 rounded-full transition-all duration-300"
                style={{ width: `${levelProgress.progress}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Navegação por abas */}
        <div className="mb-6">
          <div className="flex space-x-1 bg-gray-200 dark:bg-gray-800 p-1 rounded-lg">
            {[
              { key: 'overview', label: 'Visão Geral', icon: Star },
              { key: 'earn', label: 'Ganhar Pontos', icon: TrendingUp },
              { key: 'rewards', label: 'Recompensas', icon: Gift },
              { key: 'history', label: 'Histórico', icon: Medal }
            ].map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                  activeTab === tab.key
                    ? 'bg-white dark:bg-gray-700 shadow-sm'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
                style={{
                  backgroundColor: activeTab === tab.key ? '#3569b0' : undefined,
                  color: activeTab === tab.key ? 'white' : undefined
                }}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Conteúdo das abas */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Formas de ganhar pontos */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
              <h3 className="text-lg font-semibold mb-4">Como Ganhar Pontos</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Star className="w-5 h-5 text-yellow-500" />
                  <div>
                    <p className="font-medium">Avaliações 5 estrelas</p>
                    <p className="text-sm text-gray-500">50 pontos por avaliação</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Target className="w-5 h-5 text-blue-500" />
                  <div>
                    <p className="font-medium">Completar aluguéis</p>
                    <p className="text-sm text-gray-500">100 pontos por aluguel</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <TrendingUp className="w-5 h-5 text-green-500" />
                  <div>
                    <p className="font-medium">Indicar amigos</p>
                    <p className="text-sm text-gray-500">150 pontos por indicação</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Recompensas em destaque */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
              <h3 className="text-lg font-semibold mb-4">Recompensas em Destaque</h3>
              <div className="space-y-4">
                {rewards.slice(0, 3).map(reward => (
                  <div key={reward.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getRewardIcon(reward.icon)}
                      <div>
                        <p className="font-medium">{reward.name}</p>
                        <p className="text-sm text-gray-500">{reward.pointsCost} pontos</p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => redeemReward(reward.id)}
                      disabled={currentPoints < reward.pointsCost || !reward.available}
                      style={{backgroundColor: '#3569b0'}}
                    >
                      Resgatar
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'earn' && (
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md`}>
            <h3 className="text-lg font-semibold mb-6">Ganhe Pontos Agora</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-6 border rounded-lg border-gray-200 dark:border-gray-700">
                <Star className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
                <h4 className="font-semibold mb-2">Faça uma Avaliação</h4>
                <p className="text-sm text-gray-500 mb-4">Avalie um equipamento que você alugou</p>
                <Button
                  onClick={simulateRatingPoints}
                  className="w-full"
                  style={{backgroundColor: '#3569b0'}}
                >
                  +50 Pontos
                </Button>
              </div>
              
              <div className="text-center p-6 border rounded-lg border-gray-200 dark:border-gray-700">
                <TrendingUp className="w-12 h-12 text-green-500 mx-auto mb-4" />
                <h4 className="font-semibold mb-2">Indique um Amigo</h4>
                <p className="text-sm text-gray-500 mb-4">Convide amigos para a plataforma</p>
                <Button variant="outline" className="w-full">
                  +150 Pontos
                </Button>
              </div>
              
              <div className="text-center p-6 border rounded-lg border-gray-200 dark:border-gray-700">
                <Target className="w-12 h-12 text-blue-500 mx-auto mb-4" />
                <h4 className="font-semibold mb-2">Complete um Aluguel</h4>
                <p className="text-sm text-gray-500 mb-4">Finalize um aluguel com sucesso</p>
                <Button variant="outline" className="w-full">
                  +100 Pontos
                </Button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'rewards' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {rewards.map(reward => (
              <div key={reward.id} className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-md ${!reward.available ? 'opacity-50' : ''}`}>
                <div className="flex items-center justify-between mb-4">
                  {getRewardIcon(reward.icon)}
                  <span className={`px-2 py-1 rounded text-xs ${reward.available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {reward.available ? 'Disponível' : 'Indisponível'}
                  </span>
                </div>
                <h4 className="font-semibold mb-2">{reward.name}</h4>
                <p className="text-sm text-gray-500 mb-4">{reward.description}</p>
                <div className="flex items-center justify-between">
                  <span className="font-bold text-lg">{reward.pointsCost} pontos</span>
                  <Button
                    size="sm"
                    onClick={() => redeemReward(reward.id)}
                    disabled={currentPoints < reward.pointsCost || !reward.available}
                    style={{backgroundColor: '#3569b0'}}
                  >
                    Resgatar
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'history' && (
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md`}>
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold">Histórico de Pontos</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {transactions.map(transaction => (
                  <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-md border-gray-200 dark:border-gray-700">
                    <div className="flex items-center space-x-3">
                      {getTransactionIcon(transaction.category)}
                      <div>
                        <p className="font-medium">{transaction.description}</p>
                        <p className="text-sm text-gray-500">
                          {new Date(transaction.date).toLocaleString('pt-AO')}
                        </p>
                      </div>
                    </div>
                    <div className={`font-semibold ${getTransactionColor(transaction.type)}`}>
                      {transaction.points > 0 ? '+' : ''}{transaction.points} pts
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PointsSystem;
