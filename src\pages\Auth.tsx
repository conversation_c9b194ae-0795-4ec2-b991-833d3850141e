import React, { useState, useEffect } from 'react';
import {
  Mail, Lock, User, ArrowRight, Phone, Calendar,
  MapPin, CreditCard, Users, Building, AlertCircle,
  SkipForward, UserCheck, Store
} from 'lucide-react';
import { Button } from '../components/ui/button';

interface AuthProps {
  isDarkMode?: boolean;
}

export default function Auth({ isDarkMode = false }: AuthProps) {
  const [isLogin, setIsLogin] = useState(true);
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    birthDate: '',
    gender: '',
    nif: '',
    address: '',
    accountType: '', // Locador ou Locatário
    entityType: '', // Individual ou Empresa
    profileType: '',
    companyName: '', // Nome da empresa (quando entityType === 'empresa')
    companyAddress: '' // Morada da empresa (quando entityType === 'empresa')
  });
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  // Validação de formulário
  const validateForm = (data, fields = null) => {
    const fieldsToValidate = fields || Object.keys(data);
    const newErrors = {};

    fieldsToValidate.forEach(field => {
      // Pular campos não relevantes para o login
      if (isLogin && field !== 'email' && field !== 'phone' && field !== 'password') return;

      // Validações específicas por campo
      switch (field) {
        case 'name':
          if (!data.name) newErrors.name = 'Nome é obrigatório';
          else if (data.name.length < 3) newErrors.name = 'Nome deve ter pelo menos 3 caracteres';
          break;

        case 'email':
          // Email é obrigatório apenas se o telefone não estiver preenchido
          if (!data.email && !data.phone) {
            newErrors.email = 'Email ou telefone é obrigatório';
          } else if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
            newErrors.email = 'Email inválido';
          }
          break;

        case 'phone':
          // Telefone é obrigatório apenas se o email não estiver preenchido
          if (!data.phone && !data.email) {
            newErrors.phone = 'Telefone ou email é obrigatório';
          } else if (data.phone) {
            // Validação de número de telefone angolano: +244 9XXXXXXXX
            // Formatos válidos: +244 9XXXXXXXX, 00244 9XXXXXXXX, 9XXXXXXXX
            const phoneRegex = /^(\+244|00244)?[9][0-9]{8}$/;
            if (!phoneRegex.test(data.phone.replace(/\s/g, ''))) {
              newErrors.phone = 'Número de telefone angolano inválido';
            }
          }
          break;

        case 'password':
          if (!data.password) newErrors.password = 'Senha é obrigatória';
          else if (data.password.length < 6) newErrors.password = 'Senha deve ter pelo menos 6 caracteres';
          break;

        case 'confirmPassword':
          if (!data.confirmPassword) newErrors.confirmPassword = 'Confirme sua senha';
          else if (data.confirmPassword !== data.password) newErrors.confirmPassword = 'Senhas não coincidem';
          break;

        case 'birthDate':
          if (data.birthDate) {
            const date = new Date(data.birthDate);
            const today = new Date();

            // Calcular idade considerando mês e dia
            let age = today.getFullYear() - date.getFullYear();
            const monthDiff = today.getMonth() - date.getMonth();

            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < date.getDate())) {
              age--;
            }

            if (isNaN(date.getTime())) newErrors.birthDate = 'Data inválida';
            else if (age < 18) newErrors.birthDate = 'Deve ser maior de 18 anos';
          }
          break;

        case 'gender':
          // Gênero não é obrigatório
          break;

        case 'nif':
          if (data.nif) {
            // Validar NIF com base no tipo de entidade
            if (data.entityType === 'empresa') {
              // NIF empresarial: 5XXXXXXXX (9 dígitos começando com 5)
              if (!/^5[0-9]{8}$/.test(data.nif.replace(/\s/g, ''))) {
                newErrors.nif = 'NIF empresarial inválido (deve começar com 5 e ter 9 dígitos)';
              }
            } else {
              // BI angolano: 000000000XX000 (14 caracteres)
              // Onde XX é uma das províncias: LA, BB, BG, BI, CC, CM, CU, CN, HA, HL, KA, LE, LN, MA, MO, NA, UE, ZB
              const provinceCodes = ['LA', 'BB', 'BG', 'BI', 'CC', 'CM', 'CU', 'CN', 'HA', 'HL', 'KA', 'LE', 'LN', 'MA', 'MO', 'NA', 'UE', 'ZB'];
              const biRegex = new RegExp(`^[0-9]{9}(${provinceCodes.join('|')})[0-9]{3}$`);

              if (!biRegex.test(data.nif.replace(/\s/g, ''))) {
                newErrors.nif = 'Número de BI inválido (formato: 000000000XX000, onde XX é o código da província)';
              }
            }
          }
          break;

        case 'address':
          if (data.address && data.address.length < 10) {
            newErrors.address = 'Endereço muito curto';
          }
          break;

        case 'accountType':
          if (!data.accountType) newErrors.accountType = 'Tipo de conta é obrigatório';
          break;

        case 'entityType':
          if (!data.entityType) newErrors.entityType = 'Tipo de entidade é obrigatório';
          break;

        case 'companyName':
          if (data.companyName && data.companyName.length < 3) {
            newErrors.companyName = 'Nome da empresa deve ter pelo menos 3 caracteres';
          }
          break;

        case 'companyAddress':
          if (data.companyAddress && data.companyAddress.length < 10) {
            newErrors.companyAddress = 'Morada da empresa deve ter pelo menos 10 caracteres';
          }
          break;

        case 'profileType':
          // Tipo de perfil não é mais obrigatório
          break;

        default:
          break;
      }
    });

    return newErrors;
  };

  // Manipulador de mudança de campo
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    setTouched(prev => ({ ...prev, [name]: true }));
  };

  // Validar campos quando são modificados
  useEffect(() => {
    if (Object.keys(touched).length > 0) {
      const touchedFields = Object.keys(touched).filter(key => touched[key]);
      const newErrors = validateForm(formData, touchedFields);
      setErrors(newErrors);
    }
  }, [formData, touched]);

  // Verificar se os campos obrigatórios do passo 1 estão preenchidos
  const isStep1Valid = () => {
    // Verificar se os campos obrigatórios estão preenchidos
    const hasName = formData.name && formData.name.length >= 3;
    const hasEmailOrPhone = formData.email || formData.phone;
    const hasPassword = formData.password && formData.password.length >= 6;
    const hasConfirmPassword = formData.confirmPassword === formData.password;

    // Validar email se estiver preenchido
    const isEmailValid = !formData.email || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email);

    // Validar telefone se estiver preenchido
    const phoneRegex = /^(\+244|00244)?[9][0-9]{8}$/;
    const isPhoneValid = !formData.phone || phoneRegex.test(formData.phone.replace(/\s/g, ''));

    return hasName && hasEmailOrPhone && hasPassword && hasConfirmPassword && isEmailValid && isPhoneValid;
  };

  // Verificar se os campos obrigatórios do passo 2 estão preenchidos
  const isStep2Valid = () => {
    const hasAccountType = formData.accountType;
    const hasEntityType = formData.entityType;

    return hasAccountType && hasEntityType;
  };

  // Verificar se algum campo do passo 3 está preenchido
  const hasStep3Data = () => {
    return !!formData.birthDate || !!formData.gender || !!formData.nif || !!formData.address || !!formData.profileType;
  };

  // Manipulador para pular para o próximo passo
  const handleSkipToNext = () => {
    let requiredFields = [];

    if (currentStep === 1) {
      // Validar campos obrigatórios do passo 1
      requiredFields = ['name', 'email', 'phone', 'password', 'confirmPassword'];
    } else if (currentStep === 2) {
      // Validar campos obrigatórios do passo 2
      requiredFields = ['accountType', 'entityType'];

      // Os campos de empresa não são mais obrigatórios
    }

    // Marcar campos obrigatórios como tocados
    const stepTouched = {};
    requiredFields.forEach(field => {
      stepTouched[field] = true;
    });
    setTouched(prev => ({ ...prev, ...stepTouched }));

    // Validar campos
    const newErrors = validateForm(formData, requiredFields);
    setErrors(newErrors);

    // Se não houver erros, avançar para o próximo passo ou finalizar
    if (Object.keys(newErrors).length === 0) {
      if (currentStep === 3) {
        // Se estiver no passo 3, finalizar o registro
        console.log('Registro completo:', formData);
        // Aqui você enviaria os dados para o backend
      } else {
        // Avançar para o próximo passo
        setCurrentStep(currentStep + 1);
      }
    }
  };

  // Manipulador de envio do formulário
  const handleSubmit = (e) => {
    e.preventDefault();

    if (isLogin) {
      // Para login, validar email/telefone e senha
      const loginTouched = { email: true, phone: true, password: true };
      setTouched(loginTouched);

      const loginFields = ['email', 'phone', 'password'];
      const newErrors = validateForm(formData, loginFields);
      setErrors(newErrors);

      if (Object.keys(newErrors).length === 0) {
        console.log('Login com:', formData.email || formData.phone, formData.password);
      }
    } else {
      // Para registro, validar campos obrigatórios do passo atual
      let requiredFields = [];

      if (currentStep === 1) {
        // Etapa 1: Nome, email/telefone, senha e confirmação de senha são obrigatórios
        requiredFields = ['name', 'email', 'phone', 'password', 'confirmPassword'];
      } else if (currentStep === 2) {
        // Etapa 2: Tipo de conta e tipo de entidade são obrigatórios
        requiredFields = ['accountType', 'entityType'];

        // Os campos de empresa não são mais obrigatórios
      }

      // Marcar apenas os campos obrigatórios como tocados
      const stepTouched = {};
      requiredFields.forEach(field => {
        stepTouched[field] = true;
      });
      setTouched(prev => ({ ...prev, ...stepTouched }));

      const newErrors = validateForm(formData, requiredFields);
      setErrors(newErrors);

      if (Object.keys(newErrors).length === 0) {
        // Se estiver no último passo, enviar o formulário
        if (currentStep === 3) {
          console.log('Registro completo:', formData);
          // Aqui você enviaria os dados para o backend
        } else {
          // Avançar para o próximo passo
          setCurrentStep(currentStep + 1);
        }
      }
    }
  };

  // Voltar para o passo anterior
  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Renderizar o campo de formulário com validação
  const renderField = (name, label, type, icon, placeholder, isRequired = false) => {
    const hasError = errors[name];
    return (
      <div className="relative">
        <label htmlFor={name} className="sr-only">
          {label} {isRequired && '*'}
        </label>
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          {icon}
        </div>
        <input
          id={name}
          name={name}
          type={type}
          value={formData[name]}
          onChange={handleChange}
          className={`appearance-none rounded-lg relative block w-full pl-10 px-3 py-2 border ${hasError ? 'border-red-500' : isDarkMode ? 'border-gray-600' : 'border-gray-300'} ${isDarkMode ? 'bg-gray-800 text-white placeholder-gray-400' : 'bg-white text-gray-900 placeholder-gray-500'} focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 focus:z-10 sm:text-sm`}
          placeholder={`${placeholder}${isRequired ? ' *' : ''}`}
        />
        {hasError && (
          <div className="text-red-500 text-xs mt-1 flex items-center">
            <AlertCircle className="h-3 w-3 mr-1" />
            {errors[name]}
          </div>
        )}
      </div>
    );
  };

  // Renderizar select com validação
  const renderSelect = (name, label, icon, options, isRequired = false) => {
    const hasError = errors[name];
    return (
      <div className="relative">
        <label htmlFor={name} className="sr-only">
          {label} {isRequired && '*'}
        </label>
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          {icon}
        </div>
        <select
          id={name}
          name={name}
          value={formData[name]}
          onChange={handleChange}
          className={`appearance-none rounded-lg relative block w-full pl-10 px-3 py-2 border ${hasError ? 'border-red-500' : isDarkMode ? 'border-gray-600' : 'border-gray-300'} ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'} focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 focus:z-10 sm:text-sm`}
        >
          <option value="">{`${label}${isRequired ? ' *' : ''}`}</option>
          {options.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {hasError && (
          <div className="text-red-500 text-xs mt-1 flex items-center">
            <AlertCircle className="h-3 w-3 mr-1" />
            {errors[name]}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`min-h-screen flex items-start justify-center ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'} pt-20 px-4 sm:px-6 lg:px-8`}>
      <div className="max-w-md w-full space-y-6">
        <div className="text-center">
          <h2 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{isLogin ? 'Login' : 'Criar Conta'}</h2>
          <p className={`mt-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            {isLogin
              ? 'Bem-vindo de volta! Entre na sua conta'
              : 'Crie sua conta para começar a alugar equipamentos'}
          </p>
          {!isLogin && (
            <div className="flex justify-center mt-4">
              <div className="flex items-center">
                {[1, 2, 3].map((step) => (
                  <React.Fragment key={step}>
                    <div
                      className={`rounded-full h-8 w-8 flex items-center justify-center ${currentStep >= step ? 'bg-cyan-500 text-white' : 'bg-gray-200 text-gray-600'}`}
                    >
                      {step}
                    </div>
                    {step < 3 && (
                      <div className={`h-1 w-10 ${currentStep > step ? 'bg-cyan-500' : isDarkMode ? 'bg-gray-700' : 'bg-gray-200'}`}></div>
                    )}
                  </React.Fragment>
                ))}
              </div>
            </div>
          )}
        </div>

        <form onSubmit={handleSubmit} className="mt-6 space-y-4">
          <div className="rounded-md shadow-sm space-y-4">
            {isLogin ? (
              // Formulário de login
              <>
                {renderField('email', 'Email', 'email', <Mail className="h-5 w-5 text-gray-400" />, 'Email', true)}
                {renderField('password', 'Senha', 'password', <Lock className="h-5 w-5 text-gray-400" />, 'Senha', true)}
              </>
            ) : (
              // Formulário de registro em etapas
              <>
                {currentStep === 1 && (
                  // Etapa 1: Informações básicas
                  <>
                    {renderField('name', 'Nome completo', 'text', <User className="h-5 w-5 text-gray-400" />, 'Nome completo', true)}
                    {renderField('email', 'Email', 'email', <Mail className="h-5 w-5 text-gray-400" />, 'Email')}
                    {renderField('phone', 'Telefone', 'tel', <Phone className="h-5 w-5 text-gray-400" />, 'Telefone (ex: +244 923456789)')}
                    {renderField('password', 'Senha', 'password', <Lock className="h-5 w-5 text-gray-400" />, 'Senha', true)}
                    {renderField('confirmPassword', 'Confirmar Senha', 'password', <Lock className="h-5 w-5 text-gray-400" />, 'Confirmar Senha', true)}
                  </>
                )}

                {currentStep === 2 && (
                  // Etapa 2: Tipo de conta e entidade
                  <>
                    {/* Tipo de Conta */}
                    {renderSelect('accountType', 'Tipo de Conta', <UserCheck className="h-5 w-5 text-gray-400" />, [
                      { value: 'locador', label: 'Locador (Alugar meus equipamentos)' },
                      { value: 'locatario', label: 'Locatário (Alugar equipamentos)' }
                    ], true)}

                    {/* Tipo de Entidade */}
                    {renderSelect('entityType', 'Tipo de Entidade', <Store className="h-5 w-5 text-gray-400" />, [
                      { value: 'individual', label: 'Individual' },
                      { value: 'empresa', label: 'Empresa' }
                    ], true)}


                  </>
                )}

                {currentStep === 3 && (
                  // Etapa 3: Informações adicionais (opcionais)
                  <>
                    <div className="mb-4 p-3 bg-blue-50 text-blue-800 rounded-md border border-blue-200">
                      <p className="text-sm">Todos os campos abaixo são opcionais. Você pode preencher agora ou mais tarde no seu perfil.</p>
                      <div className="mt-2 flex justify-end">
                        <Button
                          type="button"
                          variant="ghost"
                          onClick={() => {
                            // Finalizar o registro sem preencher os campos opcionais
                            console.log('Registro completo (pulando campos opcionais):', formData);
                            // Aqui você enviaria os dados para o backend
                          }}
                          className="flex items-center text-xs text-blue-600 hover:text-blue-500"
                        >
                          <span>Pular e concluir</span>
                          <SkipForward className="w-3 h-3 ml-1" />
                        </Button>
                      </div>
                    </div>

                    {renderField('birthDate', 'Data de Nascimento', 'date', <Calendar className="h-5 w-5 text-gray-400" />, 'Data de Nascimento')}
                    {renderSelect('gender', 'Gênero', <Users className="h-5 w-5 text-gray-400" />, [
                      { value: 'masculino', label: 'Masculino' },
                      { value: 'feminino', label: 'Feminino' },
                      { value: 'outro', label: 'Outro' },
                      { value: 'prefiro_nao_dizer', label: 'Prefiro não dizer' }
                    ])}
                    {renderField('nif', formData.entityType === 'empresa' ? 'NIF Empresarial' : 'Número do BI', 'text', <CreditCard className="h-5 w-5 text-gray-400" />,
                      formData.entityType === 'empresa'
                        ? 'NIF Empresarial (ex: 500000000)'
                        : 'Número do BI (ex: 000000000LA000)')
                    }
                    {renderField('address', 'Endereço', 'text', <MapPin className="h-5 w-5 text-gray-400" />, 'Endereço completo')}

                    {/* Campos específicos para empresa */}
                    {formData.entityType === 'empresa' && (
                      <>
                        <div className="mt-4 p-3 bg-yellow-50 text-yellow-800 rounded-md border border-yellow-200">
                          <p className="text-sm">Dados da empresa (opcionais):</p>
                        </div>
                        {renderField('companyName', 'Nome da Empresa', 'text', <Building className="h-5 w-5 text-gray-400" />, 'Nome da Empresa')}
                        {renderField('companyAddress', 'Morada da Empresa', 'text', <MapPin className="h-5 w-5 text-gray-400" />, 'Morada da Empresa')}
                      </>
                    )}
                    {renderSelect('profileType', 'Tipo de Perfil', <Building className="h-5 w-5 text-gray-400" />, [
                      { value: 'individual', label: 'Individual' },
                      { value: 'empresa', label: 'Empresa' },
                      { value: 'profissional', label: 'Profissional' }
                    ])}
                  </>
                )}
              </>
            )}
          </div>

          {isLogin && (
            <div className="flex items-center justify-end">
              <button type="button" className={`text-sm ${isDarkMode ? 'text-cyan-400 hover:text-cyan-300' : 'text-cyan-600 hover:text-cyan-500'}`}>
                Esqueceu a senha?
              </button>
            </div>
          )}

          <div className={`flex ${!isLogin && currentStep > 1 ? 'justify-between' : 'justify-center'}`}>
            {!isLogin && currentStep > 1 && (
              <Button
                type="button"
                variant="outline"
                onClick={handleBack}
                className="flex items-center space-x-2"
              >
                <span>Voltar</span>
              </Button>
            )}
            <Button
              type={isLogin ? "submit" : "button"}
              onClick={isLogin ? undefined : handleSkipToNext}
              className="flex justify-center items-center space-x-2 w-full md:w-auto"
            >
              <span>
                {isLogin
                  ? 'Entrar'
                  : currentStep < 3
                    ? 'Continuar'
                    : hasStep3Data() ? 'Concluir' : 'Pular e Concluir'}
              </span>
              <ArrowRight className="w-4 h-4" />
            </Button>
          </div>

          {/* Opções de login/registro com Google e Apple */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className={`w-full border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-300'}`}></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className={`px-2 ${isDarkMode ? 'bg-gray-900 text-gray-400' : 'bg-gray-50 text-gray-500'}`}>{isLogin ? 'Ou entre com' : 'Ou registre-se com'}</span>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-3">
            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={() => {/* Implement Google login/register */}}
            >
              <img
                src="https://www.google.com/favicon.ico"
                alt="Google"
                className="w-5 h-5 mr-2"
              />
              Google
            </Button>
            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={() => {/* Implement Apple login/register */}}
            >
              <img
                src="https://www.apple.com/favicon.ico"
                alt="Apple"
                className="w-5 h-5 mr-2"
              />
              Apple
            </Button>
          </div>

          {isLogin && (
            <>
              {/* Esqueceu a senha aparece apenas no login */}
            </>
          )}

          <div className="text-center">
            <button
              type="button"
              onClick={() => {
                setIsLogin(!isLogin);
                setCurrentStep(1);
                setFormData({
                  name: '',
                  email: '',
                  phone: '',
                  password: '',
                  confirmPassword: '',
                  birthDate: '',
                  gender: '',
                  nif: '',
                  address: '',
                  accountType: '',
                  entityType: '',
                  profileType: '',
                  companyName: '',
                  companyAddress: ''
                });
                setErrors({});
                setTouched({});
              }}
              className={`text-sm ${isDarkMode ? 'text-cyan-400 hover:text-cyan-300' : 'text-cyan-600 hover:text-cyan-500'}`}
            >
              {isLogin
                ? 'Não tem uma conta? Cadastre-se'
                : 'Já tem uma conta? Faça login'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}