import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Mail, Lock, User, ArrowRight, Phone, Calendar,
  MapPin, CreditCard, Users, Building, AlertCircle,
  SkipForward, UserCheck, Store, Upload, FileText, X
} from 'lucide-react';
import { Button } from '../components/ui/button';
import { useAuth } from '../contexts/AuthContext';

interface AuthProps {
  isDarkMode?: boolean;
}

export default function Auth({ isDarkMode = false }: AuthProps) {
  const navigate = useNavigate();
  const { login, register, isLoading, isAuthenticated } = useAuth();
  const [isLogin, setIsLogin] = useState(true);
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phoneNumber: '',
    password: '',
    confirmPassword: '',
    dateOfBirth: '',
    nif: '',
    userType: '', // LANDLORD ou TENANT
    isCompany: false,
    companyName: '',
    companyAddress: '',
    companyType: '',
    companyDocuments: [] as File[]
  });
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [submitError, setSubmitError] = useState('');
  const [uploadedDocuments, setUploadedDocuments] = useState<File[]>([]);
  const [biDocument, setBiDocument] = useState<File | null>(null);

  // Função de teste para login rápido
  const handleTestLogin = async () => {
    try {
      await login('<EMAIL>', '123456');
    } catch (error) {
      console.error('Erro no login de teste:', error);
      setSubmitError('Erro no login de teste: ' + error.message);
    }
  };

  // Redirecionar se já estiver autenticado
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);

  // Validação de formulário
  const validateForm = (data, fields = null) => {
    const fieldsToValidate = fields || Object.keys(data);
    const newErrors = {};

    fieldsToValidate.forEach(field => {
      // Pular campos não relevantes para o login
      if (isLogin && field !== 'email' && field !== 'phone' && field !== 'password') return;

      // Validações específicas por campo
      switch (field) {
        case 'fullName':
          if (!data.fullName) newErrors.fullName = 'Nome completo é obrigatório';
          else if (data.fullName.length < 3) newErrors.fullName = 'Nome deve ter pelo menos 3 caracteres';
          break;

        case 'email':
          if (!data.email) {
            newErrors.email = 'Email é obrigatório';
          } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
            newErrors.email = 'Email inválido';
          }
          break;

        case 'phoneNumber':
          if (!data.phoneNumber) {
            newErrors.phoneNumber = 'Telefone é obrigatório';
          } else {
            // Validação de número de telefone angolano: +244 9XXXXXXXX
            const phoneRegex = /^(\+244|00244)?[9][0-9]{8}$/;
            if (!phoneRegex.test(data.phoneNumber.replace(/\s/g, ''))) {
              newErrors.phoneNumber = 'Número de telefone angolano inválido';
            }
          }
          break;

        case 'password':
          if (!data.password) newErrors.password = 'Senha é obrigatória';
          else if (data.password.length < 6) newErrors.password = 'Senha deve ter pelo menos 6 caracteres';
          break;

        case 'confirmPassword':
          if (!data.confirmPassword) newErrors.confirmPassword = 'Confirme sua senha';
          else if (data.confirmPassword !== data.password) newErrors.confirmPassword = 'Senhas não coincidem';
          break;

        case 'dateOfBirth':
          if (data.dateOfBirth) {
            const date = new Date(data.dateOfBirth);
            const today = new Date();

            // Calcular idade considerando mês e dia
            let age = today.getFullYear() - date.getFullYear();
            const monthDiff = today.getMonth() - date.getMonth();

            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < date.getDate())) {
              age--;
            }

            if (isNaN(date.getTime())) newErrors.dateOfBirth = 'Data inválida';
            else if (age < 18) newErrors.dateOfBirth = 'Deve ser maior de 18 anos';
          }
          break;

        case 'gender':
          // Gênero não é obrigatório
          break;

        case 'nif':
          if (data.isCompany && !data.nif) {
            newErrors.nif = 'NIF é obrigatório para empresas';
          } else if (data.nif) {
            // Validar NIF com base no tipo de entidade
            if (data.isCompany) {
              // NIF empresarial: 5XXXXXXXX (9 dígitos começando com 5)
              if (!/^5[0-9]{8}$/.test(data.nif.replace(/\s/g, ''))) {
                newErrors.nif = 'NIF empresarial inválido (deve começar com 5 e ter 9 dígitos)';
              }
            } else {
              // BI angolano: 000000000XX000 (14 caracteres)
              // Onde XX é uma das províncias: LA, BB, BG, BI, CC, CM, CU, CN, HA, HL, KA, LE, LN, MA, MO, NA, UE, ZB
              const provinceCodes = ['LA', 'BB', 'BG', 'BI', 'CC', 'CM', 'CU', 'CN', 'HA', 'HL', 'KA', 'LE', 'LN', 'MA', 'MO', 'NA', 'UE', 'ZB'];
              const biRegex = new RegExp(`^[0-9]{9}(${provinceCodes.join('|')})[0-9]{3}$`);

              if (!biRegex.test(data.nif.replace(/\s/g, ''))) {
                newErrors.nif = 'Número de BI inválido (formato: 000000000XX000, onde XX é o código da província)';
              }
            }
          }
          break;

        case 'address':
          if (data.address && data.address.length < 10) {
            newErrors.address = 'Endereço muito curto';
          }
          break;

        case 'userType':
          if (!data.userType) newErrors.userType = 'Tipo de usuário é obrigatório';
          break;

        case 'companyName':
          if (data.isCompany && !data.companyName) {
            newErrors.companyName = 'Nome da empresa é obrigatório';
          } else if (data.companyName && data.companyName.length < 3) {
            newErrors.companyName = 'Nome da empresa deve ter pelo menos 3 caracteres';
          }
          break;

        case 'companyAddress':
          if (data.companyAddress && data.companyAddress.length < 10) {
            newErrors.companyAddress = 'Morada da empresa deve ter pelo menos 10 caracteres';
          }
          break;

        case 'companyType':
          if (data.isCompany && !data.companyType) {
            newErrors.companyType = 'Tipo de empresa é obrigatório';
          }
          break;

        case 'companyDocuments':
          if (data.isCompany && (!data.companyDocuments || data.companyDocuments.length === 0)) {
            newErrors.companyDocuments = 'Pelo menos um documento da empresa é obrigatório';
          }
          break;

        default:
          break;
      }
    });

    return newErrors;
  };

  // Manipulador de mudança de campo
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;
    setFormData(prev => ({ ...prev, [name]: newValue }));
    setTouched(prev => ({ ...prev, [name]: true }));
  };

  // Manipulador de upload de documentos
  const handleDocumentUpload = (e) => {
    const files = Array.from(e.target.files || []);
    const validFiles = files.filter(file => {
      const isValidType = file.type === 'application/pdf' || file.type.startsWith('image/');
      const isValidSize = file.size <= 5 * 1024 * 1024; // 5MB
      return isValidType && isValidSize;
    });

    if (validFiles.length !== files.length) {
      setSubmitError('Alguns arquivos foram rejeitados. Apenas PDF e imagens até 5MB são aceitos.');
    }

    setFormData(prev => ({
      ...prev,
      companyDocuments: [...prev.companyDocuments, ...validFiles]
    }));
    setTouched(prev => ({ ...prev, companyDocuments: true }));
  };

  // Remover documento
  const removeDocument = (index) => {
    setFormData(prev => ({
      ...prev,
      companyDocuments: prev.companyDocuments.filter((_, i) => i !== index)
    }));
  };

  const handleBiDocumentUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB
        setErrors(prev => ({ ...prev, biDocument: 'Arquivo deve ter menos de 5MB' }));
        return;
      }

      if (!file.type.startsWith('image/') && file.type !== 'application/pdf') {
        setErrors(prev => ({ ...prev, biDocument: 'Apenas imagens e PDFs são aceitos' }));
        return;
      }

      setBiDocument(file);
      setErrors(prev => ({ ...prev, biDocument: '' }));
    }
  };

  // Validar campos quando são modificados
  useEffect(() => {
    if (Object.keys(touched).length > 0) {
      const touchedFields = Object.keys(touched).filter(key => touched[key]);
      const newErrors = validateForm(formData, touchedFields);
      setErrors(newErrors);
    }
  }, [formData, touched]);

  // Verificar se os campos obrigatórios do passo 1 estão preenchidos
  const isStep1Valid = () => {
    const hasName = formData.fullName && formData.fullName.length >= 3;
    const hasEmail = formData.email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email);
    const hasPhone = formData.phoneNumber && /^(\+244|00244)?[9][0-9]{8}$/.test(formData.phoneNumber.replace(/\s/g, ''));
    const hasPassword = formData.password && formData.password.length >= 6;
    const hasConfirmPassword = formData.confirmPassword === formData.password;

    return hasName && hasEmail && hasPhone && hasPassword && hasConfirmPassword;
  };

  // Verificar se os campos obrigatórios do passo 2 estão preenchidos
  const isStep2Valid = () => {
    const hasUserType = formData.userType;
    const hasCompanyData = !formData.isCompany || (
      formData.companyName &&
      formData.companyType &&
      formData.nif &&
      formData.companyDocuments.length > 0
    );

    return hasUserType && hasCompanyData;
  };

  // Verificar se algum campo do passo 3 está preenchido
  const hasStep3Data = () => {
    return !!formData.dateOfBirth || !!formData.gender || !!formData.nif || !!biDocument;
  };

  // Manipulador para pular para o próximo passo
  const handleSkipToNext = () => {
    let requiredFields = [];

    if (currentStep === 1) {
      // Validar campos obrigatórios do passo 1
      requiredFields = ['name', 'email', 'phone', 'password', 'confirmPassword'];
    } else if (currentStep === 2) {
      // Validar campos obrigatórios do passo 2
      requiredFields = ['accountType', 'entityType'];

      // Os campos de empresa não são mais obrigatórios
    }

    // Marcar campos obrigatórios como tocados
    const stepTouched = {};
    requiredFields.forEach(field => {
      stepTouched[field] = true;
    });
    setTouched(prev => ({ ...prev, ...stepTouched }));

    // Validar campos
    const newErrors = validateForm(formData, requiredFields);
    setErrors(newErrors);

    // Se não houver erros, avançar para o próximo passo ou finalizar
    if (Object.keys(newErrors).length === 0) {
      if (currentStep === 3) {
        // Se estiver no passo 3, finalizar o registro
        console.log('Registro completo:', formData);
        // Aqui você enviaria os dados para o backend
      } else {
        // Avançar para o próximo passo
        setCurrentStep(currentStep + 1);
      }
    }
  };

  // Manipulador de envio do formulário
  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitError('');

    if (isLogin) {
      // Para login, validar email e senha
      const loginTouched = { email: true, password: true };
      setTouched(loginTouched);

      const loginFields = ['email', 'password'];
      const newErrors = validateForm(formData, loginFields);
      setErrors(newErrors);

      if (Object.keys(newErrors).length === 0) {
        try {
          await login(formData.email, formData.password);
          // Redirecionamento será feito pelo useEffect
        } catch (error) {
          setSubmitError(error.message || 'Erro no login');
        }
      }
    } else {
      // Para registro, validar campos obrigatórios do passo atual
      let requiredFields = [];

      if (currentStep === 1) {
        // Etapa 1: Dados básicos obrigatórios
        requiredFields = ['fullName', 'email', 'phoneNumber', 'password', 'confirmPassword'];
      } else if (currentStep === 2) {
        // Etapa 2: Tipo de usuário obrigatório, dados da empresa se for empresa
        requiredFields = ['userType'];
        if (formData.isCompany) {
          requiredFields.push('companyName', 'companyType', 'nif', 'companyDocuments');
        }
      } else if (currentStep === 3) {
        // Etapa 3: Data de nascimento obrigatória para empresas
        if (formData.isCompany) {
          requiredFields = ['dateOfBirth'];
        }
      }

      // Marcar apenas os campos obrigatórios como tocados
      const stepTouched = {};
      requiredFields.forEach(field => {
        stepTouched[field] = true;
      });
      setTouched(prev => ({ ...prev, ...stepTouched }));

      const newErrors = validateForm(formData, requiredFields);
      setErrors(newErrors);

      if (Object.keys(newErrors).length === 0) {
        // Se estiver no último passo, enviar o formulário
        if (currentStep === 3) {
          try {
            // Preparar dados para envio
            const registerData = {
              fullName: formData.fullName,
              email: formData.email,
              password: formData.password,
              phoneNumber: formData.phoneNumber,
              dateOfBirth: formData.dateOfBirth || new Date().toISOString().split('T')[0],
              userType: formData.userType,
              isCompany: formData.isCompany,
              nif: formData.nif || undefined,
              companyName: formData.companyName || undefined,
              companyAddress: formData.companyAddress || undefined,
              companyType: formData.companyType || undefined,
            };

            // Upload do BI se houver
            if (biDocument) {
              const formDataUpload = new FormData();
              formDataUpload.append('file', biDocument);

              try {
                const uploadResponse = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:3000'}/upload/document`, {
                  method: 'POST',
                  body: formDataUpload,
                });

                if (uploadResponse.ok) {
                  const uploadResult = await uploadResponse.json();
                  registerData.biDocument = uploadResult.url;
                }
              } catch (uploadError) {
                console.error('Erro no upload do BI:', uploadError);
              }
            }

            // Se for empresa, fazer upload dos documentos primeiro
            if (formData.isCompany && formData.companyDocuments.length > 0) {
              const documentUrls = [];
              for (const file of formData.companyDocuments) {
                const formDataUpload = new FormData();
                formDataUpload.append('file', file);

                try {
                  const uploadResponse = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:3000'}/upload/document`, {
                    method: 'POST',
                    body: formDataUpload,
                  });

                  if (uploadResponse.ok) {
                    const uploadResult = await uploadResponse.json();
                    documentUrls.push(uploadResult.url);
                  }
                } catch (uploadError) {
                  console.error('Erro no upload do documento:', uploadError);
                }
              }
              registerData.companyDocuments = documentUrls;
            }

            await register(registerData);
            // Redirecionamento será feito pelo useEffect
          } catch (error) {
            setSubmitError(error.message || 'Erro no registro');
          }
        } else {
          // Avançar para o próximo passo
          setCurrentStep(currentStep + 1);
        }
      }
    }
  };

  // Voltar para o passo anterior
  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Renderizar o campo de formulário com validação
  const renderField = (name, label, type, icon, placeholder, isRequired = false) => {
    const hasError = errors[name];
    return (
      <div className="relative">
        <label htmlFor={name} className="sr-only">
          {label} {isRequired && '*'}
        </label>
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          {icon}
        </div>
        <input
          id={name}
          name={name}
          type={type}
          value={formData[name]}
          onChange={handleChange}
          className={`appearance-none rounded-lg relative block w-full pl-10 px-3 py-2 border ${hasError ? 'border-red-500' : isDarkMode ? 'border-gray-600' : 'border-gray-300'} ${isDarkMode ? 'bg-gray-800 text-white placeholder-gray-400' : 'bg-white text-gray-900 placeholder-gray-500'} focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 focus:z-10 sm:text-sm`}
          placeholder={`${placeholder}${isRequired ? ' *' : ''}`}
        />
        {hasError && (
          <div className="text-red-500 text-xs mt-1 flex items-center">
            <AlertCircle className="h-3 w-3 mr-1" />
            {errors[name]}
          </div>
        )}
      </div>
    );
  };

  // Renderizar select com validação
  const renderSelect = (name, label, icon, options, isRequired = false) => {
    const hasError = errors[name];
    return (
      <div className="relative">
        <label htmlFor={name} className="sr-only">
          {label} {isRequired && '*'}
        </label>
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          {icon}
        </div>
        <select
          id={name}
          name={name}
          value={formData[name]}
          onChange={handleChange}
          className={`appearance-none rounded-lg relative block w-full pl-10 px-3 py-2 border ${hasError ? 'border-red-500' : isDarkMode ? 'border-gray-600' : 'border-gray-300'} ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'} focus:outline-none focus:ring-cyan-500 focus:border-cyan-500 focus:z-10 sm:text-sm`}
        >
          <option value="">{`${label}${isRequired ? ' *' : ''}`}</option>
          {options.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {hasError && (
          <div className="text-red-500 text-xs mt-1 flex items-center">
            <AlertCircle className="h-3 w-3 mr-1" />
            {errors[name]}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`min-h-screen flex items-start justify-center ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'} pt-20 px-4 sm:px-6 lg:px-8`}>
      <div className="max-w-md w-full space-y-6">
        <div className="text-center">
          <h2 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{isLogin ? 'Login' : 'Criar Conta'}</h2>
          <p className={`mt-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            {isLogin
              ? 'Bem-vindo de volta! Entre na sua conta'
              : 'Crie sua conta para começar a alugar equipamentos'}
          </p>
          {!isLogin && (
            <div className="flex justify-center mt-4">
              <div className="flex items-center">
                {[1, 2, 3].map((step) => (
                  <React.Fragment key={step}>
                    <div
                      className={`rounded-full h-8 w-8 flex items-center justify-center ${currentStep >= step ? 'bg-cyan-500 text-white' : 'bg-gray-200 text-gray-600'}`}
                    >
                      {step}
                    </div>
                    {step < 3 && (
                      <div className={`h-1 w-10 ${currentStep > step ? 'bg-cyan-500' : isDarkMode ? 'bg-gray-700' : 'bg-gray-200'}`}></div>
                    )}
                  </React.Fragment>
                ))}
              </div>
            </div>
          )}
        </div>

        {submitError && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <div className="flex items-center">
              <AlertCircle className="h-4 w-4 mr-2" />
              <span className="text-sm">{submitError}</span>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="mt-6 space-y-4">
          <div className="rounded-md shadow-sm space-y-4">
            {isLogin ? (
              // Formulário de login
              <>
                {renderField('email', 'Email', 'email', <Mail className="h-5 w-5 text-gray-400" />, 'Email', true)}
                {renderField('password', 'Senha', 'password', <Lock className="h-5 w-5 text-gray-400" />, 'Senha', true)}
              </>
            ) : (
              // Formulário de registro em etapas
              <>
                {currentStep === 1 && (
                  // Etapa 1: Informações básicas
                  <>
                    {renderField('fullName', 'Nome completo', 'text', <User className="h-5 w-5 text-gray-400" />, 'Nome completo', true)}
                    {renderField('email', 'Email', 'email', <Mail className="h-5 w-5 text-gray-400" />, 'Email', true)}
                    {renderField('phoneNumber', 'Telefone', 'tel', <Phone className="h-5 w-5 text-gray-400" />, 'Telefone (ex: +244 923456789)', true)}
                    {renderField('password', 'Senha', 'password', <Lock className="h-5 w-5 text-gray-400" />, 'Senha', true)}
                    {renderField('confirmPassword', 'Confirmar Senha', 'password', <Lock className="h-5 w-5 text-gray-400" />, 'Confirmar Senha', true)}
                  </>
                )}

                {currentStep === 2 && (
                  // Etapa 2: Tipo de usuário e dados da empresa
                  <>
                    {/* Tipo de Usuário */}
                    {renderSelect('userType', 'Tipo de Usuário', <UserCheck className="h-5 w-5 text-gray-400" />, [
                      { value: 'LANDLORD', label: 'Locador (Alugar meus equipamentos)' },
                      { value: 'TENANT', label: 'Locatário (Alugar equipamentos)' }
                    ], true)}

                    {/* Checkbox para empresa */}
                    <div className="flex items-center">
                      <input
                        id="isCompany"
                        name="isCompany"
                        type="checkbox"
                        checked={formData.isCompany}
                        onChange={handleChange}
                        className="h-4 w-4 text-cyan-600 focus:ring-cyan-500 border-gray-300 rounded"
                      />
                      <label htmlFor="isCompany" className={`ml-2 block text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-900'}`}>
                        Sou uma empresa
                      </label>
                    </div>

                    {/* Campos específicos para empresa */}
                    {formData.isCompany && (
                      <>
                        <div className="mt-4 p-3 bg-yellow-50 text-yellow-800 rounded-md border border-yellow-200">
                          <p className="text-sm font-medium">Dados da empresa (obrigatórios):</p>
                        </div>

                        {renderField('companyName', 'Nome da Empresa', 'text', <Building className="h-5 w-5 text-gray-400" />, 'Nome da Empresa', true)}

                        {renderSelect('companyType', 'Tipo de Empresa', <Store className="h-5 w-5 text-gray-400" />, [
                          { value: 'LDA', label: 'Limitada (Lda)' },
                          { value: 'SA', label: 'Sociedade Anónima (SA)' },
                          { value: 'UNIPESSOAL', label: 'Unipessoal' },
                          { value: 'COOPERATIVA', label: 'Cooperativa' },
                          { value: 'OUTRO', label: 'Outro' }
                        ], true)}

                        {renderField('nif', 'NIF Empresarial', 'text', <CreditCard className="h-5 w-5 text-gray-400" />, 'NIF Empresarial (ex: *********)', true)}

                        {renderField('companyAddress', 'Morada da Empresa', 'text', <MapPin className="h-5 w-5 text-gray-400" />, 'Morada da Empresa')}

                        {/* Upload de documentos */}
                        <div className="space-y-2">
                          <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                            Documentos da Empresa *
                          </label>
                          <div className="flex items-center justify-center w-full">
                            <label htmlFor="companyDocuments" className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer ${isDarkMode ? 'border-gray-600 bg-gray-800 hover:bg-gray-700' : 'border-gray-300 bg-gray-50 hover:bg-gray-100'}`}>
                              <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                <Upload className={`w-8 h-8 mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                                <p className={`mb-2 text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                                  <span className="font-semibold">Clique para enviar</span> documentos
                                </p>
                                <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                                  PDF ou imagens (máx. 5MB cada)
                                </p>
                              </div>
                              <input
                                id="companyDocuments"
                                type="file"
                                multiple
                                accept=".pdf,image/*"
                                onChange={handleDocumentUpload}
                                className="hidden"
                              />
                            </label>
                          </div>

                          {/* Lista de documentos enviados */}
                          {formData.companyDocuments.length > 0 && (
                            <div className="space-y-2">
                              <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                                Documentos enviados:
                              </p>
                              {formData.companyDocuments.map((file, index) => (
                                <div key={index} className={`flex items-center justify-between p-2 rounded border ${isDarkMode ? 'border-gray-600 bg-gray-800' : 'border-gray-300 bg-gray-50'}`}>
                                  <div className="flex items-center">
                                    <FileText className="w-4 h-4 mr-2 text-gray-500" />
                                    <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                                      {file.name}
                                    </span>
                                  </div>
                                  <button
                                    type="button"
                                    onClick={() => removeDocument(index)}
                                    className="text-red-500 hover:text-red-700"
                                  >
                                    <X className="w-4 h-4" />
                                  </button>
                                </div>
                              ))}
                            </div>
                          )}

                          {errors.companyDocuments && (
                            <div className="text-red-500 text-xs mt-1 flex items-center">
                              <AlertCircle className="h-3 w-3 mr-1" />
                              {errors.companyDocuments}
                            </div>
                          )}
                        </div>
                      </>
                    )}
                  </>
                )}

                {currentStep === 3 && (
                  // Etapa 3: Informações adicionais (opcionais para indivíduos)
                  <>
                    <div className="mb-4 p-3 bg-blue-50 text-blue-800 rounded-md border border-blue-200">
                      <p className="text-sm">
                        {!formData.isCompany
                          ? 'Todos os campos abaixo são opcionais. Você pode preencher agora ou mais tarde no seu perfil.'
                          : 'Complete os dados pessoais para finalizar o registro.'
                        }
                      </p>
                      {!formData.isCompany && (
                        <div className="mt-2 flex justify-end">
                          <Button
                            type="button"
                            variant="ghost"
                            onClick={handleSubmit}
                            className="flex items-center text-xs text-blue-600 hover:text-blue-500"
                          >
                            <span>Pular e concluir</span>
                            <SkipForward className="w-3 h-3 ml-1" />
                          </Button>
                        </div>
                      )}
                    </div>

                    {renderField('dateOfBirth', 'Data de Nascimento', 'date', <Calendar className="h-5 w-5 text-gray-400" />, 'Data de Nascimento', formData.isCompany)}

                    {!formData.isCompany && (
                      <>
                        {renderSelect('gender', 'Gênero', <Users className="h-5 w-5 text-gray-400" />, [
                          { value: 'masculino', label: 'Masculino' },
                          { value: 'feminino', label: 'Feminino' },
                          { value: 'outro', label: 'Outro' },
                          { value: 'prefiro_nao_dizer', label: 'Prefiro não dizer' }
                        ])}
                        {renderField('nif', 'Número do BI', 'text', <CreditCard className="h-5 w-5 text-gray-400" />, 'Número do BI (ex: 000000000LA000)')}

                        {/* Upload do BI - Opcional */}
                        <div className="mb-4">
                          <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                            Documento de Identidade (BI) - Opcional
                          </label>
                          <div className="flex items-center justify-center w-full">
                            <label htmlFor="biDocument" className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer ${isDarkMode ? 'border-gray-600 bg-gray-700 hover:bg-gray-600' : 'border-gray-300 bg-gray-50 hover:bg-gray-100'}`}>
                              <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                <Upload className={`w-8 h-8 mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                                <p className={`mb-2 text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                                  <span className="font-semibold">Clique para enviar</span> seu BI
                                </p>
                                <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                                  PDF ou imagem (máx. 5MB)
                                </p>
                              </div>
                              <input
                                id="biDocument"
                                type="file"
                                accept=".pdf,image/*"
                                onChange={handleBiDocumentUpload}
                                className="hidden"
                              />
                            </label>
                          </div>

                          {biDocument && (
                            <div className="mt-4">
                              <div className={`flex items-center justify-between p-2 rounded border ${isDarkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-300 bg-gray-50'}`}>
                                <div className="flex items-center">
                                  <FileText className="w-4 h-4 mr-2 text-gray-500" />
                                  <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                                    {biDocument.name}
                                  </span>
                                </div>
                                <button
                                  type="button"
                                  onClick={() => setBiDocument(null)}
                                  className="text-red-500 hover:text-red-700"
                                >
                                  <X className="w-4 h-4" />
                                </button>
                              </div>
                            </div>
                          )}

                          {errors.biDocument && (
                            <div className="text-red-500 text-xs mt-1 flex items-center">
                              <AlertCircle className="h-3 w-3 mr-1" />
                              {errors.biDocument}
                            </div>
                          )}
                        </div>
                      </>
                    )}
                  </>
                )}
              </>
            )}
          </div>

          {isLogin && (
            <div className="flex items-center justify-end">
              <button type="button" className={`text-sm ${isDarkMode ? 'text-cyan-400 hover:text-cyan-300' : 'text-cyan-600 hover:text-cyan-500'}`}>
                Esqueceu a senha?
              </button>
            </div>
          )}

          <div className={`flex ${!isLogin && currentStep > 1 ? 'justify-between' : 'justify-center'}`}>
            {!isLogin && currentStep > 1 && (
              <Button
                type="button"
                variant="outline"
                onClick={handleBack}
                className="flex items-center space-x-2"
              >
                <span>Voltar</span>
              </Button>
            )}
            <Button
              type={isLogin ? "submit" : "button"}
              onClick={isLogin ? undefined : handleSkipToNext}
              disabled={isLoading}
              className="flex justify-center items-center space-x-2 w-full md:w-auto"
            >
              <span>
                {isLoading
                  ? 'Processando...'
                  : isLogin
                    ? 'Entrar'
                    : currentStep < 3
                      ? 'Continuar'
                      : hasStep3Data() ? 'Concluir' : 'Pular e Concluir'}
              </span>
              {!isLoading && <ArrowRight className="w-4 h-4" />}
            </Button>
          </div>

          {/* Opções de login/registro com Google e Apple */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className={`w-full border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-300'}`}></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className={`px-2 ${isDarkMode ? 'bg-gray-900 text-gray-400' : 'bg-gray-50 text-gray-500'}`}>{isLogin ? 'Ou entre com' : 'Ou registre-se com'}</span>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-3">
            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={() => {/* Implement Google login/register */}}
            >
              <img
                src="https://www.google.com/favicon.ico"
                alt="Google"
                className="w-5 h-5 mr-2"
              />
              Google
            </Button>
            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={() => {/* Implement Apple login/register */}}
            >
              <img
                src="https://www.apple.com/favicon.ico"
                alt="Apple"
                className="w-5 h-5 mr-2"
              />
              Apple
            </Button>
          </div>

          {/* Botão de teste - apenas para desenvolvimento */}
          {isLogin && (
            <div className="mt-4">
              <Button
                type="button"
                variant="outline"
                className="w-full bg-yellow-100 border-yellow-300 text-yellow-800 hover:bg-yellow-200"
                onClick={handleTestLogin}
                disabled={isLoading}
              >
                🧪 Login de Teste (Admin)
              </Button>
            </div>
          )}

          {isLogin && (
            <>
              {/* Esqueceu a senha aparece apenas no login */}
            </>
          )}

          <div className="text-center">
            <button
              type="button"
              onClick={() => {
                setIsLogin(!isLogin);
                setCurrentStep(1);
                setFormData({
                  fullName: '',
                  email: '',
                  phoneNumber: '',
                  password: '',
                  confirmPassword: '',
                  dateOfBirth: '',
                  nif: '',
                  userType: '',
                  isCompany: false,
                  companyName: '',
                  companyAddress: '',
                  companyType: '',
                  companyDocuments: []
                });
                setErrors({});
                setTouched({});
              }}
              className={`text-sm ${isDarkMode ? 'text-cyan-400 hover:text-cyan-300' : 'text-cyan-600 hover:text-cyan-500'}`}
            >
              {isLogin
                ? 'Não tem uma conta? Cadastre-se'
                : 'Já tem uma conta? Faça login'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}