import React from 'react';
import { FileText, Users, AlertTriangle, Scale, CreditCard, Shield } from 'lucide-react';

interface TermsOfServiceProps {
  isDarkMode: boolean;
}

const TermsOfService: React.FC<TermsOfServiceProps> = ({ isDarkMode }) => {
  return (
    <div className={`min-h-screen pt-16 ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-4">
            <div className={`p-4 rounded-full ${isDarkMode ? 'bg-blue-900' : 'bg-blue-100'}`}>
              <FileText className="w-12 h-12 text-blue-600" style={{color: '#3569b0'}} />
            </div>
          </div>
          <h1 className="text-4xl font-bold mb-4">Termos de Uso</h1>
          <p className={`text-lg ${isDarkMode ? 'text-gray-400' : 'text-gray-600'} max-w-3xl mx-auto`}>
            Estes termos regem o uso da plataforma YMRentals e estabelecem os direitos e responsabilidades de todos os usuários.
          </p>
          <p className="text-sm text-gray-500 mt-2">
            Última atualização: Janeiro de 2024
          </p>
        </div>

        {/* Conteúdo */}
        <div className="max-w-4xl mx-auto">
          <div className="grid gap-8">
            
            {/* Seção 1: Aceitação dos Termos */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-8 rounded-lg shadow-md`}>
              <div className="flex items-center mb-6">
                <FileText className="w-6 h-6 text-blue-600 mr-3" style={{color: '#3569b0'}} />
                <h2 className="text-2xl font-bold">1. Aceitação dos Termos</h2>
              </div>
              <div className="space-y-4">
                <p className="text-gray-600 dark:text-gray-300">
                  Ao acessar e usar a plataforma YMRentals, você concorda em cumprir estes Termos de Uso. 
                  Se você não concorda com qualquer parte destes termos, não deve usar nossos serviços.
                </p>
                <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-yellow-900/20' : 'bg-yellow-50'} border-l-4 border-yellow-500`}>
                  <p className="text-sm">
                    <strong>Importante:</strong> Estes termos podem ser atualizados periodicamente. 
                    É sua responsabilidade revisar regularmente as mudanças.
                  </p>
                </div>
              </div>
            </div>

            {/* Seção 2: Elegibilidade */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-8 rounded-lg shadow-md`}>
              <div className="flex items-center mb-6">
                <Users className="w-6 h-6 text-blue-600 mr-3" style={{color: '#3569b0'}} />
                <h2 className="text-2xl font-bold">2. Elegibilidade</h2>
              </div>
              <div className="space-y-4">
                <p className="text-gray-600 dark:text-gray-300">
                  Para usar nossos serviços, você deve:
                </p>
                <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span>Ter pelo menos 18 anos de idade</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span>Possuir documentos válidos (BI, NIF)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span>Fornecer informações verdadeiras e atualizadas</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span>Ter capacidade legal para celebrar contratos</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Seção 3: Uso da Plataforma */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-8 rounded-lg shadow-md`}>
              <div className="flex items-center mb-6">
                <Shield className="w-6 h-6 text-blue-600 mr-3" style={{color: '#3569b0'}} />
                <h2 className="text-2xl font-bold">3. Uso Permitido</h2>
              </div>
              <div className="space-y-4">
                <p className="text-gray-600 dark:text-gray-300">
                  Você pode usar nossa plataforma para:
                </p>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-green-900/20' : 'bg-green-50'} border-l-4 border-green-500`}>
                    <h4 className="font-semibold mb-2 text-green-700 dark:text-green-300">✓ Permitido</h4>
                    <ul className="text-sm space-y-1">
                      <li>• Alugar equipamentos para uso comercial</li>
                      <li>• Listar seus equipamentos para aluguel</li>
                      <li>• Comunicar com outros usuários</li>
                      <li>• Avaliar e comentar sobre equipamentos</li>
                    </ul>
                  </div>
                  <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-red-900/20' : 'bg-red-50'} border-l-4 border-red-500`}>
                    <h4 className="font-semibold mb-2 text-red-700 dark:text-red-300">✗ Proibido</h4>
                    <ul className="text-sm space-y-1">
                      <li>• Usar para atividades ilegais</li>
                      <li>• Fornecer informações falsas</li>
                      <li>• Violar direitos de propriedade</li>
                      <li>• Spam ou comunicação abusiva</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Seção 4: Responsabilidades */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-8 rounded-lg shadow-md`}>
              <div className="flex items-center mb-6">
                <AlertTriangle className="w-6 h-6 text-blue-600 mr-3" style={{color: '#3569b0'}} />
                <h2 className="text-2xl font-bold">4. Responsabilidades</h2>
              </div>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-3">Responsabilidades do Locatário:</h3>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                      <span>Usar o equipamento conforme especificado</span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                      <span>Devolver o equipamento nas condições recebidas</span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                      <span>Pagar todas as taxas e custos acordados</span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                      <span>Reportar danos ou problemas imediatamente</span>
                    </li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold mb-3">Responsabilidades do Locador:</h3>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                      <span>Fornecer equipamentos em boas condições</span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                      <span>Manter informações precisas sobre equipamentos</span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                      <span>Cumprir prazos de entrega acordados</span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                      <span>Possuir documentação legal dos equipamentos</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Seção 5: Pagamentos */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-8 rounded-lg shadow-md`}>
              <div className="flex items-center mb-6">
                <CreditCard className="w-6 h-6 text-blue-600 mr-3" style={{color: '#3569b0'}} />
                <h2 className="text-2xl font-bold">5. Pagamentos e Taxas</h2>
              </div>
              <div className="space-y-4">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Taxas da Plataforma:</h3>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                      <li>• Taxa de serviço: 5% por transação</li>
                      <li>• Taxa de processamento: 2.5%</li>
                      <li>• Taxa de prioridade: 25.000 Kz</li>
                      <li>• Promoção de anúncios: variável</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Políticas de Pagamento:</h3>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                      <li>• Pagamento antecipado obrigatório</li>
                      <li>• Reembolsos em até 7 dias úteis</li>
                      <li>• Depósito de segurança pode ser exigido</li>
                      <li>• Multas por atraso na devolução</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Seção 6: Limitação de Responsabilidade */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-8 rounded-lg shadow-md`}>
              <div className="flex items-center mb-6">
                <Scale className="w-6 h-6 text-blue-600 mr-3" style={{color: '#3569b0'}} />
                <h2 className="text-2xl font-bold">6. Limitação de Responsabilidade</h2>
              </div>
              <div className="space-y-4">
                <p className="text-gray-600 dark:text-gray-300">
                  A YMRentals atua como intermediária entre locadores e locatários. Nossa responsabilidade é limitada a:
                </p>
                <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span>Facilitar a conexão entre usuários</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span>Processar pagamentos de forma segura</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span>Fornecer suporte ao cliente</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" style={{backgroundColor: '#3569b0'}}></span>
                    <span>Manter a plataforma funcionando</span>
                  </li>
                </ul>
                <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-red-900/20' : 'bg-red-50'} border-l-4 border-red-500`}>
                  <p className="text-sm">
                    <strong>Importante:</strong> Não somos responsáveis por danos, perdas ou disputas 
                    decorrentes do uso inadequado dos equipamentos ou descumprimento de acordos entre usuários.
                  </p>
                </div>
              </div>
            </div>

            {/* Seção 7: Resolução de Disputas */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-8 rounded-lg shadow-md`}>
              <h2 className="text-2xl font-bold mb-6">7. Resolução de Disputas</h2>
              <div className="space-y-4">
                <p className="text-gray-600 dark:text-gray-300">
                  Em caso de disputas, seguimos este processo:
                </p>
                <div className="grid md:grid-cols-3 gap-4">
                  <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'} text-center`}>
                    <div className="text-2xl font-bold text-blue-600 mb-2" style={{color: '#3569b0'}}>1</div>
                    <h4 className="font-semibold mb-2">Mediação Interna</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Nossa equipe tenta resolver a disputa amigavelmente
                    </p>
                  </div>
                  <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'} text-center`}>
                    <div className="text-2xl font-bold text-blue-600 mb-2" style={{color: '#3569b0'}}>2</div>
                    <h4 className="font-semibold mb-2">Arbitragem</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Arbitragem independente se necessário
                    </p>
                  </div>
                  <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'} text-center`}>
                    <div className="text-2xl font-bold text-blue-600 mb-2" style={{color: '#3569b0'}}>3</div>
                    <h4 className="font-semibold mb-2">Tribunal</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Recurso aos tribunais de Angola como última instância
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Seção 8: Contato */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-8 rounded-lg shadow-md`}>
              <h2 className="text-2xl font-bold mb-6">8. Contato</h2>
              <div className="space-y-4">
                <p className="text-gray-600 dark:text-gray-300">
                  Para questões sobre estes termos ou suporte:
                </p>
                <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Telefone:</strong> +244 999 999 999</p>
                  <p><strong>Endereço:</strong> Luanda, Kilamba, Bloco X, Edifício 35, 3º andar, porta 36</p>
                  <p><strong>Horário:</strong> Segunda a Sexta, 8h às 18h</p>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  );
};

export default TermsOfService;
