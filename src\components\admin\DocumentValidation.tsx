import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { Eye, Check, X, Search, FileText, User, Calendar, Building, Mail } from 'lucide-react';
import { apiService } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

interface PendingUser {
  id: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  biDocument: string;
  createdAt: string;
  userType: string;
  companyName?: string;
}

export default function DocumentValidation() {
  const { user } = useAuth();
  const [pendingUsers, setPendingUsers] = useState<PendingUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<PendingUser | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [processing, setProcessing] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('PENDING');
  const [userTypeFilter, setUserTypeFilter] = useState('');
  const [dateFilter, setDateFilter] = useState('');

  useEffect(() => {
    fetchUsers();
  }, [statusFilter, userTypeFilter, dateFilter]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      let data;

      if (statusFilter === 'PENDING') {
        data = await apiService.getPendingBiValidation();
      } else {
        // Buscar usuários com documentos validados/rejeitados
        const params = {
          biValidated: statusFilter === 'APPROVED',
          biRejected: statusFilter === 'REJECTED'
        };
        data = await apiService.getAllUsers(params);
      }

      setPendingUsers(data);
    } catch (error: any) {
      console.error('Erro ao carregar usuários:', error);
      alert('Erro ao carregar usuários');
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (userId: string) => {
    try {
      setProcessing(userId);
      await apiService.validateBi(userId, true);
      
      // Remover da lista
      setPendingUsers(prev => prev.filter(u => u.id !== userId));
      setSelectedUser(null);
      
      alert('Documento aprovado com sucesso!');
    } catch (error: any) {
      console.error('Erro ao aprovar documento:', error);
      alert('Erro ao aprovar documento');
    } finally {
      setProcessing(null);
    }
  };

  const handleReject = async (userId: string) => {
    if (!rejectionReason.trim()) {
      alert('Por favor, forneça uma razão para a rejeição');
      return;
    }

    try {
      setProcessing(userId);
      await apiService.validateBi(userId, false, rejectionReason);
      
      // Remover da lista
      setPendingUsers(prev => prev.filter(u => u.id !== userId));
      setSelectedUser(null);
      setRejectionReason('');
      
      alert('Documento rejeitado com sucesso!');
    } catch (error: any) {
      console.error('Erro ao rejeitar documento:', error);
      alert('Erro ao rejeitar documento');
    } finally {
      setProcessing(null);
    }
  };

  const getUserTypeBadge = (userType: string) => {
    const typeConfig = {
      TENANT: { label: 'Locatário', className: 'bg-blue-100 text-blue-800' },
      LANDLORD: { label: 'Locador', className: 'bg-purple-100 text-purple-800' }
    };
    
    const config = typeConfig[userType as keyof typeof typeConfig] || typeConfig.TENANT;
    return <Badge className={config.className}>{config.label}</Badge>;
  };

  const filteredUsers = pendingUsers.filter(user => {
    const matchesSearch = user.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.companyName && user.companyName.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesUserType = !userTypeFilter || user.userType === userTypeFilter;

    const matchesDate = !dateFilter ||
      new Date(user.createdAt).toDateString() === new Date(dateFilter).toDateString();

    return matchesSearch && matchesUserType && matchesDate;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Validação de Documentos BI
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Primeira linha - Busca e Status */}
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Buscar por nome, email ou empresa..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[150px]"
              >
                <option value="PENDING">🟡 Pendentes</option>
                <option value="APPROVED">✅ Aprovados</option>
                <option value="REJECTED">❌ Rejeitados</option>
              </select>
            </div>

            {/* Segunda linha - Filtros adicionais */}
            <div className="flex flex-col md:flex-row gap-4">
              <select
                value={userTypeFilter}
                onChange={(e) => setUserTypeFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Todos os tipos</option>
                <option value="TENANT">👤 Locatários</option>
                <option value="LANDLORD">🏢 Locadores</option>
              </select>

              <Input
                type="date"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="min-w-[150px]"
                placeholder="Filtrar por data"
              />

              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm('');
                  setUserTypeFilter('');
                  setDateFilter('');
                }}
                className="min-w-[100px]"
              >
                Limpar
              </Button>
            </div>

            {/* Contador de resultados */}
            <div className="text-sm text-gray-600">
              {filteredUsers.length} documento(s) encontrado(s)
              {statusFilter === 'PENDING' && ' pendente(s) de validação'}
              {statusFilter === 'APPROVED' && ' aprovado(s)'}
              {statusFilter === 'REJECTED' && ' rejeitado(s)'}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Lista de Usuários */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredUsers.map((pendingUser) => (
          <Card key={pendingUser.id} className="overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="font-semibold text-lg mb-1">{pendingUser.fullName}</h3>
                  <p className="text-sm text-gray-600 mb-2">{pendingUser.email}</p>
                  {getUserTypeBadge(pendingUser.userType)}
                </div>
              </div>

              <div className="space-y-2 text-sm text-gray-600 mb-4">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <span>{pendingUser.phoneNumber}</span>
                </div>
                
                {pendingUser.companyName && (
                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4" />
                    <span>{pendingUser.companyName}</span>
                  </div>
                )}
                
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>Enviado em {new Date(pendingUser.createdAt).toLocaleDateString('pt-AO')}</span>
                </div>
              </div>

              <Dialog>
                <DialogTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => setSelectedUser(pendingUser)}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Ver Documento
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Validação de Documento BI
                      <div className="ml-auto">
                        {getUserTypeBadge(pendingUser.userType)}
                      </div>
                    </DialogTitle>
                  </DialogHeader>
                  {selectedUser && (
                    <DocumentDetails
                      user={selectedUser}
                      onApprove={handleApprove}
                      onReject={handleReject}
                      rejectionReason={rejectionReason}
                      setRejectionReason={setRejectionReason}
                      processing={processing}
                    />
                  )}
                </DialogContent>
              </Dialog>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredUsers.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="text-gray-500">
              {searchTerm 
                ? 'Nenhum usuário encontrado com os critérios de busca' 
                : 'Nenhum documento pendente de validação'
              }
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Componente para detalhes do documento
function DocumentDetails({
  user,
  onApprove,
  onReject,
  rejectionReason,
  setRejectionReason,
  processing
}: {
  user: PendingUser;
  onApprove: (id: string) => void;
  onReject: (id: string) => void;
  rejectionReason: string;
  setRejectionReason: (reason: string) => void;
  processing: string | null;
}) {
  const [documentView, setDocumentView] = useState<'preview' | 'fullscreen'>('preview');

  return (
    <div className="space-y-6 p-6">
      {/* Cards de Informações */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Card do Usuário */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">👤 Informações do Usuário</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="font-medium">Nome Completo:</span>
              <span className="text-right">{user.fullName}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Email:</span>
              <span className="text-right">{user.email}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Telefone:</span>
              <span className="text-right">{user.phoneNumber}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Tipo:</span>
              <span className="text-right">
                {user.userType === 'TENANT' ? '👤 Locatário' : '🏢 Locador'}
              </span>
            </div>
            {user.companyName && (
              <div className="flex justify-between">
                <span className="font-medium">Empresa:</span>
                <span className="text-right">{user.companyName}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="font-medium">Data de Envio:</span>
              <span className="text-right">{new Date(user.createdAt).toLocaleDateString('pt-AO')}</span>
            </div>
            <div className="pt-2">
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => window.open(`mailto:${user.email}`, '_blank')}
              >
                <Mail className="h-4 w-4 mr-2" />
                Contactar Usuário
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Card do Documento */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">📄 Documento BI</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <FileText className="h-16 w-16 mx-auto text-blue-500 mb-3" />
              <p className="text-sm text-gray-600 mb-4">
                Documento de identificação enviado pelo usuário
              </p>

              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => window.open(user.biDocument, '_blank')}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Abrir em Nova Aba
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => setDocumentView(documentView === 'preview' ? 'fullscreen' : 'preview')}
                >
                  {documentView === 'preview' ? '🔍 Visualização Ampliada' : '📱 Visualização Normal'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Visualização do Documento */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">🔍 Pré-visualização do Documento</CardTitle>
        </CardHeader>
        <CardContent>
          <div
            className={`border rounded-lg overflow-hidden bg-gray-50 ${
              documentView === 'fullscreen' ? 'h-[600px]' : 'h-[400px]'
            }`}
          >
            {user.biDocument.toLowerCase().includes('.pdf') ? (
              <iframe
                src={user.biDocument}
                className="w-full h-full"
                title="Documento BI"
              />
            ) : (
              <img
                src={user.biDocument}
                alt="Documento BI"
                className="w-full h-full object-contain cursor-zoom-in"
                onClick={() => window.open(user.biDocument, '_blank')}
              />
            )}
          </div>

          <div className="mt-4 text-center">
            <p className="text-sm text-gray-600">
              💡 Clique na imagem para abrir em tamanho completo
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Checklist de Validação */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="text-lg text-blue-800">✅ Checklist de Validação</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <input type="checkbox" id="readable" className="rounded" />
              <label htmlFor="readable">Documento legível e sem borrões</label>
            </div>
            <div className="flex items-center gap-2">
              <input type="checkbox" id="complete" className="rounded" />
              <label htmlFor="complete">Todas as informações visíveis</label>
            </div>
            <div className="flex items-center gap-2">
              <input type="checkbox" id="valid" className="rounded" />
              <label htmlFor="valid">Documento aparenta ser válido</label>
            </div>
            <div className="flex items-center gap-2">
              <input type="checkbox" id="matches" className="rounded" />
              <label htmlFor="matches">Nome no documento confere com o cadastro</label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Ações de validação */}
      <Card className="border-orange-200 bg-orange-50">
        <CardHeader>
          <CardTitle className="text-lg text-orange-800">⚖️ Ações de Validação</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2 text-gray-700">
              Observações da Validação
            </label>
            <Textarea
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              placeholder="Adicione observações sobre a validação ou motivo da rejeição..."
              rows={4}
              className="resize-none"
            />
            <div className="text-xs text-gray-500 mt-1">
              * Obrigatório apenas para rejeição
            </div>
          </div>

          <div className="flex gap-4 pt-4">
            <Button
              onClick={() => onApprove(user.id)}
              disabled={processing === user.id}
              className="flex-1 bg-green-600 hover:bg-green-700 text-white py-3"
              size="lg"
            >
              <Check className="h-5 w-5 mr-2" />
              {processing === user.id ? 'Aprovando...' : 'Aprovar Documento'}
            </Button>

            <Button
              onClick={() => onReject(user.id)}
              disabled={processing === user.id}
              variant="destructive"
              className="flex-1 py-3"
              size="lg"
            >
              <X className="h-5 w-5 mr-2" />
              {processing === user.id ? 'Rejeitando...' : 'Rejeitar Documento'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
