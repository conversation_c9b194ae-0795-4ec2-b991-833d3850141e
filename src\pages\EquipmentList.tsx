import React, { useState } from 'react';
import { Star, MapPin, ArrowRight, Grid, List, Map as MapIcon, Eye, EyeOff, User } from 'lucide-react';
import { Button } from '../components/ui/button';
import EquipmentListingOptions from '../components/EquipmentListingOptions';

interface Owner {
  id: number;
  name: string;
  avatar: string;
  rating: number;
  reviews: number;
}

interface Equipment {
  id: number;
  name: string;
  image: string;
  price: number;
  location: string;
  rating: number;
  reviews: number;
  description?: string;
  owner: Owner;
  coordinates?: { lat: number; lng: number };
}

export default function EquipmentList() {
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'map'>('grid');
  const [expandedItems, setExpandedItems] = useState<number[]>([]);

  // Toggle expanded view for an item
  const toggleItemExpand = (id: number) => {
    if (expandedItems.includes(id)) {
      setExpandedItems(expandedItems.filter(itemId => itemId !== id));
    } else {
      setExpandedItems([...expandedItems, id]);
    }
  };

  // Check if an item is expanded
  const isItemExpanded = (id: number) => expandedItems.includes(id);

  const equipment: Equipment[] = [
    {
      id: 1,
      name: 'Retroescavadeira CAT 420F2',
      image: 'https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=600',
      price: 250000,
      location: 'Luanda, Talatona',
      rating: 4.2,
      reviews: 48,
      description: 'Retroescavadeira em excelente estado, ideal para obras de médio e grande porte. Inclui diversos acessórios e manutenção recente.',
      owner: {
        id: 101,
        name: 'Carlos Mendes',
        avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
        rating: 4.7,
        reviews: 23
      },
      coordinates: { lat: -8.9177, lng: 13.1834 }
    },
    {
      id: 2,
      name: 'Empilhadeira Toyota 8FD',
      image: 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?auto=format&fit=crop&w=600',
      price: 180000,
      location: 'Luanda, Viana',
      rating: 4.5,
      reviews: 32,
      description: 'Empilhadeira com capacidade para 3 toneladas, altura máxima de elevação de 4,5 metros. Bateria nova e manutenção em dia.',
      owner: {
        id: 102,
        name: 'Ana Silva',
        avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
        rating: 4.9,
        reviews: 41
      },
      coordinates: { lat: -8.9077, lng: 13.3634 }
    },
    {
      id: 3,
      name: 'Guindaste Liebherr LTM',
      image: 'https://images.unsplash.com/photo-1541625602330-2277a4c46182?auto=format&fit=crop&w=600',
      price: 450000,
      location: 'Luanda, Cacuaco',
      rating: 4.8,
      reviews: 15,
      description: 'Guindaste móvel com capacidade de 100 toneladas, lança telescópica de 60 metros. Disponível com operador certificado.',
      owner: {
        id: 103,
        name: 'Pedro Santos',
        avatar: 'https://randomuser.me/api/portraits/men/67.jpg',
        rating: 4.6,
        reviews: 19
      },
      coordinates: { lat: -8.7877, lng: 13.3734 }
    },
    {
      id: 4,
      name: 'Gerador Diesel 100kVA',
      image: 'https://images.unsplash.com/photo-1586744666440-fef0dc5d0d18?auto=format&fit=crop&w=600',
      price: 120000,
      location: 'Luanda, Belas',
      rating: 4.3,
      reviews: 27,
      description: 'Gerador silenciado com motor Cummins, ideal para eventos e obras. Inclui tanque de combustível extra e painel de controle digital.',
      owner: {
        id: 104,
        name: 'Mariana Costa',
        avatar: 'https://randomuser.me/api/portraits/women/22.jpg',
        rating: 4.4,
        reviews: 31
      },
      coordinates: { lat: -8.9977, lng: 13.2034 }
    },
    {
      id: 5,
      name: 'Compressor de Ar Atlas Copco',
      image: 'https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=600',
      price: 95000,
      location: 'Luanda, Kilamba',
      rating: 4.6,
      reviews: 19,
      description: 'Compressor de ar portátil com capacidade de 185 CFM, pressão máxima de 100 PSI. Ideal para ferramentas pneumáticas e jateamento.',
      owner: {
        id: 105,
        name: 'João Oliveira',
        avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
        rating: 4.8,
        reviews: 37
      },
      coordinates: { lat: -8.9277, lng: 13.2934 }
    },
    {
      id: 6,
      name: 'Betoneira 400L',
      image: 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?auto=format&fit=crop&w=600',
      price: 45000,
      location: 'Luanda, Talatona',
      rating: 4.1,
      reviews: 22,
      description: 'Betoneira com capacidade de 400 litros, motor elétrico trifásico. Ideal para obras de médio porte e construções residenciais.',
      owner: {
        id: 106,
        name: 'Luísa Fernandes',
        avatar: 'https://randomuser.me/api/portraits/women/33.jpg',
        rating: 4.3,
        reviews: 15
      },
      coordinates: { lat: -8.9177, lng: 13.1934 }
    }
  ];

  // Render grid view
  const renderGridView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {equipment.map((item) => (
        <div key={item.id} className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="relative">
            <img
              src={item.image}
              alt={item.name}
              className="w-full h-40 object-cover"
            />
            <div className="absolute bottom-2 right-2 bg-white px-2 py-1 rounded text-sm font-semibold text-cyan-600">
              {item.price.toLocaleString('pt-AO')} Kz/dia
            </div>
          </div>
          <div className="p-4">
            <h3 className="text-lg font-semibold mb-2">{item.name}</h3>
            <div className="flex items-center space-x-2 mb-4">
              <MapPin className="w-4 h-4 text-gray-400" />
              <span className="text-gray-600 text-sm">{item.location}</span>
            </div>

            {isItemExpanded(item.id) && (
              <div className="mb-4 space-y-3">
                <p className="text-gray-600 text-sm">{item.description}</p>
                <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded-md">
                  <img
                    src={item.owner.avatar}
                    alt={item.owner.name}
                    className="w-10 h-10 rounded-full"
                  />
                  <div>
                    <p className="font-medium text-sm">{item.owner.name}</p>
                    <div className="flex items-center">
                      <Star className="w-3 h-3 text-yellow-400 fill-current" />
                      <span className="text-xs text-gray-600 ml-1">
                        {item.owner.rating} ({item.owner.reviews} avaliações)
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-1">
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <span className="text-sm text-gray-600">
                  {item.rating} ({item.reviews} avaliações)
                </span>
              </div>
              <Button
                variant="outline"
                className="flex items-center space-x-1 text-xs"
                onClick={() => toggleItemExpand(item.id)}
              >
                {isItemExpanded(item.id) ? (
                  <>
                    <span>Ver menos</span>
                    <EyeOff className="w-4 h-4" />
                  </>
                ) : (
                  <>
                    <span>Ver mais</span>
                    <Eye className="w-4 h-4" />
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  // Render list view
  const renderListView = () => (
    <div className="space-y-4 sm:space-y-6">
      {equipment.map((item) => (
        <div key={item.id} className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="flex flex-row">
            <div className="w-1/3 relative h-auto">
              <img
                src={item.image}
                alt={item.name}
                className="w-full h-full object-cover"
              />
              <div className="absolute bottom-2 right-2 bg-white px-2 py-1 rounded text-sm font-semibold text-cyan-600">
                {item.price.toLocaleString('pt-AO')} Kz/dia
              </div>
            </div>
            <div className="p-4 w-2/3">
              <div className="flex flex-col mb-3">
                <div>
                  <h3 className="text-lg font-semibold mb-2">{item.name}</h3>
                  <div className="flex items-center space-x-2 mb-2">
                    <MapPin className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600 text-sm">{item.location}</span>
                  </div>
                  <div className="flex items-center space-x-1 mb-4">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-sm text-gray-600">
                      {item.rating} ({item.reviews} avaliações)
                    </span>
                  </div>
                </div>
                <div className="flex items-center mt-2">
                  <img
                    src={item.owner.avatar}
                    alt={item.owner.name}
                    className="w-8 h-8 rounded-full mr-2"
                  />
                  <div>
                    <p className="font-medium text-sm">{item.owner.name}</p>
                    <div className="flex items-center">
                      <Star className="w-3 h-3 text-yellow-400 fill-current" />
                      <span className="text-xs text-gray-600 ml-1">
                        {item.owner.rating}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {isItemExpanded(item.id) && (
                <div className="mb-4">
                  <p className="text-gray-600">{item.description}</p>
                </div>
              )}

              <div className="flex justify-end mt-2">
                <Button
                  variant="outline"
                  className="flex items-center space-x-1 text-xs"
                  onClick={() => toggleItemExpand(item.id)}
                >
                  {isItemExpanded(item.id) ? (
                    <>
                      <span>Ver menos</span>
                      <EyeOff className="w-4 h-4" />
                    </>
                  ) : (
                    <>
                      <span>Ver mais</span>
                      <Eye className="w-4 h-4" />
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );


  // Render map view
  const renderMapView = () => (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden p-4">
      <div className="bg-gray-200 h-[500px] rounded-lg flex items-center justify-center mb-4">
        <div className="text-center">
          <MapIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
          <p className="text-gray-600">Mapa de equipamentos disponíveis</p>
          <p className="text-gray-500 text-sm">(Visualização de mapa simulada)</p>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {equipment.map((item) => (
          <div key={item.id} className="border border-gray-200 rounded-md p-2 flex items-center space-x-2">
            <img
              src={item.image}
              alt={item.name}
              className="w-14 h-14 object-cover rounded-md"
            />
            <div className="flex-1">
              <h4 className="font-medium text-sm">{item.name}</h4>
              <p className="text-gray-600 text-xs">{item.location}</p>
              <p className="text-cyan-600 font-medium text-xs">{item.price.toLocaleString('pt-AO')} Kz/dia</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="container mx-auto px-4 py-8 pt-24">
      <div className="mb-8">
        <h1 className="text-2xl font-bold mb-6">Equipamentos Disponíveis</h1>

        <div className="mb-6">
          <EquipmentListingOptions
            isDarkMode={false}
            viewMode={viewMode}
            onViewModeChange={setViewMode}
          />
          <div className="mt-4 text-right">
            <span className="text-gray-600">{equipment.length} equipamentos encontrados</span>
          </div>
        </div>
      </div>

      {viewMode === 'grid' && renderGridView()}
      {viewMode === 'list' && renderListView()}
      {viewMode === 'map' && renderMapView()}
    </div>
  );
}