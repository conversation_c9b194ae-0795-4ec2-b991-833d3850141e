import React, { useState, useEffect } from 'react';
import apiService from '../../services/api';

interface StatsProps {
  isDarkMode: boolean;
}

/**
 * Componente que exibe estatísticas da plataforma
 * Mostra o número de cidades, fornecedores e equipamentos disponíveis
 */
const Stats: React.FC<StatsProps> = ({ isDarkMode }) => {
  const [stats, setStats] = useState({
    cities: 12,
    suppliers: 83,
    equipment: 325
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await apiService.get('/stats');
        if (response.data) {
          setStats({
            cities: response.data.cities || 12,
            suppliers: response.data.suppliers || 83,
            equipment: response.data.equipment || 325
          });
        }
      } catch (error) {
        console.error('Erro ao buscar estatísticas:', error);
        // Manter valores padrão em caso de erro
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return (
      <div className="grid grid-cols-3 gap-8 mb-10">
        {[1, 2, 3].map((i) => (
          <div key={i} className="text-center">
            <div className="h-9 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
            <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-3 gap-8 mb-10">
      <div className="text-center">
        <h3 className="text-3xl font-bold" style={{color: '#3569b0'}}>{stats.cities}</h3>
        <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Cidades</p>
      </div>
      <div className="text-center">
        <h3 className="text-3xl font-bold" style={{color: '#3569b0'}}>{stats.suppliers}</h3>
        <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Fornecedores</p>
      </div>
      <div className="text-center">
        <h3 className="text-3xl font-bold" style={{color: '#3569b0'}}>{stats.equipment}</h3>
        <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Equipamentos</p>
      </div>
    </div>
  );
};

export default Stats;
