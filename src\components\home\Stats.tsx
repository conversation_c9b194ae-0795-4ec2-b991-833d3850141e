import React from 'react';

interface StatsProps {
  isDarkMode: boolean;
}

/**
 * Componente que exibe estatísticas da plataforma
 * Mostra o número de cidades, fornecedores e equipamentos disponíveis
 */
const Stats: React.FC<StatsProps> = ({ isDarkMode }) => {
  return (
    <div className="grid grid-cols-3 gap-8 mb-10">
      <div className="text-center">
        <h3 className="text-3xl font-bold" style={{color: '#3569b0'}}>12</h3>
        <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Cidades</p>
      </div>
      <div className="text-center">
        <h3 className="text-3xl font-bold" style={{color: '#3569b0'}}>83</h3>
        <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Fornecedores</p>
      </div>
      <div className="text-center">
        <h3 className="text-3xl font-bold" style={{color: '#3569b0'}}>325</h3>
        <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Equipamentos</p>
      </div>
    </div>
  );
};

export default Stats;
