import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Users, Award, Target, Zap, Globe, Send, User, Mail, Phone,
  Upload, FileText, Building, Star, Heart, CheckCircle
} from 'lucide-react';
import { Button } from '../components/ui/button';
import BackButton from '../components/ui/BackButton';

interface CareersProps {
  isDarkMode: boolean;
}

interface Department {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
}

interface ApplicationData {
  name: string;
  email: string;
  phone: string;
  department: string;
  position: string;
  experience: string;
  coverLetter: string;
  resume: File | null;
}

const Careers: React.FC<CareersProps> = ({ isDarkMode }) => {
  const navigate = useNavigate();
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const departments: Department[] = [
    {
      id: 'engineering',
      name: 'Engenharia',
      description: 'Desenvolvimento e manutenção de equipamentos',
      icon: <Zap className="w-6 h-6" />
    },
    {
      id: 'operations',
      name: 'Operações',
      description: 'Gestão de frotas e logística',
      icon: <Target className="w-6 h-6" />
    },
    {
      id: 'sales',
      name: 'Vendas',
      description: 'Relacionamento com clientes e vendas',
      icon: <Users className="w-6 h-6" />
    },
    {
      id: 'technology',
      name: 'Tecnologia',
      description: 'Desenvolvimento de software e TI',
      icon: <Globe className="w-6 h-6" />
    }
  ];

  const [applicationData, setApplicationData] = useState<ApplicationData>({
    name: '',
    email: '',
    phone: '',
    department: '',
    position: '',
    experience: '',
    coverLetter: '',
    resume: null
  });

  const handleApplicationSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Aqui seria enviado para a API
    console.log('Application submitted:', applicationData);
    setIsSubmitted(true);
    setShowApplicationForm(false);
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setApplicationData({ ...applicationData, resume: file });
    }
  };

  return (
    <div className="min-h-screen max-w-full overflow-x-hidden">
      <div className={`container mx-auto px-4 py-8 pt-24 max-w-7xl ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
        <div className="flex items-center space-x-4 mb-8">
          <BackButton isDarkMode={isDarkMode} />
          <div>
            <h1 className={`text-4xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Carreiras</h1>
            <p className={`text-lg ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Junte-se à nossa equipe e construa o futuro dos equipamentos em Angola
            </p>
          </div>
        </div>

        {/* Hero Section */}
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-8 mb-12`}>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <h2 className={`text-3xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Por que trabalhar na YMRentals?
              </h2>
              <p className={`text-lg mb-6 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Somos líderes no mercado de aluguel de equipamentos em Angola, oferecendo oportunidades
                de crescimento em um ambiente inovador e colaborativo.
              </p>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <Award className="w-6 h-6" style={{color: '#3569b0'}} />
                  <span>Reconhecimento</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Target className="w-6 h-6" style={{color: '#3569b0'}} />
                  <span>Crescimento</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Users className="w-6 h-6" style={{color: '#3569b0'}} />
                  <span>Equipe Unida</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Star className="w-6 h-6" style={{color: '#3569b0'}} />
                  <span>Benefícios</span>
                </div>
              </div>
            </div>
            <div className="relative">
              <img
                src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?auto=format&fit=crop&w=600&q=80"
                alt="Equipe YMRentals"
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>

        {/* Departamentos */}
        <div className="mb-12">
          <h2 className={`text-2xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Nossos Departamentos
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {departments.map((dept) => (
              <div
                key={dept.id}
                className={`p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow ${
                  isDarkMode ? 'bg-gray-800' : 'bg-white'
                }`}
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 rounded-lg" style={{backgroundColor: '#3569b0', color: 'white'}}>
                    {dept.icon}
                  </div>
                  <div>
                    <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {dept.name}
                    </h3>
                  </div>
                </div>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {dept.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Benefícios */}
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-8 mb-12`}>
          <h2 className={`text-2xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Nossos Benefícios
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-6 h-6 text-green-500 mt-1" />
              <div>
                <h3 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Salário Competitivo
                </h3>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Remuneração justa e compatível com o mercado
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-6 h-6 text-green-500 mt-1" />
              <div>
                <h3 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Seguro de Saúde
                </h3>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Cobertura médica e odontológica completa
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-6 h-6 text-green-500 mt-1" />
              <div>
                <h3 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Vale Alimentação
                </h3>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Auxílio alimentação mensal
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-6 h-6 text-green-500 mt-1" />
              <div>
                <h3 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Transporte
                </h3>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Transporte corporativo ou vale transporte
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-6 h-6 text-green-500 mt-1" />
              <div>
                <h3 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Treinamentos
                </h3>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Capacitação e desenvolvimento profissional
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-6 h-6 text-green-500 mt-1" />
              <div>
                <h3 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Plano de Carreira
                </h3>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Oportunidades de crescimento e promoção
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Candidatura Espontânea */}
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-8 text-center`}>
          {!isSubmitted ? (
            <>
              <h2 className={`text-2xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Candidatura Espontânea
              </h2>
              <p className={`text-lg mb-6 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Não encontrou a vaga ideal? Envie seu currículo e entraremos em contato quando surgir uma oportunidade que combine com seu perfil.
              </p>
              <Button
                onClick={() => setShowApplicationForm(true)}
                className="text-white px-8 py-3 text-lg"
                style={{backgroundColor: '#3569b0'}}
              >
                <Send className="w-5 h-5 mr-2" />
                Enviar Candidatura
              </Button>
            </>
          ) : (
            <div>
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h2 className={`text-2xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Candidatura Enviada!
              </h2>
              <p className={`text-lg ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Obrigado pelo seu interesse! Analisaremos seu perfil e entraremos em contato em breve.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Modal de Candidatura */}
      {showApplicationForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto`}>
            <h3 className={`text-2xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Candidatura Espontânea
            </h3>

            <form onSubmit={handleApplicationSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Nome Completo *
                  </label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="text"
                      required
                      value={applicationData.name}
                      onChange={(e) => setApplicationData({...applicationData, name: e.target.value})}
                      className={`w-full pl-10 pr-4 py-2 border rounded-lg ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Email *
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="email"
                      required
                      value={applicationData.email}
                      onChange={(e) => setApplicationData({...applicationData, email: e.target.value})}
                      className={`w-full pl-10 pr-4 py-2 border rounded-lg ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>
                </div>
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Telefone *
                </label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="tel"
                    required
                    value={applicationData.phone}
                    onChange={(e) => setApplicationData({...applicationData, phone: e.target.value})}
                    className={`w-full pl-10 pr-4 py-2 border rounded-lg ${
                      isDarkMode
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Departamento de Interesse
                  </label>
                  <select
                    value={applicationData.department}
                    onChange={(e) => setApplicationData({...applicationData, department: e.target.value})}
                    className={`w-full px-3 py-2 border rounded-lg ${
                      isDarkMode
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  >
                    <option value="">Selecione um departamento</option>
                    {departments.map((dept) => (
                      <option key={dept.id} value={dept.id}>
                        {dept.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Cargo Pretendido
                  </label>
                  <input
                    type="text"
                    value={applicationData.position}
                    onChange={(e) => setApplicationData({...applicationData, position: e.target.value})}
                    placeholder="Ex: Engenheiro, Técnico, Vendedor..."
                    className={`w-full px-3 py-2 border rounded-lg ${
                      isDarkMode
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    }`}
                  />
                </div>
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Experiência Profissional
                </label>
                <select
                  value={applicationData.experience}
                  onChange={(e) => setApplicationData({...applicationData, experience: e.target.value})}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                >
                  <option value="">Selecione sua experiência</option>
                  <option value="entry">Sem experiência / Recém-formado</option>
                  <option value="junior">1-2 anos</option>
                  <option value="mid">3-5 anos</option>
                  <option value="senior">6-10 anos</option>
                  <option value="expert">Mais de 10 anos</option>
                </select>
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Currículo (PDF) *
                </label>
                <div className="relative">
                  <input
                    type="file"
                    accept=".pdf"
                    required
                    onChange={handleFileUpload}
                    className="hidden"
                    id="resume-upload"
                  />
                  <label
                    htmlFor="resume-upload"
                    className={`w-full p-4 border-2 border-dashed rounded-lg cursor-pointer flex items-center justify-center space-x-2 ${
                      isDarkMode
                        ? 'border-gray-600 hover:border-gray-500'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <Upload className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-500">
                      {applicationData.resume ? applicationData.resume.name : 'Clique para enviar seu currículo'}
                    </span>
                  </label>
                </div>
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Carta de Apresentação
                </label>
                <textarea
                  rows={6}
                  value={applicationData.coverLetter}
                  onChange={(e) => setApplicationData({...applicationData, coverLetter: e.target.value})}
                  placeholder="Conte-nos sobre você, suas motivações e por que gostaria de trabalhar na YMRentals..."
                  className={`w-full p-3 border rounded-lg resize-none ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  }`}
                />
              </div>

              <div className="flex space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowApplicationForm(false)}
                  className="flex-1"
                >
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  className="flex-1 text-white"
                  style={{backgroundColor: '#3569b0'}}
                >
                  <Send className="w-4 h-4 mr-2" />
                  Enviar Candidatura
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Careers;
