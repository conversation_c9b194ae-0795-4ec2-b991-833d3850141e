import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { Eye, Check, X, Search, Receipt, DollarSign, Calendar, User, Package, Mail } from 'lucide-react';
import { apiService } from '../../services/api';

interface PendingRental {
  id: string;
  startDate: string;
  endDate: string;
  totalAmount: number;
  paymentReceipt: string;
  paymentReceiptStatus: string;
  createdAt: string;
  equipment: {
    id: string;
    name: string;
    images: string[];
  };
  renter: {
    id: string;
    fullName: string;
    email: string;
  };
  owner: {
    id: string;
    fullName: string;
    email: string;
    companyName?: string;
  };
}

interface PaymentValidationProps {
  isDarkMode?: boolean;
}

export default function PaymentValidation({ isDarkMode }: PaymentValidationProps) {
  const [pendingRentals, setPendingRentals] = useState<PendingRental[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRental, setSelectedRental] = useState<PendingRental | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [processing, setProcessing] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('PENDING');
  const [dateFilter, setDateFilter] = useState('');
  const [amountFilter, setAmountFilter] = useState({ min: '', max: '' });

  useEffect(() => {
    fetchRentals();
  }, [statusFilter, dateFilter, amountFilter]);

  const fetchRentals = async () => {
    try {
      setLoading(true);
      let data;

      if (statusFilter === 'PENDING') {
        data = await apiService.getAdminPendingPaymentReceipts();
      } else {
        // Buscar todos os aluguéis com filtro de status
        const params = {
          paymentReceiptStatus: statusFilter
        };
        data = await apiService.getAllRentals(params);
      }

      setPendingRentals(data);
    } catch (error: any) {
      console.error('Erro ao carregar comprovativos:', error);
      alert('Erro ao carregar comprovativos de pagamento');
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (rentalId: string) => {
    try {
      setProcessing(rentalId);
      await apiService.adminValidatePaymentReceipt(rentalId, true);
      
      setPendingRentals(prev => prev.filter(r => r.id !== rentalId));
      setSelectedRental(null);
      
      alert('Comprovativo aprovado com sucesso!');
    } catch (error: any) {
      console.error('Erro ao aprovar comprovativo:', error);
      alert('Erro ao aprovar comprovativo');
    } finally {
      setProcessing(null);
    }
  };

  const handleReject = async (rentalId: string) => {
    if (!rejectionReason.trim()) {
      alert('Por favor, forneça uma razão para a rejeição');
      return;
    }

    try {
      setProcessing(rentalId);
      await apiService.adminValidatePaymentReceipt(rentalId, false, rejectionReason);
      
      setPendingRentals(prev => prev.filter(r => r.id !== rentalId));
      setSelectedRental(null);
      setRejectionReason('');
      
      alert('Comprovativo rejeitado com sucesso!');
    } catch (error: any) {
      console.error('Erro ao rejeitar comprovativo:', error);
      alert('Erro ao rejeitar comprovativo');
    } finally {
      setProcessing(null);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { label: 'Pendente', className: 'bg-yellow-100 text-yellow-800' },
      APPROVED: { label: 'Aprovado', className: 'bg-green-100 text-green-800' },
      REJECTED: { label: 'Rejeitado', className: 'bg-red-100 text-red-800' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING;
    return <Badge className={config.className}>{config.label}</Badge>;
  };

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString('pt-AO')} Kz`;
  };

  const formatDateRange = (startDate: string, endDate: string) => {
    const start = new Date(startDate).toLocaleDateString('pt-AO');
    const end = new Date(endDate).toLocaleDateString('pt-AO');
    return `${start} - ${end}`;
  };

  const filteredRentals = pendingRentals.filter(rental => {
    const matchesSearch = rental.equipment.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rental.renter.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rental.owner.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (rental.owner.companyName && rental.owner.companyName.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesDate = !dateFilter ||
      new Date(rental.createdAt).toDateString() === new Date(dateFilter).toDateString();

    const matchesAmount = (!amountFilter.min || rental.totalAmount >= parseFloat(amountFilter.min)) &&
      (!amountFilter.max || rental.totalAmount <= parseFloat(amountFilter.max));

    return matchesSearch && matchesDate && matchesAmount;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Receipt className="h-5 w-5" />
            Validação de Comprovativos de Pagamento
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Primeira linha - Busca e Status */}
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Buscar por equipamento, locatário ou locador..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[150px]"
              >
                <option value="PENDING">🟡 Pendentes</option>
                <option value="APPROVED">✅ Aprovados</option>
                <option value="REJECTED">❌ Rejeitados</option>
              </select>
            </div>

            {/* Segunda linha - Filtros adicionais */}
            <div className="flex flex-col md:flex-row gap-4">
              <Input
                type="date"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="min-w-[150px]"
                placeholder="Filtrar por data"
              />

              <div className="flex gap-2">
                <Input
                  type="number"
                  placeholder="Valor mín. (Kz)"
                  value={amountFilter.min}
                  onChange={(e) => setAmountFilter(prev => ({ ...prev, min: e.target.value }))}
                  className="min-w-[120px]"
                />
                <Input
                  type="number"
                  placeholder="Valor máx. (Kz)"
                  value={amountFilter.max}
                  onChange={(e) => setAmountFilter(prev => ({ ...prev, max: e.target.value }))}
                  className="min-w-[120px]"
                />
              </div>

              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm('');
                  setDateFilter('');
                  setAmountFilter({ min: '', max: '' });
                }}
                className="min-w-[100px]"
              >
                Limpar
              </Button>
            </div>

            {/* Contador de resultados */}
            <div className="text-sm text-gray-600">
              {filteredRentals.length} comprovativo(s) encontrado(s)
              {statusFilter === 'PENDING' && ' pendente(s) de validação'}
              {statusFilter === 'APPROVED' && ' aprovado(s)'}
              {statusFilter === 'REJECTED' && ' rejeitado(s)'}
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredRentals.map((rental) => (
          <Card key={rental.id} className="overflow-hidden">
            <div className="aspect-video bg-gray-100 relative">
              {rental.equipment.images && rental.equipment.images.length > 0 ? (
                <img
                  src={rental.equipment.images[0]}
                  alt={rental.equipment.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400">
                  <Package className="h-12 w-12" />
                </div>
              )}
              <div className="absolute top-2 right-2">
                {getStatusBadge(rental.paymentReceiptStatus)}
              </div>
            </div>
            
            <CardContent className="p-4">
              <h3 className="font-semibold text-lg mb-2 line-clamp-1">{rental.equipment.name}</h3>
              
              <div className="space-y-2 text-sm text-gray-600 mb-4">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <span>Locatário: {rental.renter.fullName}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  <span>{formatCurrency(rental.totalAmount)}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>{formatDateRange(rental.startDate, rental.endDate)}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Receipt className="h-4 w-4" />
                  <span>Enviado em {new Date(rental.createdAt).toLocaleDateString('pt-AO')}</span>
                </div>
              </div>

              <Dialog>
                <DialogTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => setSelectedRental(rental)}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Ver Comprovativo
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                      <Receipt className="h-5 w-5" />
                      Validação de Comprovativo de Pagamento
                      <div className="ml-auto">
                        {getStatusBadge(rental.paymentReceiptStatus)}
                      </div>
                    </DialogTitle>
                  </DialogHeader>
                  {selectedRental && (
                    <PaymentDetails
                      rental={selectedRental}
                      onApprove={handleApprove}
                      onReject={handleReject}
                      rejectionReason={rejectionReason}
                      setRejectionReason={setRejectionReason}
                      processing={processing}
                    />
                  )}
                </DialogContent>
              </Dialog>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredRentals.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="text-gray-500">
              {searchTerm 
                ? 'Nenhum aluguel encontrado com os critérios de busca' 
                : 'Nenhum comprovativo pendente de validação'
              }
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

function PaymentDetails({ 
  rental, 
  onApprove, 
  onReject, 
  rejectionReason, 
  setRejectionReason, 
  processing 
}: {
  rental: PendingRental;
  onApprove: (id: string) => void;
  onReject: (id: string) => void;
  rejectionReason: string;
  setRejectionReason: (reason: string) => void;
  processing: string | null;
}) {
  const [receiptView, setReceiptView] = useState<'preview' | 'fullscreen'>('preview');

  const calculateDuration = () => {
    const start = new Date(rental.startDate);
    const end = new Date(rental.endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    <div className="space-y-6 p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 className="font-semibold mb-3">Informações do Aluguel</h3>
          <div className="space-y-2 text-sm">
            <div><strong>Equipamento:</strong> {rental.equipment.name}</div>
            <div><strong>Valor Total:</strong> {rental.totalAmount.toLocaleString('pt-AO')} Kz</div>
            <div><strong>Período:</strong> {new Date(rental.startDate).toLocaleDateString('pt-AO')} - {new Date(rental.endDate).toLocaleDateString('pt-AO')}</div>
            <div><strong>Status:</strong> {rental.paymentReceiptStatus}</div>
          </div>
        </div>

        <div>
          <h3 className="font-semibold mb-3">Partes Envolvidas</h3>
          <div className="space-y-3 text-sm">
            <div>
              <strong>Locatário:</strong>
              <div className="ml-4">
                <div>{rental.renter.fullName}</div>
                <div className="text-gray-600">{rental.renter.email}</div>
              </div>
            </div>
            <div>
              <strong>Locador:</strong>
              <div className="ml-4">
                <div>{rental.owner.companyName || rental.owner.fullName}</div>
                <div className="text-gray-600">{rental.owner.email}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div>
        <h3 className="font-semibold mb-3">Comprovativo de Pagamento</h3>
        <div className="border rounded-lg overflow-hidden bg-gray-50" style={{ height: '400px' }}>
          {rental.paymentReceipt.toLowerCase().includes('.pdf') ? (
            <iframe
              src={rental.paymentReceipt}
              className="w-full h-full"
              title="Comprovativo de Pagamento"
            />
          ) : (
            <img
              src={rental.paymentReceipt}
              alt="Comprovativo de Pagamento"
              className="w-full h-full object-contain"
            />
          )}
        </div>
        <div className="mt-2 text-center">
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open(rental.paymentReceipt, '_blank')}
          >
            <Eye className="h-4 w-4 mr-2" />
            Abrir em Nova Aba
          </Button>
        </div>
      </div>

      <div className="border-t pt-6">
        <h3 className="font-semibold mb-4">Ações de Validação</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Razão da rejeição (obrigatório apenas para rejeição)
            </label>
            <Textarea
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              placeholder="Descreva o motivo da rejeição do comprovativo..."
              rows={3}
            />
          </div>

          <div className="flex gap-4">
            <Button
              onClick={() => onApprove(rental.id)}
              disabled={processing === rental.id}
              className="flex-1 bg-green-600 hover:bg-green-700"
            >
              <Check className="h-4 w-4 mr-2" />
              {processing === rental.id ? 'Aprovando...' : 'Aprovar Pagamento'}
            </Button>
            
            <Button
              onClick={() => onReject(rental.id)}
              disabled={processing === rental.id}
              variant="destructive"
              className="flex-1"
            >
              <X className="h-4 w-4 mr-2" />
              {processing === rental.id ? 'Rejeitando...' : 'Rejeitar Pagamento'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
