import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import apiService from '../services/api';

interface Rental {
  id: string;
  equipmentId: string;
  renterId: string;
  ownerId: string;
  startDate: string;
  endDate: string;
  totalAmount: number;
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'PAID' | 'ACTIVE' | 'COMPLETED' | 'CANCELLED';
  paymentStatus?: string;
  paymentMethod?: string;
  transactionId?: string;
  createdAt: string;
  updatedAt: string;
  equipment: {
    id: string;
    name: string;
    images: string[];
    price: number;
    pricePeriod: string;
    owner: {
      id: string;
      fullName: string;
      profilePicture?: string;
    };
  };
  renter: {
    id: string;
    fullName: string;
    profilePicture?: string;
  };
}

interface RentalContextType {
  rentals: Rental[];
  myRentals: Rental[];
  rentalHistory: Rental[];
  loading: boolean;
  error: string | null;
  createRental: (rentalData: any) => Promise<Rental>;
  updateRentalStatus: (id: string, status: string) => Promise<Rental>;
  processPayment: (id: string, paymentData: any) => Promise<Rental>;
  cancelRental: (id: string) => Promise<void>;
  fetchMyRentals: (type?: 'renter' | 'owner') => Promise<void>;
  fetchRentalHistory: () => Promise<void>;
  fetchRentalById: (id: string) => Promise<Rental | null>;
}

const RentalContext = createContext<RentalContextType | undefined>(undefined);

export const useRental = () => {
  const context = useContext(RentalContext);
  if (context === undefined) {
    throw new Error('useRental must be used within a RentalProvider');
  }
  return context;
};

interface RentalProviderProps {
  children: ReactNode;
}

export const RentalProvider: React.FC<RentalProviderProps> = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const [rentals, setRentals] = useState<Rental[]>([]);
  const [myRentals, setMyRentals] = useState<Rental[]>([]);
  const [rentalHistory, setRentalHistory] = useState<Rental[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Carregar dados quando usuário estiver autenticado
  useEffect(() => {
    if (isAuthenticated && user) {
      fetchMyRentals();
      fetchRentalHistory();
    }
  }, [isAuthenticated, user]);

  const createRental = async (rentalData: any): Promise<Rental> => {
    try {
      setLoading(true);
      const rental = await apiService.createRental(rentalData);
      setMyRentals(prev => [rental, ...prev]);
      return rental;
    } catch (err: any) {
      setError(err.message || 'Erro ao criar aluguel');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateRentalStatus = async (id: string, status: string): Promise<Rental> => {
    try {
      const rental = await apiService.updateRentalStatus(id, status);
      
      // Atualizar nas listas locais
      setMyRentals(prev => 
        prev.map(r => r.id === id ? { ...r, status: status as any } : r)
      );
      
      return rental;
    } catch (err: any) {
      setError(err.message || 'Erro ao atualizar status do aluguel');
      throw err;
    }
  };

  const processPayment = async (id: string, paymentData: any): Promise<Rental> => {
    try {
      setLoading(true);
      const rental = await apiService.processRentalPayment(id, paymentData);
      
      // Atualizar nas listas locais
      setMyRentals(prev => 
        prev.map(r => r.id === id ? { ...r, ...rental } : r)
      );
      
      return rental;
    } catch (err: any) {
      setError(err.message || 'Erro ao processar pagamento');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const cancelRental = async (id: string): Promise<void> => {
    try {
      await apiService.cancelRental(id);
      
      // Remover das listas locais ou marcar como cancelado
      setMyRentals(prev => 
        prev.map(r => r.id === id ? { ...r, status: 'CANCELLED' } : r)
      );
    } catch (err: any) {
      setError(err.message || 'Erro ao cancelar aluguel');
      throw err;
    }
  };

  const fetchMyRentals = async (type?: 'renter' | 'owner'): Promise<void> => {
    try {
      setLoading(true);
      const response = await apiService.getMyRentals(type);
      setMyRentals(response);
    } catch (err: any) {
      setError(err.message || 'Erro ao carregar aluguéis');
    } finally {
      setLoading(false);
    }
  };

  const fetchRentalHistory = async (): Promise<void> => {
    try {
      const response = await apiService.getRentalHistory();
      setRentalHistory(response);
    } catch (err: any) {
      setError(err.message || 'Erro ao carregar histórico');
    }
  };

  const fetchRentalById = async (id: string): Promise<Rental | null> => {
    try {
      const rental = await apiService.getRentalById(id);
      return rental;
    } catch (err: any) {
      setError(err.message || 'Erro ao carregar aluguel');
      return null;
    }
  };

  const value: RentalContextType = {
    rentals,
    myRentals,
    rentalHistory,
    loading,
    error,
    createRental,
    updateRentalStatus,
    processPayment,
    cancelRental,
    fetchMyRentals,
    fetchRentalHistory,
    fetchRentalById,
  };

  return (
    <RentalContext.Provider value={value}>
      {children}
    </RentalContext.Provider>
  );
};
