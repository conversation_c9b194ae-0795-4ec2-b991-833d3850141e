import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Button } from './ui/button';
import { LogIn } from 'lucide-react';

interface ProtectedActionProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
  className?: string;
}

const ProtectedAction: React.FC<ProtectedActionProps> = ({
  children,
  fallback,
  requireAuth = true,
  redirectTo = '/auth',
  className = '',
}) => {
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();

  if (!requireAuth || isAuthenticated) {
    return <>{children}</>;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  // Fallback padrão - botão para fazer login
  return (
    <Button
      variant="outline"
      className={`flex items-center space-x-2 ${className}`}
      onClick={() => navigate(redirectTo)}
    >
      <LogIn className="w-4 h-4" />
      <span>Entrar para continuar</span>
    </Button>
  );
};

export default ProtectedAction;
