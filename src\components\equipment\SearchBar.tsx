import React from 'react';
import { Search, MapPin } from 'lucide-react';
import { Button } from '../ui/button';

interface SearchBarProps {
  isDarkMode: boolean;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  startDate: string;
  setStartDate: (date: string) => void;
  endDate: string;
  setEndDate: (date: string) => void;
  showMobileFilters: boolean;
  setShowMobileFilters: (show: boolean) => void;
  handleStartDateChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleEndDateChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  today: string;
}

/**
 * Componente de barra de pesquisa para a página de catálogo de equipamentos
 * Inclui campos para pesquisa, localização, datas e botões de filtro
 */
const SearchBar: React.FC<SearchBarProps> = ({
  isDarkMode,
  searchTerm,
  setSearchTerm,
  startDate,
  endDate,
  showMobileFilters,
  setShowMobileFilters,
  handleStartDateChange,
  handleEndDateChange,
  today
}) => {
  return (
    <div className={`w-full ${isDarkMode ? 'bg-gray-800' : 'bg-gray-100'} py-4`}>
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-12 gap-3">
          {/* Campo de pesquisa */}
          <div className="col-span-1 sm:col-span-1 lg:col-span-3">
            <div className={`flex items-center rounded-md overflow-hidden shadow-sm ${
              isDarkMode ? 'bg-gray-700' : 'bg-white'
            }`}>
              <div className={`p-3 border-r ${
                isDarkMode ? 'bg-gray-600 border-gray-500' : 'bg-gray-50 border-gray-200'
              }`}>
                <Search className={`w-5 h-5 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
              </div>
              <input
                type="text"
                placeholder="Pesquisar equipamentos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full p-3 outline-none ${
                  isDarkMode
                    ? 'bg-gray-700 text-white placeholder-gray-400'
                    : 'bg-white text-gray-900 placeholder-gray-500'
                }`}
              />
            </div>
          </div>

          {/* Campo de localização */}
          <div className="col-span-1 sm:col-span-1 lg:col-span-3">
            <div className={`flex items-center rounded-md overflow-hidden shadow-sm ${
              isDarkMode ? 'bg-gray-700' : 'bg-white'
            }`}>
              <div className={`p-3 border-r ${
                isDarkMode ? 'bg-gray-600 border-gray-500' : 'bg-gray-50 border-gray-200'
              }`}>
                <MapPin className={`w-5 h-5 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
              </div>
              <input
                type="text"
                placeholder="Localização"
                className={`w-full p-3 outline-none ${
                  isDarkMode
                    ? 'bg-gray-700 text-white placeholder-gray-400'
                    : 'bg-white text-gray-900 placeholder-gray-500'
                }`}
              />
            </div>
          </div>

          {/* Datas */}
          <div className="col-span-1 sm:col-span-1 lg:col-span-2">
            <div className={`flex items-center rounded-md overflow-hidden shadow-sm ${
              isDarkMode ? 'bg-gray-700' : 'bg-white'
            }`}>
              <div className={`p-3 border-r ${
                isDarkMode ? 'bg-gray-600 border-gray-500' : 'bg-gray-50 border-gray-200'
              }`}>
                <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Início</span>
              </div>
              <input
                type="date"
                min={today}
                value={startDate}
                onChange={handleStartDateChange}
                className={`w-full p-3 outline-none ${
                  isDarkMode
                    ? 'bg-gray-700 text-white'
                    : 'bg-white text-gray-900'
                }`}
              />
            </div>
          </div>
          <div className="col-span-1 sm:col-span-1 lg:col-span-2">
            <div className={`flex items-center rounded-md overflow-hidden shadow-sm ${
              isDarkMode ? 'bg-gray-700' : 'bg-white'
            }`}>
              <div className={`p-3 border-r ${
                isDarkMode ? 'bg-gray-600 border-gray-500' : 'bg-gray-50 border-gray-200'
              }`}>
                <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Fim</span>
              </div>
              <input
                type="date"
                min={startDate || today}
                value={endDate}
                onChange={handleEndDateChange}
                className={`w-full p-3 outline-none ${
                  isDarkMode
                    ? 'bg-gray-700 text-white'
                    : 'bg-white text-gray-900'
                }`}
              />
            </div>
          </div>

          {/* Entrega */}
          <div className="col-span-1 sm:col-span-1 lg:col-span-1">
            <div className={`h-full items-center rounded-md overflow-hidden shadow-sm ${
              isDarkMode ? 'bg-gray-700' : 'bg-white'
            }`}>
              <label className="flex items-center h-full p-3 cursor-pointer">
                <input type="checkbox" className="mr-2" />
                <span className={`text-sm ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Entrega</span>
              </label>
            </div>
          </div>

          {/* Botões aplicar e filtros */}
          <div className="col-span-1 sm:col-span-1 lg:col-span-1 flex gap-2">
            <Button
              variant={showMobileFilters ? "default" : "outline"}
              onClick={() => setShowMobileFilters(!showMobileFilters)}
              className="md:hidden h-full w-10 flex items-center justify-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
              </svg>
            </Button>
            <Button
              className="w-full h-full text-white px-6 py-3"
              style={{backgroundColor: '#3569b0'}}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2a5490'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3569b0'}
            >
              Aplicar
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchBar;
