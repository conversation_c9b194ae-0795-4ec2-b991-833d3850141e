import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import apiService from '../services/api';
import { CheckCircle, XCircle, Eye, Building, Calendar, Phone, Mail, FileText } from 'lucide-react';

interface PendingLandlord {
  id: string;
  email: string;
  fullName: string;
  phoneNumber: string;
  companyName: string;
  companyType: string;
  companyAddress: string;
  nif: string;
  companyDocuments: string[];
  createdAt: string;
}

interface LandlordValidationProps {
  isDarkMode?: boolean;
}

const LandlordValidation: React.FC<LandlordValidationProps> = ({ isDarkMode = false }) => {
  const { user } = useAuth();
  const [pendingLandlords, setPendingLandlords] = useState<PendingLandlord[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedLandlord, setSelectedLandlord] = useState<PendingLandlord | null>(null);
  const [validationReason, setValidationReason] = useState('');
  const [isValidating, setIsValidating] = useState(false);

  useEffect(() => {
    loadPendingLandlords();
  }, []);

  const loadPendingLandlords = async () => {
    try {
      setLoading(true);
      const data = await apiService.getPendingLandlords();
      setPendingLandlords(data);
    } catch (error) {
      console.error('Erro ao carregar locadores pendentes:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleValidation = async (landlordId: string, approved: boolean) => {
    try {
      setIsValidating(true);
      await apiService.validateLandlord(landlordId, approved, approved ? undefined : validationReason);
      
      // Remover da lista
      setPendingLandlords(prev => prev.filter(l => l.id !== landlordId));
      setSelectedLandlord(null);
      setValidationReason('');
      
      alert(approved ? 'Locador aprovado com sucesso!' : 'Locador rejeitado com sucesso!');
    } catch (error) {
      console.error('Erro na validação:', error);
      alert('Erro ao processar validação');
    } finally {
      setIsValidating(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Validação de Locadores</h1>
          <p className="text-gray-600">
            Gerencie as solicitações de registro de locadores pendentes
          </p>
        </div>

        {pendingLandlords.length === 0 ? (
          <div className="text-center py-12">
            <Building className="w-16 h-16 mx-auto mb-4 text-gray-400" />
            <h3 className="text-xl font-semibold mb-2">Nenhum locador pendente</h3>
            <p className="text-gray-600">Todas as solicitações foram processadas.</p>
          </div>
        ) : (
          <div className="grid gap-6">
            {pendingLandlords.map((landlord) => (
              <div
                key={landlord.id}
                className={`${
                  isDarkMode ? 'bg-gray-800' : 'bg-white'
                } rounded-lg shadow-md p-6 border ${
                  selectedLandlord?.id === landlord.id ? 'border-blue-500' : 'border-gray-200'
                }`}
              >
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-xl font-semibold">{landlord.fullName}</h3>
                    <p className="text-gray-600">{landlord.companyName}</p>
                  </div>
                  <button
                    onClick={() => setSelectedLandlord(
                      selectedLandlord?.id === landlord.id ? null : landlord
                    )}
                    className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    <Eye className="w-4 h-4" />
                    <span>{selectedLandlord?.id === landlord.id ? 'Ocultar' : 'Ver Detalhes'}</span>
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div className="flex items-center space-x-2">
                    <Mail className="w-4 h-4 text-gray-400" />
                    <span>{landlord.email}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Phone className="w-4 h-4 text-gray-400" />
                    <span>{landlord.phoneNumber}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Building className="w-4 h-4 text-gray-400" />
                    <span>{landlord.companyType}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <span>{new Date(landlord.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>

                {selectedLandlord?.id === landlord.id && (
                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <div className="grid gap-4 mb-6">
                      <div>
                        <label className="block text-sm font-medium mb-2">Endereço da Empresa</label>
                        <p className="text-gray-600">{landlord.companyAddress}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">NIF</label>
                        <p className="text-gray-600">{landlord.nif}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">Documentos da Empresa</label>
                        <div className="flex flex-wrap gap-2">
                          {landlord.companyDocuments.map((doc, index) => (
                            <a
                              key={index}
                              href={doc}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center space-x-2 px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200"
                            >
                              <FileText className="w-4 h-4" />
                              <span>Documento {index + 1}</span>
                            </a>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="mb-4">
                      <label className="block text-sm font-medium mb-2">
                        Motivo da Rejeição (opcional)
                      </label>
                      <textarea
                        value={validationReason}
                        onChange={(e) => setValidationReason(e.target.value)}
                        placeholder="Digite o motivo caso vá rejeitar..."
                        className={`w-full px-3 py-2 border rounded-lg ${
                          isDarkMode 
                            ? 'bg-gray-700 border-gray-600 text-white' 
                            : 'bg-white border-gray-300 text-gray-900'
                        }`}
                        rows={3}
                      />
                    </div>

                    <div className="flex space-x-4">
                      <button
                        onClick={() => handleValidation(landlord.id, true)}
                        disabled={isValidating}
                        className="flex items-center space-x-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                      >
                        <CheckCircle className="w-5 h-5" />
                        <span>Aprovar</span>
                      </button>
                      <button
                        onClick={() => handleValidation(landlord.id, false)}
                        disabled={isValidating || !validationReason.trim()}
                        className="flex items-center space-x-2 px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
                      >
                        <XCircle className="w-5 h-5" />
                        <span>Rejeitar</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default LandlordValidation;
