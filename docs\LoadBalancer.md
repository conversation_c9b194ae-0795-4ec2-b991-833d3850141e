# Sistema de Load Balance - YMRentals

## Visão Geral

O sistema de Load Balance da YMRentals é projetado para garantir alta disponibilidade, performance otimizada e distribuição eficiente de carga entre os servidores da aplicação.

## Arquitetura

### Componentes Principais

1. **Load Balancer Principal (NGINX)**
   - Distribuição de tráfego HTTP/HTTPS
   - Terminação SSL/TLS
   - Cache de conteúdo estático
   - Rate limiting e proteção DDoS

2. **Application Servers**
   - Múltiplas instâncias da aplicação React/Node.js
   - Auto-scaling baseado em métricas
   - Health checks automáticos

3. **Database Load Balancer**
   - Distribuição entre read replicas
   - Failover automático para master
   - Connection pooling

4. **CDN (Content Delivery Network)**
   - Distribuição global de assets
   - Cache de imagens e arquivos estáticos
   - Redução de latência

## Configuração do NGINX

```nginx
upstream ymrentals_backend {
    least_conn;
    server app1.ymrentals.com:3000 weight=3 max_fails=3 fail_timeout=30s;
    server app2.ymrentals.com:3000 weight=3 max_fails=3 fail_timeout=30s;
    server app3.ymrentals.com:3000 weight=2 max_fails=3 fail_timeout=30s;
    server app4.ymrentals.com:3000 backup;
}

server {
    listen 80;
    listen 443 ssl http2;
    server_name ymrentals.com www.ymrentals.com;
    
    # SSL Configuration
    ssl_certificate /etc/ssl/certs/ymrentals.crt;
    ssl_certificate_key /etc/ssl/private/ymrentals.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
    
    # Static Files
    location /static/ {
        alias /var/www/ymrentals/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        gzip_static on;
    }
    
    # API Endpoints
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://ymrentals_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Health Check
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
        proxy_connect_timeout 5s;
        proxy_send_timeout 10s;
        proxy_read_timeout 10s;
    }
    
    # Authentication Endpoints
    location /auth/ {
        limit_req zone=login burst=5 nodelay;
        proxy_pass http://ymrentals_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket Support
    location /ws/ {
        proxy_pass http://ymrentals_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Default Location
    location / {
        proxy_pass http://ymrentals_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Algoritmos de Balanceamento

### 1. Least Connections (Padrão)
- Direciona requisições para o servidor com menos conexões ativas
- Ideal para aplicações com tempos de resposta variáveis

### 2. Round Robin
- Distribui requisições sequencialmente entre servidores
- Simples e eficaz para cargas uniformes

### 3. IP Hash
- Direciona usuários para o mesmo servidor baseado no IP
- Mantém sessões sticky quando necessário

### 4. Weighted Round Robin
- Permite atribuir pesos diferentes aos servidores
- Útil quando servidores têm capacidades diferentes

## Health Checks

### Configuração de Health Checks

```javascript
// health-check.js
const express = require('express');
const app = express();

app.get('/health', (req, res) => {
  const healthCheck = {
    uptime: process.uptime(),
    message: 'OK',
    timestamp: Date.now(),
    checks: {
      database: checkDatabase(),
      redis: checkRedis(),
      memory: checkMemory(),
      disk: checkDisk()
    }
  };
  
  try {
    res.status(200).json(healthCheck);
  } catch (error) {
    healthCheck.message = error;
    res.status(503).json(healthCheck);
  }
});

function checkDatabase() {
  // Verificar conexão com banco de dados
  return { status: 'healthy', latency: '5ms' };
}

function checkRedis() {
  // Verificar conexão com Redis
  return { status: 'healthy', latency: '2ms' };
}

function checkMemory() {
  const used = process.memoryUsage();
  return {
    status: used.heapUsed < ********** ? 'healthy' : 'warning',
    heapUsed: `${Math.round(used.heapUsed / 1024 / 1024)} MB`,
    heapTotal: `${Math.round(used.heapTotal / 1024 / 1024)} MB`
  };
}

function checkDisk() {
  // Verificar espaço em disco
  return { status: 'healthy', usage: '45%' };
}
```

## Auto Scaling

### Métricas de Scaling

1. **CPU Utilization**
   - Scale up: > 70% por 5 minutos
   - Scale down: < 30% por 10 minutos

2. **Memory Usage**
   - Scale up: > 80% por 3 minutos
   - Scale down: < 40% por 15 minutos

3. **Request Rate**
   - Scale up: > 1000 req/min por servidor
   - Scale down: < 200 req/min por servidor

4. **Response Time**
   - Scale up: > 500ms média por 2 minutos
   - Scale down: < 100ms média por 20 minutos

### Configuração Docker Swarm

```yaml
version: '3.8'
services:
  ymrentals-app:
    image: ymrentals/app:latest
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        delay: 10s
        order: start-first
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

## Monitoramento

### Métricas Importantes

1. **Performance**
   - Response time médio
   - Throughput (requests/second)
   - Error rate
   - Uptime

2. **Recursos**
   - CPU usage por servidor
   - Memory usage
   - Disk I/O
   - Network bandwidth

3. **Aplicação**
   - Active connections
   - Queue length
   - Database connections
   - Cache hit rate

### Alertas

```yaml
# prometheus-alerts.yml
groups:
  - name: ymrentals-load-balancer
    rules:
      - alert: HighErrorRate
        expr: rate(nginx_http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          
      - alert: HighResponseTime
        expr: nginx_http_request_duration_seconds{quantile="0.95"} > 1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          
      - alert: ServerDown
        expr: up{job="ymrentals-app"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Server instance is down"
```

## Disaster Recovery

### Estratégias de Failover

1. **Active-Passive**
   - Servidor principal ativo
   - Backup em standby
   - Failover automático em caso de falha

2. **Active-Active**
   - Múltiplos servidores ativos
   - Distribuição de carga
   - Redundância completa

3. **Multi-Region**
   - Deployment em múltiplas regiões
   - DNS failover geográfico
   - Replicação de dados cross-region

### Backup e Restore

```bash
#!/bin/bash
# backup-script.sh

# Backup do banco de dados
pg_dump ymrentals_db > /backups/db_$(date +%Y%m%d_%H%M%S).sql

# Backup de arquivos estáticos
tar -czf /backups/static_$(date +%Y%m%d_%H%M%S).tar.gz /var/www/ymrentals/static/

# Sync para storage remoto
aws s3 sync /backups/ s3://ymrentals-backups/

# Limpeza de backups antigos (manter 30 dias)
find /backups/ -name "*.sql" -mtime +30 -delete
find /backups/ -name "*.tar.gz" -mtime +30 -delete
```

## Segurança

### Proteção DDoS

1. **Rate Limiting**
   - Limite por IP
   - Limite por endpoint
   - Burst protection

2. **Geo-blocking**
   - Bloqueio de países específicos
   - Whitelist de IPs confiáveis

3. **WAF (Web Application Firewall)**
   - Filtros de SQL injection
   - XSS protection
   - Bot detection

### SSL/TLS

```nginx
# SSL Configuration
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
ssl_prefer_server_ciphers off;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
ssl_stapling on;
ssl_stapling_verify on;
```

## Otimizações

### Cache Strategy

1. **Browser Cache**
   - Assets estáticos: 1 ano
   - HTML: sem cache
   - API responses: cache condicional

2. **CDN Cache**
   - Imagens: cache agressivo
   - CSS/JS: cache com versioning
   - API: cache seletivo

3. **Application Cache**
   - Redis para sessões
   - Memcached para queries
   - Database query cache

### Compressão

```nginx
# Gzip Configuration
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/javascript
    application/xml+rss
    application/json;
```

## Implementação

### Fase 1: Setup Básico
- [ ] Configurar NGINX como load balancer
- [ ] Implementar health checks
- [ ] Configurar SSL/TLS
- [ ] Setup básico de monitoramento

### Fase 2: Auto Scaling
- [ ] Implementar auto scaling
- [ ] Configurar métricas de scaling
- [ ] Setup de alertas
- [ ] Testes de carga

### Fase 3: Otimizações
- [ ] Implementar CDN
- [ ] Otimizar cache strategies
- [ ] Fine-tuning de performance
- [ ] Disaster recovery testing

### Fase 4: Segurança Avançada
- [ ] Implementar WAF
- [ ] Configurar DDoS protection
- [ ] Security hardening
- [ ] Penetration testing

## Custos Estimados (Mensal)

- **Load Balancer**: $50-100
- **Application Servers**: $200-500
- **CDN**: $30-80
- **Monitoring**: $20-50
- **Backup Storage**: $10-30

**Total Estimado**: $310-760/mês

## Conclusão

O sistema de Load Balance proposto garante alta disponibilidade, performance otimizada e escalabilidade para a plataforma YMRentals, suportando o crescimento futuro da aplicação com custos controlados e máxima confiabilidade.
