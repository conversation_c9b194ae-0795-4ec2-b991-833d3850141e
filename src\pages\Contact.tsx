import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  Phone,
  Mail,
  MapPin,
  Clock,
  MessageCircle,
  Send,
  Facebook,
  Instagram,
  Linkedin,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { Button } from '../components/ui/button';
import apiService from '../services/api';

interface ContactProps {
  isDarkMode: boolean;
}

const Contact: React.FC<ContactProps> = ({ isDarkMode }) => {
  const navigate = useNavigate();
  const [content, setContent] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    type: 'support'
  });
  const [isSubmitted, setIsSubmitted] = useState(false);

  useEffect(() => {
    const loadContent = async () => {
      try {
        setLoading(true);
        const response = await apiService.getContactContent();
        setContent(response);
      } catch (error) {
        console.error('Erro ao carregar conteúdo:', error);
        // Usar conteúdo padrão em caso de erro
        setContent({
          title: 'Entre em Contato',
          content: 'Estamos aqui para ajudar! Entre em contato conosco através de qualquer um dos canais abaixo.'
        });
      } finally {
        setLoading(false);
      }
    };

    loadContent();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Simular envio
    setIsSubmitted(true);
    setTimeout(() => {
      setIsSubmitted(false);
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: '',
        type: 'support'
      });
    }, 3000);
  };

  const contactInfo = [
    {
      icon: Phone,
      title: 'Telefone',
      details: ['+244 923 456 789', '+244 912 345 678'],
      description: 'Suporte 24/7 disponível'
    },
    {
      icon: Mail,
      title: 'Email',
      details: ['<EMAIL>', '<EMAIL>'],
      description: 'Resposta em até 24 horas'
    },
    {
      icon: MapPin,
      title: 'Endereço',
      details: ['Rua Comandante Che Guevara, 123', 'Talatona, Luanda - Angola'],
      description: 'Sede principal'
    },
    {
      icon: Clock,
      title: 'Horário',
      details: ['Segunda - Sexta: 8h às 18h', 'Sábado: 8h às 14h'],
      description: 'Suporte online 24/7'
    }
  ];

  const socialLinks = [
    { icon: Facebook, name: 'Facebook', url: '#', color: '#1877F2' },
    { icon: Instagram, name: 'Instagram', url: '#', color: '#E4405F' },
    { icon: Linkedin, name: 'LinkedIn', url: '#', color: '#0A66C2' }
  ];

  return (
    <div className="container mx-auto px-4 py-8 pt-24">
      {/* Botão de Voltar */}
      <div className="mb-6">
        <Button
          variant="outline"
          onClick={() => navigate(-1)}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Voltar</span>
        </Button>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center items-center py-20">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
          <span className={`ml-2 ${isDarkMode ? 'text-white' : 'text-gray-700'}`}>
            Carregando informações de contato...
          </span>
        </div>
      )}

      {/* Header */}
      {!loading && content && (
        <div className="text-center mb-12">
          <h1 className={`text-3xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            {content.title || 'Entre em Contato'}
          </h1>
          <div
            className={`text-lg max-w-2xl mx-auto ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}
            dangerouslySetInnerHTML={{ __html: content.content || 'Informações de contato não disponíveis.' }}
          />
        </div>
      )}

      {!loading && (

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Informações de Contato */}
        <div>
          <h2 className={`text-2xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Informações de Contato
          </h2>

          <div className="space-y-6 mb-8">
            {contactInfo.map((info, index) => {
              const IconComponent = info.icon;
              return (
                <div key={index} className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0" style={{backgroundColor: '#3569b0'}}>
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className={`font-semibold mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {info.title}
                    </h3>
                    {info.details.map((detail, idx) => (
                      <p key={idx} className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                        {detail}
                      </p>
                    ))}
                    <p className={`text-sm mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      {info.description}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Redes Sociais */}
          <div>
            <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Siga-nos nas Redes Sociais
            </h3>
            <div className="flex space-x-4">
              {socialLinks.map((social, index) => {
                const IconComponent = social.icon;
                return (
                  <a
                    key={index}
                    href={social.url}
                    className={`w-10 h-10 rounded-lg flex items-center justify-center transition-colors ${
                      isDarkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-100 hover:bg-gray-200'
                    }`}
                    style={{ backgroundColor: social.color }}
                  >
                    <IconComponent className="w-5 h-5 text-white" />
                  </a>
                );
              })}
            </div>
          </div>

          {/* Mapa (Placeholder) */}
          <div className="mt-8">
            <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Nossa Localização
            </h3>
            <div className={`w-full h-64 rounded-lg flex items-center justify-center ${
              isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
            }`}>
              <div className="text-center">
                <MapPin className="w-12 h-12 mx-auto mb-2 text-gray-400" />
                <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Mapa interativo em breve
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Formulário de Contato */}
        <div>
          <div className={`p-8 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
            <h2 className={`text-2xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Envie uma Mensagem
            </h2>

            {isSubmitted ? (
              <div className="text-center py-8">
                <CheckCircle className="w-16 h-16 mx-auto mb-4 text-green-500" />
                <h3 className={`text-xl font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Mensagem Enviada!
                </h3>
                <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  Obrigado pelo contato. Responderemos em breve.
                </p>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Nome *
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                        isDarkMode 
                          ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:ring-blue-500' 
                          : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:ring-blue-500'
                      }`}
                      placeholder="Seu nome completo"
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Email *
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                        isDarkMode 
                          ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:ring-blue-500' 
                          : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:ring-blue-500'
                      }`}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Telefone
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                        isDarkMode 
                          ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:ring-blue-500' 
                          : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:ring-blue-500'
                      }`}
                      placeholder="+244 923 456 789"
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Tipo de Contato
                    </label>
                    <select
                      name="type"
                      value={formData.type}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                        isDarkMode 
                          ? 'bg-gray-700 border-gray-600 text-white focus:ring-blue-500' 
                          : 'bg-white border-gray-300 text-gray-900 focus:ring-blue-500'
                      }`}
                    >
                      <option value="support">Suporte Técnico</option>
                      <option value="commercial">Comercial</option>
                      <option value="partnership">Parceria</option>
                      <option value="complaint">Reclamação</option>
                      <option value="suggestion">Sugestão</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Assunto *
                  </label>
                  <input
                    type="text"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    required
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                      isDarkMode 
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:ring-blue-500' 
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:ring-blue-500'
                    }`}
                    placeholder="Resumo do seu contato"
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Mensagem *
                  </label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={5}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                      isDarkMode 
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:ring-blue-500' 
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:ring-blue-500'
                    }`}
                    placeholder="Descreva sua dúvida ou solicitação em detalhes..."
                  />
                </div>

                <Button 
                  type="submit"
                  className="w-full text-white"
                  style={{backgroundColor: '#3569b0'}}
                  onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2a5490'}
                  onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3569b0'}
                >
                  <Send className="w-4 h-4 mr-2" />
                  Enviar Mensagem
                </Button>
              </form>
            )}
          </div>

          {/* Contato Rápido */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button 
              variant="outline" 
              className="flex items-center justify-center space-x-2"
            >
              <Phone className="w-4 h-4" />
              <span>Ligar Agora</span>
            </Button>
            <Button 
              variant="outline"
              className="flex items-center justify-center space-x-2"
            >
              <MessageCircle className="w-4 h-4" />
              <span>Chat Online</span>
            </Button>
          </div>
        </div>
      </div>
      )}
    </div>
  );
};

export default Contact;
