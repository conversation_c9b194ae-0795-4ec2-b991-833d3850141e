import React from 'react';
import { MapPin, List, Grid as GridIcon } from 'lucide-react';
import { Button } from './ui/button';

interface EquipmentListingOptionsProps {
  isDarkMode: boolean;
  viewMode: 'map' | 'list' | 'grid';
  onViewModeChange: (mode: 'map' | 'list' | 'grid') => void;
}

const EquipmentListingOptions: React.FC<EquipmentListingOptionsProps> = ({
  isDarkMode,
  viewMode,
  onViewModeChange
}) => {
  return (
    <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-2 mb-3`}>
      <h3 className="font-medium text-sm mb-2">Visualizar como:</h3>

      <div className="grid grid-cols-3 gap-2">
        {/* Opção Mapa */}
        <Button
          variant={viewMode === 'map' ? 'default' : 'outline'}
          size="sm"
          onClick={() => onViewModeChange('map')}
          className="flex flex-col items-center justify-center h-16 py-1"
        >
          <MapPin className="w-5 h-5 mb-1" />
          <span className="text-xs">Mapa</span>
        </Button>

        {/* Opção Lista */}
        <Button
          variant={viewMode === 'list' ? 'default' : 'outline'}
          size="sm"
          onClick={() => onViewModeChange('list')}
          className="flex flex-col items-center justify-center h-16 py-1"
        >
          <List className="w-5 h-5 mb-1" />
          <span className="text-xs">Lista</span>
        </Button>

        {/* Opção Grade */}
        <Button
          variant={viewMode === 'grid' ? 'default' : 'outline'}
          size="sm"
          onClick={() => onViewModeChange('grid')}
          className="flex flex-col items-center justify-center h-16 py-1"
        >
          <GridIcon className="w-5 h-5 mb-1" />
          <span className="text-xs">Grade</span>
        </Button>
      </div>
    </div>
  );
};

export default EquipmentListingOptions;
