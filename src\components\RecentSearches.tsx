import React, { useState, useEffect } from 'react';
import { Clock, X, Search } from 'lucide-react';
import { Button } from './ui/button';

interface RecentSearch {
  id: string;
  searchTerm: string;
  location: string;
  category: string | null;
  timestamp: string;
}

interface RecentSearchesProps {
  isDarkMode: boolean;
  onSearchSelect: (search: RecentSearch) => void;
}

/**
 * Componente que exibe as pesquisas recentes do usuário
 * Permite reutilizar pesquisas anteriores e limpar o histórico
 */
const RecentSearches: React.FC<RecentSearchesProps> = ({ isDarkMode, onSearchSelect }) => {
  const [recentSearches, setRecentSearches] = useState<RecentSearch[]>([]);

  // Carregar pesquisas recentes do localStorage
  useEffect(() => {
    const loadRecentSearches = () => {
      const searches = JSON.parse(localStorage.getItem('recentSearches') || '[]');
      setRecentSearches(searches);
    };

    loadRecentSearches();

    // Atualizar quando o localStorage mudar (em outras abas)
    const handleStorageChange = () => {
      loadRecentSearches();
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // Função para remover uma pesquisa específica
  const removeSearch = (searchId: string) => {
    const updatedSearches = recentSearches.filter(search => search.id !== searchId);
    setRecentSearches(updatedSearches);
    localStorage.setItem('recentSearches', JSON.stringify(updatedSearches));
  };

  // Função para limpar todas as pesquisas
  const clearAllSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem('recentSearches');
  };

  // Função para formatar o texto da pesquisa
  const formatSearchText = (search: RecentSearch) => {
    const parts = [];
    if (search.searchTerm) parts.push(search.searchTerm);
    if (search.location) parts.push(`em ${search.location}`);
    if (search.category) parts.push(`categoria: ${search.category}`);
    return parts.join(' • ') || 'Pesquisa sem filtros';
  };

  // Função para formatar a data
  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Agora mesmo';
    if (diffInHours < 24) return `${diffInHours}h atrás`;
    if (diffInHours < 48) return 'Ontem';
    return date.toLocaleDateString('pt-AO');
  };

  if (recentSearches.length === 0) {
    return (
      <div>
        <h3 className={`text-sm font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Pesquisas Recentes
        </h3>
        <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
          Suas pesquisas aparecerão aqui para acesso rápido.
        </p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-2">
        <h3 className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Pesquisas Recentes
        </h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={clearAllSearches}
          className={`text-xs p-1 h-auto ${isDarkMode ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700'}`}
        >
          Limpar
        </Button>
      </div>

      <div className="space-y-1">
        {recentSearches.slice(0, 5).map((search) => (
          <div
            key={search.id}
            className={`flex items-center justify-between p-2 rounded cursor-pointer transition-colors ${
              isDarkMode
                ? 'hover:bg-gray-700'
                : 'hover:bg-gray-100'
            }`}
            onClick={() => onSearchSelect(search)}
          >
            <div className="flex items-center flex-1 min-w-0">
              <Search className={`w-3 h-3 mr-2 flex-shrink-0 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
              <div className="flex-1 min-w-0">
                <p className={`text-xs truncate ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {formatSearchText(search)}
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                removeSearch(search.id);
              }}
              className={`ml-1 p-0.5 h-auto ${isDarkMode ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700'}`}
            >
              <X className="w-3 h-3" />
            </Button>
          </div>
        ))}
      </div>

    </div>
  );
};

export default RecentSearches;
