import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from './ui/button';

interface SlideshowProps {
  slides: {
    image: string;
    title: string;
    description: string;
    buttonText?: string;
    buttonLink?: string;
  }[];
  autoPlay?: boolean;
  interval?: number;
  isDarkMode?: boolean;
}

const Slideshow: React.FC<SlideshowProps> = ({
  slides,
  autoPlay = true,
  interval = 5000,
  isDarkMode = false
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const nextSlide = () => {
    if (isTransitioning) return;

    setIsTransitioning(true);
    setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));

    setTimeout(() => {
      setIsTransitioning(false);
    }, 500);
  };

  const prevSlide = () => {
    if (isTransitioning) return;

    setIsTransitioning(true);
    setCurrentSlide((prev) => (prev === 0 ? slides.length - 1 : prev - 1));

    setTimeout(() => {
      setIsTransitioning(false);
    }, 500);
  };

  useEffect(() => {
    if (!autoPlay) return;

    const timer = setInterval(() => {
      nextSlide();
    }, interval);

    return () => clearInterval(timer);
  }, [autoPlay, interval, currentSlide, isTransitioning]);

  return (
    <div className="relative w-full overflow-hidden">
      {/* Slides */}
      <div className="relative h-[500px] md:h-[600px] pointer-events-none">
        <div className="absolute inset-0 pointer-events-auto">
        {slides.map((slide, index) => (
          <div
            key={index}
            className={`absolute top-0 left-0 w-full h-full transition-all duration-700 ease-in-out ${
              index === currentSlide
                ? 'opacity-100 z-10 scale-100'
                : 'opacity-0 z-0 scale-105'
            }`}
            style={{
              backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.7)), url(${slide.image})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          >
            <div className="container mx-auto px-4 h-full flex flex-col justify-center">
              <div className="max-w-3xl">
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 animate-slideUp">
                  {slide.title}
                </h1>
                <p className="text-xl text-white/90 mb-2 animate-slideUp animation-delay-200">
                  {slide.description}
                </p>
                {slide.buttonText && slide.buttonLink && (
                  <div className="animate-fadeIn animation-delay-300 relative z-20">
                    <Button
                      size="lg"
                      className="text-white shadow-lg transition-all hover:translate-y-[-2px] cursor-pointer"
                      style={{backgroundColor: '#3569b0'}}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2a5490'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3569b0'}
                      onClick={() => window.location.href = slide.buttonLink || '#'}
                    >
                      {slide.buttonText}
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
        </div>
      </div>

      {/* Navigation Arrows */}
      <Button
        variant="ghost"
        className="absolute left-4 top-1/2 transform -translate-y-1/2 z-30 text-white rounded-full p-2 opacity-70 hover:opacity-100 transition-all duration-300 shadow-lg pointer-events-auto"
        style={{'--hover-bg': '#3569b0'} as any}
        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#3569b0'}
        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
        onClick={prevSlide}
      >
        <ChevronLeft className="w-8 h-8" />
      </Button>
      <Button
        variant="ghost"
        className="absolute right-4 top-1/2 transform -translate-y-1/2 z-30 text-white rounded-full p-2 opacity-70 hover:opacity-100 transition-all duration-300 shadow-lg pointer-events-auto"
        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#3569b0'}
        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
        onClick={nextSlide}
      >
        <ChevronRight className="w-8 h-8" />
      </Button>

      {/* Indicators */}
      <div className="absolute bottom-6 left-0 right-0 z-30 flex justify-center space-x-2 pointer-events-auto">
        {slides.map((_, index) => (
          <button
            key={index}
            className={`h-2 rounded-full transition-all duration-300 cursor-pointer pointer-events-auto ${
              index === currentSlide ? 'w-10' : 'bg-white/50 w-3 hover:bg-white/80'
            }`}
            style={index === currentSlide ? {backgroundColor: '#3569b0'} : {}}
            onClick={() => setCurrentSlide(index)}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>

      {/* Overlay gradient at bottom for better readability */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/50 to-transparent z-10"></div>
    </div>
  );
};

export default Slideshow;
