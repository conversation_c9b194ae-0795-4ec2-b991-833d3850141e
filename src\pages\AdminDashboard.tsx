import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Users,
  Package,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  DollarSign,
  Star,
  AlertTriangle,
  ArrowLeft,
  Eye,
  Filter,
  Download,
  RefreshCw,
  Settings,
  Bell,
  Activity,
  Truck,
  Shield,
  Calendar,
  MapPin,
  MoreVertical,
  Search,
  UserCheck,
  UserX,
  Wrench,
  CreditCard
} from 'lucide-react';
import { Button } from '../components/ui/button';

interface AdminDashboardProps {
  isDarkMode: boolean;
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({ isDarkMode }) => {
  const navigate = useNavigate();
  const [selectedPeriod, setSelectedPeriod] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [activeTab, setActiveTab] = useState<'overview' | 'users' | 'equipment' | 'finance' | 'analytics'>('overview');

  // Dados mockados para demonstração
  const kpiData = [
    { title: 'Usuários Ativos', value: '2,847', change: '+12%', icon: Users, color: '#3569b0' },
    { title: 'Equipamentos', value: '1,234', change: '+8%', icon: Package, color: '#10B981' },
    { title: 'Receita Mensal', value: '45.2M Kz', change: '+23%', icon: DollarSign, color: '#F59E0B' },
    { title: 'Aluguéis Ativos', value: '156', change: '+15%', icon: Activity, color: '#EF4444' }
  ];

  const revenueData = [
    { month: 'Jan', revenue: 12000000, rentals: 45 },
    { month: 'Fev', revenue: 15000000, rentals: 52 },
    { month: 'Mar', revenue: 18000000, rentals: 61 },
    { month: 'Abr', revenue: 22000000, rentals: 73 },
    { month: 'Mai', revenue: 28000000, rentals: 89 },
    { month: 'Jun', revenue: 35000000, rentals: 102 },
    { month: 'Jul', revenue: 42000000, rentals: 118 },
    { month: 'Ago', revenue: 38000000, rentals: 95 },
    { month: 'Set', revenue: 45000000, rentals: 134 },
    { month: 'Out', revenue: 52000000, rentals: 156 },
    { month: 'Nov', revenue: 48000000, rentals: 142 },
    { month: 'Dez', revenue: 55000000, rentals: 167 }
  ];

  const equipmentData = [
    { name: 'Retroescavadeiras', value: 35, color: '#3569b0' },
    { name: 'Empilhadeiras', value: 28, color: '#10B981' },
    { name: 'Guindastes', value: 20, color: '#F59E0B' },
    { name: 'Compressores', value: 17, color: '#EF4444' }
  ];

  const recentActivities = [
    { id: 1, type: 'rental', user: 'João Silva', action: 'alugou Retroescavadeira CAT 420F2', time: '2 min atrás', status: 'success' },
    { id: 2, type: 'user', user: 'Maria Santos', action: 'se cadastrou na plataforma', time: '15 min atrás', status: 'info' },
    { id: 3, type: 'payment', user: 'Pedro Costa', action: 'pagamento de 1.250.000 Kz processado', time: '1h atrás', status: 'success' },
    { id: 4, type: 'equipment', user: 'Sistema', action: 'Empilhadeira Toyota precisa de manutenção', time: '2h atrás', status: 'warning' },
    { id: 5, type: 'rental', user: 'Ana Ferreira', action: 'cancelou aluguel de Compressor', time: '3h atrás', status: 'error' }
  ];

  const pendingApprovals = [
    { id: 1, type: 'equipment', title: 'Novo Guindaste Liebherr', user: 'Carlos Mendes', priority: 'high' },
    { id: 2, type: 'user', title: 'Verificação de Empresa', user: 'Construções SA', priority: 'medium' },
    { id: 3, type: 'payment', title: 'Reembolso Solicitado', user: 'João Silva', priority: 'low' },
    { id: 4, type: 'equipment', title: 'Manutenção Urgente', user: 'Sistema', priority: 'high' }
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'rental': return <Calendar className="w-4 h-4" />;
      case 'user': return <Users className="w-4 h-4" />;
      case 'payment': return <DollarSign className="w-4 h-4" />;
      case 'equipment': return <Package className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-500';
      case 'warning': return 'text-yellow-500';
      case 'error': return 'text-red-500';
      default: return 'text-blue-500';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  const tabs = [
    { key: 'overview', label: 'Visão Geral', icon: Activity },
    { key: 'users', label: 'Usuários', icon: Users },
    { key: 'equipment', label: 'Equipamentos', icon: Package },
    { key: 'finance', label: 'Financeiro', icon: DollarSign },
    { key: 'analytics', label: 'Analytics', icon: TrendingUp }
  ];

  return (
    <div className="min-h-screen p-4 pt-24 max-w-full overflow-x-hidden">
      <div className="container mx-auto max-w-7xl">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8 space-y-4 lg:space-y-0">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => navigate(-1)}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Voltar</span>
          </Button>
          <div>
            <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Painel Administrativo
            </h1>
            <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Gerencie sua plataforma YMRentals
            </p>
          </div>
        </div>

          <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value as any)}
              className={`px-3 py-2 border rounded-md w-full sm:w-auto ${
                isDarkMode
                  ? 'bg-gray-800 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              <option value="7d">Últimos 7 dias</option>
              <option value="30d">Últimos 30 dias</option>
              <option value="90d">Últimos 90 dias</option>
              <option value="1y">Último ano</option>
            </select>
            <div className="flex space-x-2 w-full sm:w-auto">
              <Button variant="outline" size="sm" className="flex-1 sm:flex-none">
                <RefreshCw className="w-4 h-4 mr-2" />
                Atualizar
              </Button>
              <Button variant="outline" size="sm" className="flex-1 sm:flex-none">
                <Download className="w-4 h-4 mr-2" />
                Exportar
              </Button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className={`border-b mb-8 overflow-x-auto ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <nav className="flex space-x-4 md:space-x-8 min-w-max">
            {tabs.map((tab) => {
              const IconComponent = tab.icon;
              return (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 whitespace-nowrap ${
                    activeTab === tab.key
                      ? 'text-white'
                      : isDarkMode
                      ? 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                  style={activeTab === tab.key ? {borderColor: '#3569b0', color: '#3569b0'} : {}}
                >
                  <IconComponent className="w-4 h-4" />
                  <span className="hidden sm:inline">{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

      {/* KPIs */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {kpiData.map((kpi, index) => {
          const IconComponent = kpi.icon;
          return (
            <div key={index} className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>{kpi.title}</p>
                  <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{kpi.value}</p>
                  <p className="text-sm text-green-600">{kpi.change}</p>
                </div>
                <div className="p-3 rounded-full" style={{backgroundColor: kpi.color}}>
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          );
        })}
      </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Charts */}
        <div className="lg:col-span-2 space-y-8">
          {/* Revenue Chart */}
          <div className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Receita e Aluguéis
              </h3>
              <Button variant="outline" size="sm">
                <Eye className="w-4 h-4 mr-2" />
                Detalhes
              </Button>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {revenueData.slice(-4).map((item, index) => (
                <div key={index} className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                  <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>{item.month}</p>
                  <p className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {(item.revenue / 1000000).toFixed(1)}M Kz
                  </p>
                  <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {item.rentals} aluguéis
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Equipment Distribution */}
          <div className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
            <h3 className={`text-lg font-semibold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Distribuição de Equipamentos
            </h3>
            <div className="space-y-4">
              {equipmentData.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{backgroundColor: item.color}}
                    />
                    <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      {item.name}
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className={`w-24 h-2 rounded-full ${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>
                      <div
                        className="h-2 rounded-full"
                        style={{
                          backgroundColor: item.color,
                          width: `${item.value}%`
                        }}
                      />
                    </div>
                    <span className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'} w-8`}>
                      {item.value}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-8">
          {/* Recent Activities */}
          <div className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Atividades Recentes
              </h3>
              <Button variant="outline" size="sm">
                <Eye className="w-4 h-4" />
              </Button>
            </div>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className={`p-1 rounded-full ${getStatusColor(activity.status)}`}>
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className={`text-sm ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      <span className="font-medium">{activity.user}</span> {activity.action}
                    </p>
                    <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      {activity.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Pending Approvals */}
          <div className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Aprovações Pendentes
              </h3>
              <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                {pendingApprovals.length}
              </span>
            </div>
            <div className="space-y-4">
              {pendingApprovals.map((item) => (
                <div key={item.id} className={`p-3 rounded-lg border ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <h4 className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {item.title}
                    </h4>
                    <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(item.priority)}`}>
                      {item.priority === 'high' ? 'Alta' : item.priority === 'medium' ? 'Média' : 'Baixa'}
                    </span>
                  </div>
                  <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {item.user}
                  </p>
                  <div className="flex space-x-2 mt-3">
                    <Button size="sm" className="flex-1 text-white" style={{backgroundColor: '#3569b0'}}>
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Aprovar
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <XCircle className="w-3 h-3 mr-1" />
                      Rejeitar
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
