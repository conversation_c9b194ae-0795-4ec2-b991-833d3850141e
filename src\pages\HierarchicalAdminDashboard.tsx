import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import AdminLayout from '../components/AdminLayout';
import HierarchicalNotifications from '../components/HierarchicalNotifications';
import LandlordValidation from '../pages/LandlordValidation';
import ModeratorBiValidation from '../pages/ModeratorBiValidation';
import ModerationDashboard from '../pages/ModerationDashboard';
import UserManagement from '../components/admin/UserManagement';
import ModeratorManagement from '../components/admin/ModeratorManagement';
import AdminProfile from '../components/admin/AdminProfile';
import AdminMessages from '../components/admin/AdminMessages';
import PaymentValidation from '../components/admin/PaymentValidation';
import CategoryManagement from '../components/admin/CategoryManagement';
import ContentManagement from '../components/admin/ContentManagement';
import ReportsAndAnalytics from '../components/admin/ReportsAndAnalytics';
import SystemConfiguration from '../components/admin/SystemConfiguration';
import adminApi from '../services/adminApi';
import {
  Users,
  Building,
  Package,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  BarChart3,
  FileText,
  Shield,
  MessageSquare,
  Settings,
  User,
  Activity
} from 'lucide-react';

interface HierarchicalAdminDashboardProps {
  isDarkMode: boolean;
}

interface DashboardStats {
  totalUsers: number;
  pendingLandlords: number;
  pendingEquipment: number;
  totalEquipment: number;
  totalRentals: number;
  monthlyRevenue: number;
  activeRentals: number;
  categories: number;
}

interface RecentActivity {
  type: string;
  title: string;
  description: string;
  timestamp: string;
  status?: string;
}

const HierarchicalAdminDashboard: React.FC<HierarchicalAdminDashboardProps> = ({ isDarkMode }) => {
  const { user } = useAuth();
  const [activeSection, setActiveSection] = useState('dashboard');
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    pendingLandlords: 0,
    pendingEquipment: 0,
    totalEquipment: 0,
    totalRentals: 0,
    monthlyRevenue: 0,
    activeRentals: 0,
    categories: 0,
  });
  const [loading, setLoading] = useState(true);
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);

  useEffect(() => {
    loadDashboardStats();
  }, []);

  const loadDashboardStats = async () => {
    try {
      setLoading(true);

      // Carregar dados do dashboard da API
      const dashboardData = await adminApi.getDashboardData();

      setStats({
        totalUsers: dashboardData.overview.totalUsers || 0,
        pendingLandlords: dashboardData.overview.pendingLandlords || 0,
        pendingEquipment: dashboardData.overview.pendingEquipment || 0,
        totalEquipment: dashboardData.overview.totalEquipment || 0,
        totalRentals: dashboardData.overview.totalRentals || 0,
        monthlyRevenue: dashboardData.overview.totalRevenue || 0,
        activeRentals: dashboardData.overview.activeRentals || 0,
        categories: 6, // Será carregado dinamicamente depois
      });

      // Carregar atividades recentes
      setRecentActivities(dashboardData.recentActivities || []);
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error);
      // Fallback para dados padrão em caso de erro
      setStats({
        totalUsers: 0,
        pendingLandlords: 0,
        pendingEquipment: 0,
        totalEquipment: 0,
        totalRentals: 0,
        monthlyRevenue: 0,
        activeRentals: 0,
        categories: 0,
      });
    } finally {
      setLoading(false);
    }
  };

  const getKPIs = () => {
    const baseKPIs = [
      {
        title: 'Equipamentos Pendentes',
        value: stats.pendingEquipment,
        icon: AlertTriangle,
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-100',
        change: '+2 hoje',
      },
      {
        title: 'Total de Equipamentos',
        value: stats.totalEquipment,
        icon: Package,
        color: 'text-green-600',
        bgColor: 'bg-green-100',
        change: '+5 esta semana',
      },
    ];

    if (user?.role === 'ADMIN') {
      return [
        {
          title: 'Total de Usuários',
          value: stats.totalUsers,
          icon: Users,
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
          change: '+12 este mês',
        },
        {
          title: 'Locadores Pendentes',
          value: stats.pendingLandlords,
          icon: Building,
          color: 'text-orange-600',
          bgColor: 'bg-orange-100',
          change: '+1 hoje',
        },
        ...baseKPIs,
        {
          title: 'Receita Mensal',
          value: `${(stats.monthlyRevenue / 1000).toFixed(0)}K Kz`,
          icon: DollarSign,
          color: 'text-purple-600',
          bgColor: 'bg-purple-100',
          change: '+15% vs mês anterior',
        },
      ];
    } else if (user?.role === 'MODERATOR_MANAGER') {
      return [
        {
          title: 'Locadores Pendentes',
          value: stats.pendingLandlords,
          icon: Building,
          color: 'text-orange-600',
          bgColor: 'bg-orange-100',
          change: '+1 hoje',
        },
        ...baseKPIs,
        {
          title: 'Categorias Ativas',
          value: stats.categories,
          icon: FileText,
          color: 'text-indigo-600',
          bgColor: 'bg-indigo-100',
          change: 'Estável',
        },
      ];
    } else {
      return [
        ...baseKPIs,
        {
          title: 'Aluguéis Ativos',
          value: stats.activeRentals,
          icon: Clock,
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          change: '+3 hoje',
        },
      ];
    }
  };

  const renderDashboardContent = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      );
    }

    const kpis = getKPIs();

    return (
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Dashboard {user?.role === 'ADMIN' ? 'Administrativo' : 
                     user?.role === 'MODERATOR_MANAGER' ? 'Gerencial' : 'de Moderação'}
          </h1>
          <p className={`mt-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Visão geral das suas responsabilidades e métricas importantes
          </p>
        </div>

        {/* KPIs */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {kpis.map((kpi, index) => {
            const IconComponent = kpi.icon;
            return (
              <div
                key={index}
                className={`p-6 rounded-lg shadow-sm ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {kpi.title}
                    </p>
                    <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {kpi.value}
                    </p>
                    <p className={`text-sm ${kpi.color}`}>
                      {kpi.change}
                    </p>
                  </div>
                  <div className={`p-3 rounded-full ${kpi.bgColor}`}>
                    <IconComponent className={`w-6 h-6 ${kpi.color}`} />
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Quick Actions */}
        <div className={`p-6 rounded-lg shadow-sm ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Ações Rápidas
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {(user?.role === 'ADMIN' || user?.role === 'MODERATOR_MANAGER') && stats.pendingLandlords > 0 && (
              <button
                onClick={() => setActiveSection('landlords')}
                className="flex items-center space-x-3 p-4 border border-orange-200 rounded-lg hover:bg-orange-50 transition-colors"
              >
                <Building className="w-5 h-5 text-orange-600" />
                <div className="text-left">
                  <p className="font-medium text-gray-900">Validar Locadores</p>
                  <p className="text-sm text-gray-600">{stats.pendingLandlords} pendentes</p>
                </div>
              </button>
            )}
            
            {stats.pendingEquipment > 0 && (
              <button
                onClick={() => setActiveSection('equipment')}
                className="flex items-center space-x-3 p-4 border border-yellow-200 rounded-lg hover:bg-yellow-50 transition-colors"
              >
                <Package className="w-5 h-5 text-yellow-600" />
                <div className="text-left">
                  <p className="font-medium text-gray-900">Moderar Equipamentos</p>
                  <p className="text-sm text-gray-600">{stats.pendingEquipment} pendentes</p>
                </div>
              </button>
            )}

            <button
              onClick={() => setActiveSection('notifications')}
              className="flex items-center space-x-3 p-4 border border-blue-200 rounded-lg hover:bg-blue-50 transition-colors"
            >
              <MessageSquare className="w-5 h-5 text-blue-600" />
              <div className="text-left">
                <p className="font-medium text-gray-900">Ver Notificações</p>
                <p className="text-sm text-gray-600">Central de notificações</p>
              </div>
            </button>
          </div>
        </div>

        {/* Recent Activity */}
        <div className={`p-6 rounded-lg shadow-sm ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Atividade Recente
          </h3>
          <div className="space-y-3">
            {recentActivities.length > 0 ? (
              recentActivities.map((activity, index) => {
                const getActivityIcon = (type: string) => {
                  switch (type) {
                    case 'user_registered':
                      return Users;
                    case 'equipment_created':
                      return Package;
                    case 'equipment_approved':
                      return CheckCircle;
                    case 'equipment_rejected':
                      return XCircle;
                    case 'landlord_approved':
                      return Building;
                    default:
                      return AlertTriangle;
                  }
                };

                const getActivityColor = (type: string, status?: string) => {
                  if (status === 'APPROVED') return 'text-green-600';
                  if (status === 'REJECTED') return 'text-red-600';
                  if (status === 'PENDING') return 'text-yellow-600';

                  switch (type) {
                    case 'user_registered':
                      return 'text-blue-600';
                    case 'equipment_created':
                      return 'text-purple-600';
                    default:
                      return 'text-gray-600';
                  }
                };

                const getBgColor = (type: string, status?: string) => {
                  if (status === 'APPROVED') return 'bg-green-50';
                  if (status === 'REJECTED') return 'bg-red-50';
                  if (status === 'PENDING') return 'bg-yellow-50';

                  switch (type) {
                    case 'user_registered':
                      return 'bg-blue-50';
                    case 'equipment_created':
                      return 'bg-purple-50';
                    default:
                      return 'bg-gray-50';
                  }
                };

                const IconComponent = getActivityIcon(activity.type);
                const iconColor = getActivityColor(activity.type, activity.status);
                const bgColor = getBgColor(activity.type, activity.status);

                const formatTimestamp = (timestamp: string) => {
                  const date = new Date(timestamp);
                  const now = new Date();
                  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

                  if (diffInHours < 1) return 'Há poucos minutos';
                  if (diffInHours < 24) return `Há ${diffInHours} hora${diffInHours > 1 ? 's' : ''}`;

                  const diffInDays = Math.floor(diffInHours / 24);
                  return `Há ${diffInDays} dia${diffInDays > 1 ? 's' : ''}`;
                };

                return (
                  <div key={index} className={`flex items-center space-x-3 p-3 ${bgColor} rounded-lg`}>
                    <IconComponent className={`w-5 h-5 ${iconColor}`} />
                    <div>
                      <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                      <p className="text-xs text-gray-600">{activity.description}</p>
                      <p className="text-xs text-gray-500">{formatTimestamp(activity.timestamp)}</p>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="text-center py-8">
                <Activity className={`w-12 h-12 mx-auto mb-4 ${isDarkMode ? 'text-gray-600' : 'text-gray-400'}`} />
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Nenhuma atividade recente encontrada
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderSectionContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return renderDashboardContent();
      case 'notifications':
        return <HierarchicalNotifications isDarkMode={isDarkMode} />;
      case 'users':
        return <UserManagement isDarkMode={isDarkMode} />;
      case 'landlords':
        return <LandlordValidation isDarkMode={isDarkMode} />;
      case 'moderators':
        return <ModeratorManagement isDarkMode={isDarkMode} />;
      case 'equipment':
        return <ModerationDashboard isDarkMode={isDarkMode} />;
      case 'bi-validation':
        return <ModeratorBiValidation isDarkMode={isDarkMode} />;
      case 'payment-validation':
        return <PaymentValidation isDarkMode={isDarkMode} />;
      case 'categories':
        return <CategoryManagement isDarkMode={isDarkMode} />;
      case 'content-management':
        return <ContentManagement isDarkMode={isDarkMode} />;
      case 'reports':
        return <ReportsAndAnalytics isDarkMode={isDarkMode} />;
      case 'system-config':
        return <SystemConfiguration isDarkMode={isDarkMode} />;
      case 'profile':
        return <AdminProfile isDarkMode={isDarkMode} />;
      case 'messages':
        return <AdminMessages isDarkMode={isDarkMode} />;
      case 'settings':
        return (
          <div className={`p-6 rounded-lg shadow-sm ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <h2 className={`text-2xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Configurações
            </h2>
            <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Configurações do sistema serão implementadas aqui
            </p>
          </div>
        );
      default:
        return renderDashboardContent();
    }
  };

  return (
    <AdminLayout
      isDarkMode={isDarkMode}
      activeSection={activeSection}
      onSectionChange={setActiveSection}
    >
      {renderSectionContent()}
    </AdminLayout>
  );
};

export default HierarchicalAdminDashboard;
