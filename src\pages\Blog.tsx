import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Calendar, User, Tag, Search, Filter, ArrowRight, Clock,
  Eye, Heart, Share2, MessageCircle, Bookmark, TrendingUp
} from 'lucide-react';
import { Button } from '../components/ui/button';
import BackButton from '../components/ui/BackButton';

interface BlogProps {
  isDarkMode: boolean;
}

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  authorAvatar: string;
  publishDate: string;
  readTime: string;
  category: string;
  tags: string[];
  image: string;
  views: number;
  likes: number;
  comments: number;
  featured: boolean;
}

interface BlogCategory {
  id: string;
  name: string;
  description: string;
  count: number;
  color: string;
}

const Blog: React.FC<BlogProps> = ({ isDarkMode }) => {
  const navigate = useNavigate();
  const { postId } = useParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('recent');
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [categories, setCategories] = useState<BlogCategory[]>([]);
  const [loading, setLoading] = useState(true);

  // Mock data - em produção viria de uma API/CMS
  useEffect(() => {
    const mockCategories: BlogCategory[] = [
      { id: 'all', name: 'Todos', description: 'Todos os posts', count: 12, color: '#3569b0' },
      { id: 'equipamentos', name: 'Equipamentos', description: 'Novidades e reviews', count: 5, color: '#10B981' },
      { id: 'industria', name: 'Indústria', description: 'Tendências do setor', count: 3, color: '#F59E0B' },
      { id: 'tecnologia', name: 'Tecnologia', description: 'Inovações tecnológicas', count: 2, color: '#EF4444' },
      { id: 'sustentabilidade', name: 'Sustentabilidade', description: 'Práticas sustentáveis', count: 2, color: '#8B5CF6' }
    ];

    const mockPosts: BlogPost[] = [
      {
        id: '1',
        title: 'O Futuro dos Equipamentos de Construção em Angola',
        excerpt: 'Descubra as tendências que estão moldando o setor de equipamentos pesados no país e como a tecnologia está revolucionando a indústria.',
        content: 'Conteúdo completo do post...',
        author: 'Carlos Mendes',
        authorAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=facearea&facepad=2&w=64&h=64&q=80',
        publishDate: '2024-01-20',
        readTime: '5 min',
        category: 'industria',
        tags: ['construção', 'tecnologia', 'angola'],
        image: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?auto=format&fit=crop&w=800&q=80',
        views: 1250,
        likes: 89,
        comments: 23,
        featured: true
      },
      {
        id: '2',
        title: 'Como Escolher a Retroescavadeira Ideal para Seu Projeto',
        excerpt: 'Guia completo para selecionar o equipamento certo considerando tipo de obra, terreno e orçamento disponível.',
        content: 'Conteúdo completo do post...',
        author: 'Ana Silva',
        authorAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?auto=format&fit=facearea&facepad=2&w=64&h=64&q=80',
        publishDate: '2024-01-18',
        readTime: '8 min',
        category: 'equipamentos',
        tags: ['retroescavadeira', 'guia', 'projetos'],
        image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?auto=format&fit=crop&w=800&q=80',
        views: 890,
        likes: 67,
        comments: 15,
        featured: false
      },
      {
        id: '3',
        title: 'Sustentabilidade na Construção: Equipamentos Eco-Friendly',
        excerpt: 'Conheça as inovações em equipamentos sustentáveis que estão reduzindo o impacto ambiental na construção civil.',
        content: 'Conteúdo completo do post...',
        author: 'João Santos',
        authorAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=facearea&facepad=2&w=64&h=64&q=80',
        publishDate: '2024-01-15',
        readTime: '6 min',
        category: 'sustentabilidade',
        tags: ['sustentabilidade', 'meio ambiente', 'inovação'],
        image: 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?auto=format&fit=crop&w=800&q=80',
        views: 654,
        likes: 45,
        comments: 8,
        featured: false
      },
      {
        id: '4',
        title: 'Digitalização na Gestão de Frotas de Equipamentos',
        excerpt: 'Como a tecnologia IoT e sistemas de gestão estão otimizando o controle e manutenção de equipamentos pesados.',
        content: 'Conteúdo completo do post...',
        author: 'Maria Costa',
        authorAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=facearea&facepad=2&w=64&h=64&q=80',
        publishDate: '2024-01-12',
        readTime: '7 min',
        category: 'tecnologia',
        tags: ['iot', 'gestão', 'digitalização'],
        image: 'https://images.unsplash.com/photo-1581092795360-fd1ca04f0952?auto=format&fit=crop&w=800&q=80',
        views: 432,
        likes: 34,
        comments: 12,
        featured: false
      }
    ];

    setCategories(mockCategories);
    setPosts(mockPosts);
    setLoading(false);
  }, []);

  // Filtrar posts
  const filteredPosts = posts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || post.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // Ordenar posts
  const sortedPosts = [...filteredPosts].sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return b.views - a.views;
      case 'liked':
        return b.likes - a.likes;
      case 'recent':
      default:
        return new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime();
    }
  });

  const featuredPosts = posts.filter(post => post.featured);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2" style={{borderColor: '#3569b0'}}></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen max-w-full overflow-x-hidden">
      <div className={`container mx-auto px-4 py-8 pt-24 max-w-7xl ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
        <div className="flex items-center space-x-4 mb-8">
          <BackButton isDarkMode={isDarkMode} />
          <div>
            <h1 className={`text-4xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Blog YMRentals</h1>
            <p className={`text-lg ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Insights, tendências e novidades do setor de equipamentos
            </p>
          </div>
        </div>

        {/* Posts em Destaque */}
        {featuredPosts.length > 0 && (
          <div className="mb-12">
            <h2 className={`text-2xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Posts em Destaque
            </h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {featuredPosts.map((post) => (
                <div
                  key={post.id}
                  className={`rounded-lg overflow-hidden shadow-lg cursor-pointer hover:shadow-xl transition-shadow ${
                    isDarkMode ? 'bg-gray-800' : 'bg-white'
                  }`}
                  onClick={() => navigate(`/blog/${post.id}`)}
                >
                  <div className="relative">
                    <img
                      src={post.image}
                      alt={post.title}
                      className="w-full h-64 object-cover"
                    />
                    <div className="absolute top-4 left-4">
                      <span
                        className="px-3 py-1 rounded-full text-white text-sm font-medium"
                        style={{backgroundColor: categories.find(c => c.id === post.category)?.color}}
                      >
                        {categories.find(c => c.id === post.category)?.name}
                      </span>
                    </div>
                  </div>
                  <div className="p-6">
                    <h3 className={`text-xl font-bold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {post.title}
                    </h3>
                    <p className={`text-sm mb-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {post.excerpt}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <img
                          src={post.authorAvatar}
                          alt={post.author}
                          className="w-8 h-8 rounded-full"
                        />
                        <div>
                          <p className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                            {post.author}
                          </p>
                          <div className="flex items-center space-x-2 text-xs text-gray-500">
                            <Calendar className="w-3 h-3" />
                            <span>{new Date(post.publishDate).toLocaleDateString('pt-AO')}</span>
                            <Clock className="w-3 h-3 ml-2" />
                            <span>{post.readTime}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Eye className="w-4 h-4" />
                          <span>{post.views}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Heart className="w-4 h-4" />
                          <span>{post.likes}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1 order-2 lg:order-1">
            {/* Busca */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-6 mb-6`}>
              <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Buscar Posts
              </h3>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Buscar..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full pl-10 pr-4 py-2 border rounded-lg ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  }`}
                />
              </div>
            </div>

            {/* Categorias */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-6 mb-6`}>
              <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Categorias
              </h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      selectedCategory === category.id
                        ? isDarkMode
                          ? 'bg-blue-900/30 text-blue-400'
                          : 'bg-blue-50 text-blue-600'
                        : isDarkMode
                        ? 'hover:bg-gray-700 text-gray-300'
                        : 'hover:bg-gray-100 text-gray-700'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{category.name}</span>
                      <span className="text-sm">{category.count}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Posts Populares */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-6`}>
              <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Posts Populares
              </h3>
              <div className="space-y-4">
                {posts.slice(0, 3).map((post) => (
                  <div
                    key={post.id}
                    className="flex space-x-3 cursor-pointer hover:opacity-75 transition-opacity"
                    onClick={() => navigate(`/blog/${post.id}`)}
                  >
                    <img
                      src={post.image}
                      alt={post.title}
                      className="w-16 h-16 rounded-lg object-cover"
                    />
                    <div className="flex-1">
                      <h4 className={`text-sm font-medium mb-1 line-clamp-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {post.title}
                      </h4>
                      <div className="flex items-center space-x-2 text-xs text-gray-500">
                        <TrendingUp className="w-3 h-3" />
                        <span>{post.views} visualizações</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Lista de Posts */}
          <div className="lg:col-span-3 order-1 lg:order-2">
            {/* Filtros e Ordenação */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 space-y-4 md:space-y-0">
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Todos os Posts ({sortedPosts.length})
              </h2>
              <div className="flex items-center space-x-4">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className={`px-3 py-2 border rounded-md ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                >
                  <option value="recent">Mais Recentes</option>
                  <option value="popular">Mais Populares</option>
                  <option value="liked">Mais Curtidos</option>
                </select>
              </div>
            </div>

            {/* Grid de Posts */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {sortedPosts.map((post) => (
                <div
                  key={post.id}
                  className={`rounded-lg overflow-hidden shadow-lg cursor-pointer hover:shadow-xl transition-shadow ${
                    isDarkMode ? 'bg-gray-800' : 'bg-white'
                  }`}
                  onClick={() => navigate(`/blog/${post.id}`)}
                >
                  <div className="relative">
                    <img
                      src={post.image}
                      alt={post.title}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute top-3 left-3">
                      <span
                        className="px-2 py-1 rounded-full text-white text-xs font-medium"
                        style={{backgroundColor: categories.find(c => c.id === post.category)?.color}}
                      >
                        {categories.find(c => c.id === post.category)?.name}
                      </span>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className={`text-lg font-bold mb-2 line-clamp-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {post.title}
                    </h3>
                    <p className={`text-sm mb-3 line-clamp-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {post.excerpt}
                    </p>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center space-x-2">
                        <img
                          src={post.authorAvatar}
                          alt={post.author}
                          className="w-6 h-6 rounded-full"
                        />
                        <span>{post.author}</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-1">
                          <Eye className="w-3 h-3" />
                          <span>{post.views}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MessageCircle className="w-3 h-3" />
                          <span>{post.comments}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {sortedPosts.length === 0 && (
              <div className="text-center py-12">
                <p className={`text-lg ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Nenhum post encontrado com os filtros selecionados.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Blog;
