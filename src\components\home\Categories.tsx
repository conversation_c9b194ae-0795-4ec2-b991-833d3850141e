import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronRight } from 'lucide-react';
import { Button } from '../ui/button';
import CategoryCard from '../CategoryCard';

interface Category {
  id: string;
  name: string;
  image: string;
  description: string;
}

interface CategoriesProps {
  isDarkMode: boolean;
  categories: Category[];
}

/**
 * Componente que exibe as categorias de equipamentos
 * Mostra uma grade de categorias com imagens e nomes
 */
const Categories: React.FC<CategoriesProps> = ({ isDarkMode, categories }) => {
  return (
    <section className="mb-16">
      <div className="flex justify-between items-center mb-8">
        <h2 className={`ml-10 text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'} text `}>Categorias</h2>
        <Link to="/categories">
          <Button
            variant="ghost"
            className="flex items-center text-blue-600 hover:text-blue-700"
            style={{color: '#3569b0'}}
          >
            Ver mais categorias <ChevronRight className="ml-2" />
          </Button>
        </Link>
      </div>
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4">
        {categories.map((category) => (
          <CategoryCard
            key={category.id}
            id={category.id}
            name={category.name}
            image={category.image}
            isDarkMode={isDarkMode}
          />
        ))}
      </div>
    </section>
  );
};

export default Categories;
