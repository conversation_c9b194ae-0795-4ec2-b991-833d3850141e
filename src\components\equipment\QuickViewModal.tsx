import React from 'react';
import { X, Star, ShoppingCart } from 'lucide-react';
import { Button } from '../ui/button';

interface Equipment {
  id: string;
  title: string;
  description: string;
  classCode: string;
  price: {
    daily: number;
    weekly: number;
    monthly: number;
    annual?: number;
  };
  image: string;
  category: string;
  subcategory: string;
  specifications: string[];
}

interface QuickViewModalProps {
  isDarkMode: boolean;
  equipment: Equipment | null;
  onClose: () => void;
  formatPrice: (price: number) => string;
}

/**
 * Componente de modal para visualização rápida de um equipamento
 * Exibe detalhes do equipamento sem sair da página de catálogo
 */
const QuickViewModal: React.FC<QuickViewModalProps> = ({
  isDarkMode,
  equipment,
  onClose,
  formatPrice
}) => {
  if (!equipment) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className={`relative w-full max-w-4xl rounded-lg shadow-lg overflow-hidden ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'}`}>
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 bg-white rounded-full p-1 shadow-md"
        >
          <X className="w-5 h-5 text-gray-500" />
        </button>

        <div className="flex flex-col md:flex-row">
          {/* Imagem */}
          <div className="md:w-1/2">
            <img
              src={equipment.image}
              alt={equipment.title}
              className="w-full h-64 md:h-full object-cover"
            />
          </div>

          {/* Conteúdo */}
          <div className="p-6 md:w-1/2 flex flex-col">
            <div className="mb-2">
              <span className={`text-xs px-2 py-1 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                {equipment.classCode}
              </span>
            </div>
            <h2 className="text-2xl font-bold mb-2">{equipment.title}</h2>
            <p className={`mb-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              {equipment.description}
            </p>

            {/* Avaliação */}
            <div className="flex items-center mb-4">
              <Star className="w-5 h-5 text-yellow-400" />
              <Star className="w-5 h-5 text-yellow-400" />
              <Star className="w-5 h-5 text-yellow-400" />
              <Star className="w-5 h-5 text-yellow-400" />
              <Star className="w-5 h-5 text-gray-300" />
              <span className="ml-2">(4.0)</span>
            </div>

            {/* Especificações */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-2">Especificações:</h3>
              <ul className="space-y-1">
                {equipment.specifications.map((spec, index) => (
                  <li key={index} className="flex items-center">
                    <div className={`w-2 h-2 rounded-full mr-2 ${isDarkMode ? 'bg-blue-400' : 'bg-blue-500'}`}></div>
                    {spec}
                  </li>
                ))}
              </ul>
            </div>

            {/* Preços */}
            <div className="mb-6 grid grid-cols-2 gap-4">
              <div className={`p-3 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <div className="text-sm mb-1">Diário</div>
                <div className="font-bold">{formatPrice(equipment.price.daily)}</div>
              </div>
              <div className={`p-3 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <div className="text-sm mb-1">Semanal</div>
                <div className="font-bold">{formatPrice(equipment.price.weekly)}</div>
              </div>
              <div className={`p-3 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <div className="text-sm mb-1">Mensal</div>
                <div className="font-bold">{formatPrice(equipment.price.monthly)}</div>
              </div>
              {equipment.price.annual && (
                <div className={`p-3 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="text-sm mb-1">Anual</div>
                  <div className="font-bold">{formatPrice(equipment.price.annual)}</div>
                </div>
              )}
            </div>

            {/* Botões */}
            <div className="mt-auto flex space-x-4">
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => window.location.href = `/equipment/${equipment.id}`}
              >
                Ver Detalhes Completos
              </Button>
              <Button
                variant="default"
                className="flex-1 bg-blue-600 hover:bg-blue-700"
              >
                <ShoppingCart className="w-4 h-4 mr-2" />
                Alugar Agora
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickViewModal;
