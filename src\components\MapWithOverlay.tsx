import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

interface MapWithOverlayProps {
  isDarkMode: boolean;
}

const MapWithOverlay: React.FC<MapWithOverlayProps> = ({ isDarkMode }) => {
  const navigate = useNavigate();
  const [selectedProvince, setSelectedProvince] = useState<string | null>(null);
  const [showProvinceList, setShowProvinceList] = useState(true);
  
  // Dados de províncias
  const provinces = [
    { id: 'luanda', name: 'Luanda', count: 145 },
    { id: 'benguela', name: '<PERSON><PERSON><PERSON>', count: 87 },
    { id: 'huambo', name: '<PERSON><PERSON><PERSON>', count: 56 },
    { id: 'huila', name: '<PERSON><PERSON><PERSON>', count: 42 },
    { id: 'cabinda', name: '<PERSON><PERSON><PERSON>', count: 23 },
    { id: 'malanje', name: '<PERSON><PERSON><PERSON>', count: 35 },
    { id: 'uige', name: '<PERSON><PERSON><PERSON>', count: 28 },
    { id: 'zaire', name: 'Zaire', count: 19 },
    { id: 'cuanza-norte', name: 'Cuanza Norte', count: 25 },
    { id: 'cuanza-sul', name: 'Cuanza Sul', count: 31 },
    { id: 'lunda-norte', name: 'Lunda Norte', count: 22 },
    { id: 'lunda-sul', name: 'Lunda Sul', count: 18 },
    { id: 'moxico', name: 'Moxico', count: 15 },
    { id: 'bie', name: 'Bié', count: 27 },
    { id: 'namibe', name: 'Namibe', count: 20 },
    { id: 'cunene', name: 'Cunene', count: 16 },
    { id: 'cuando-cubango', name: 'Cuando Cubango', count: 12 },
    { id: 'bengo', name: 'Bengo', count: 24 }
  ];
  
  // Dados de municípios
  const municipalityData: Record<string, Array<{id: string, name: string, count: number}>> = {
    'luanda': [
      { id: 'luanda-city', name: 'Luanda', count: 78 },
      { id: 'viana', name: 'Viana', count: 32 },
      { id: 'cacuaco', name: 'Cacuaco', count: 18 },
      { id: 'belas', name: 'Belas', count: 17 }
    ],
    'benguela': [
      { id: 'benguela-city', name: 'Benguela', count: 45 },
      { id: 'lobito', name: 'Lobito', count: 32 },
      { id: 'catumbela', name: 'Catumbela', count: 10 }
    ],
    'huambo': [
      { id: 'huambo-city', name: 'Huambo', count: 38 },
      { id: 'caala', name: 'Caála', count: 18 }
    ]
  };
  
  // URLs dos mapas para cada província
  const getMapUrl = () => {
    if (!selectedProvince) {
      return "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d8099881.771693946!2d12.8015811!3d-13.0568368!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1a51f24ecaad8b27%3A0x590a289d0d4a4e3d!2sAngola!5e0!3m2!1spt-PT!2sao!4v1710371547387!5m2!1spt-PT!2sao";
    }
    
    const provinceUrls: Record<string, string> = {
      'luanda': "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d254081.2196460341!2d13.099942651621974!3d-8.838842507333386!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1a51f15cdc8d2c7d%3A0x850c1c5c5ecc5a92!2sLuanda!5e0!3m2!1spt-PT!2sao!4v1710371547387!5m2!1spt-PT!2sao",
      'benguela': "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d249424.6851388919!2d13.204942651621974!3d-12.576342507333386!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1bafd46adf9fec69%3A0x9c2f23a4cde2e8de!2sBenguela!5e0!3m2!1spt-PT!2sao!4v1710371547387!5m2!1spt-PT!2sao",
      'huambo': "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d249424.6851388919!2d15.604942651621974!3d-12.776142507333386!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1bb4a1cef89ffd5b%3A0x9f296d527bcc8b8d!2sHuambo!5e0!3m2!1spt-PT!2sao!4v1710371547387!5m2!1spt-PT!2sao",
      'huila': "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d249424.6851388919!2d14.504942651621974!3d-14.922642507333386!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1bb0f3b2c9c6d587%3A0x9f296d527bcc8b8d!2sHu%C3%ADla!5e0!3m2!1spt-PT!2sao!4v1710371547387!5m2!1spt-PT!2sao",
      'cabinda': "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d249424.6851388919!2d12.104942651621974!3d-5.549642507333386!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1a5c1eca93a7e365%3A0x9f296d527bcc8b8d!2sCabinda!5e0!3m2!1spt-PT!2sao!4v1710371547387!5m2!1spt-PT!2sao"
    };
    
    return provinceUrls[selectedProvince] || provinceUrls['luanda'];
  };
  
  const handleProvinceClick = (provinceId: string) => {
    setSelectedProvince(provinceId);
    setShowProvinceList(false);
  };
  
  const handleMunicipalityClick = (municipalityId: string) => {
    navigate(`/equipment?location=${municipalityId}`);
  };
  
  const handleBackClick = () => {
    setSelectedProvince(null);
    setShowProvinceList(true);
  };
  
  return (
    <div className={`relative rounded-lg overflow-hidden ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h3 className="text-lg font-semibold">
          {selectedProvince
            ? `Equipamentos em ${provinces.find(p => p.id === selectedProvince)?.name}`
            : 'Pesquisar no Mapa'}
        </h3>
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setShowProvinceList(!showProvinceList)}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            {showProvinceList ? 'Esconder Lista' : 'Mostrar Lista'}
          </button>
          {selectedProvince && (
            <button
              onClick={handleBackClick}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Voltar ao mapa de Angola
            </button>
          )}
        </div>
      </div>
      
      <div className="relative" style={{ height: '600px' }}>
        {/* Mapa do Google */}
        <iframe
          src={getMapUrl()}
          width="100%"
          height="100%"
          style={{ border: 0 }}
          allowFullScreen
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
          className="absolute inset-0"
        ></iframe>
        
        {/* Lista de províncias ou municípios */}
        {showProvinceList && (
          <div className={`absolute top-4 right-4 p-4 rounded-lg shadow-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} max-h-[calc(100%-32px)] overflow-y-auto`}>
            <h4 className="font-semibold mb-2 text-sm">
              {selectedProvince ? 'Municípios' : 'Províncias'}
            </h4>
            <ul className="space-y-2">
              {selectedProvince 
                ? (municipalityData[selectedProvince] || []).map(item => (
                  <li key={item.id} className="flex items-center justify-between">
                    <button 
                      className={`text-left flex-1 px-3 py-2 rounded-md text-sm ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
                      onClick={() => handleMunicipalityClick(item.id)}
                    >
                      {item.name}
                    </button>
                    <span className={`flex items-center justify-center w-8 h-8 rounded-full ${isDarkMode ? 'bg-blue-600' : 'bg-blue-500'} text-white font-semibold text-xs`}>
                      {item.count}
                    </span>
                  </li>
                ))
                : provinces.map(item => (
                  <li key={item.id} className="flex items-center justify-between">
                    <button 
                      className={`text-left flex-1 px-3 py-2 rounded-md text-sm ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
                      onClick={() => handleProvinceClick(item.id)}
                    >
                      {item.name}
                    </button>
                    <span className={`flex items-center justify-center w-8 h-8 rounded-full ${isDarkMode ? 'bg-blue-600' : 'bg-blue-500'} text-white font-semibold text-xs`}>
                      {item.count}
                    </span>
                  </li>
                ))
              }
            </ul>
          </div>
        )}
      </div>
      
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          {selectedProvince
            ? 'Selecione um município para ver os equipamentos disponíveis.'
            : 'Selecione uma província para ver os equipamentos disponíveis em cada município.'}
        </p>
      </div>
    </div>
  );
};

export default MapWithOverlay;
