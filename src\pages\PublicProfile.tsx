import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Star, Package, Calendar, MapPin, Shield, MessageSquare,
  Heart, Flag, Share2, User, Briefcase, Clock
} from 'lucide-react';
import { Button } from '../components/ui/button';
import BackButton from '../components/ui/BackButton';

interface PublicProfileProps {
  isDarkMode: boolean;
}

interface PublicUser {
  id: string;
  name: string;
  avatar: string;
  location: string;
  memberSince: string;
  rating: number;
  totalRentals: number;
  responseTime: string;
  isVerified: boolean;
  bio: string;
  equipmentCount: number;
  completedRentals: number;
}

interface Equipment {
  id: string;
  title: string;
  image: string;
  price: number;
  rating: number;
  distance: string;
  isAvailable: boolean;
}

const PublicProfile: React.FC<PublicProfileProps> = ({ isDarkMode }) => {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('equipments');
  const [user, setUser] = useState<PublicUser | null>(null);
  const [userEquipments, setUserEquipments] = useState<Equipment[]>([]);
  const [loading, setLoading] = useState(true);

  // Mock data - em produção viria de uma API
  useEffect(() => {
    // Simular carregamento
    setTimeout(() => {
      const mockUser: PublicUser = {
        id: userId || '1',
        name: 'Carlos Mendes',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
        location: 'Luanda, Angola',
        memberSince: '2022-03-15',
        rating: 4.8,
        totalRentals: 127,
        responseTime: '2 horas',
        isVerified: true,
        bio: 'Especialista em equipamentos de construção com mais de 10 anos de experiência. Oferecemos equipamentos de alta qualidade e manutenção regular.',
        equipmentCount: 23,
        completedRentals: 127
      };

      const mockEquipments: Equipment[] = [
        {
          id: '1',
          title: 'Retroescavadeira CAT 320D',
          image: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?auto=format&fit=crop&w=600&q=80',
          price: 1250000,
          rating: 4.9,
          distance: '3.2 km',
          isAvailable: true
        },
        {
          id: '2',
          title: 'Empilhadeira Toyota 3T',
          image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?auto=format&fit=crop&w=600&q=80',
          price: 360000,
          rating: 4.7,
          distance: '3.2 km',
          isAvailable: true
        },
        {
          id: '3',
          title: 'Gerador Diesel 100kVA',
          image: 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?auto=format&fit=crop&w=600&q=80',
          price: 890000,
          rating: 4.8,
          distance: '3.2 km',
          isAvailable: false
        }
      ];

      setUser(mockUser);
      setUserEquipments(mockEquipments);
      setLoading(false);
    }, 1000);
  }, [userId]);

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />);
    }

    if (hasHalfStar) {
      stars.push(<Star key="half" className="w-4 h-4 text-yellow-400 fill-current opacity-50" />);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<Star key={`empty-${i}`} className="w-4 h-4 text-gray-300" />);
    }

    return stars;
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2" style={{borderColor: '#3569b0'}}></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Usuário não encontrado</h2>
          <Button onClick={() => navigate(-1)}>Voltar</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen max-w-full overflow-x-hidden">
      <div className={`container mx-auto px-4 py-8 pt-24 max-w-6xl ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
        <div className="flex items-center space-x-4 mb-8">
          <BackButton isDarkMode={isDarkMode} />
          <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Perfil Público</h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Informações do Usuário */}
          <div className="lg:col-span-1">
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-6 sticky top-24`}>
              {/* Avatar e Nome */}
              <div className="text-center mb-6">
                <div className="relative inline-block">
                  <img
                    src={user.avatar}
                    alt={user.name}
                    className="w-24 h-24 rounded-full mx-auto mb-4"
                  />
                  {user.isVerified && (
                    <div className="absolute -bottom-1 -right-1 bg-blue-600 rounded-full p-1">
                      <Shield className="w-4 h-4 text-white" />
                    </div>
                  )}
                </div>
                <h2 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {user.name}
                </h2>
                <div className="flex items-center justify-center space-x-1 mt-2">
                  <MapPin className="w-4 h-4 text-gray-400" />
                  <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {user.location}
                  </span>
                </div>
              </div>

              {/* Rating */}
              <div className="text-center mb-6">
                <div className="flex items-center justify-center space-x-1 mb-2">
                  {renderStars(user.rating)}
                </div>
                <p className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {user.rating.toFixed(1)} ({user.totalRentals} avaliações)
                </p>
              </div>

              {/* Estatísticas */}
              <div className="space-y-4 mb-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Membro desde
                    </span>
                  </div>
                  <span className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {new Date(user.memberSince).toLocaleDateString('pt-AO')}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Package className="w-4 h-4 text-gray-400" />
                    <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Equipamentos
                    </span>
                  </div>
                  <span className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {user.equipmentCount}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-gray-400" />
                    <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Tempo de resposta
                    </span>
                  </div>
                  <span className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {user.responseTime}
                  </span>
                </div>
              </div>

              {/* Ações */}
              <div className="space-y-3">
                <Button
                  className="w-full text-white"
                  style={{backgroundColor: '#3569b0'}}
                  onClick={() => navigate('/messages')}
                >
                  <MessageSquare className="w-4 h-4 mr-2" />
                  Enviar Mensagem
                </Button>

                <div className="flex space-x-2">
                  <Button variant="outline" className="flex-1">
                    <Heart className="w-4 h-4 mr-2" />
                    Seguir
                  </Button>
                  <Button variant="outline" size="icon">
                    <Share2 className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="icon">
                    <Flag className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Conteúdo Principal */}
          <div className="lg:col-span-2">
            {/* Bio */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-6 mb-6`}>
              <h3 className={`text-lg font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Sobre
              </h3>
              <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {user.bio}
              </p>
            </div>

            {/* Tabs */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg overflow-hidden`}>
              <div className={`border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                <nav className="flex">
                  <button
                    onClick={() => setActiveTab('equipments')}
                    className={`py-4 px-6 font-medium text-sm ${
                      activeTab === 'equipments'
                        ? 'border-b-2 text-blue-600'
                        : isDarkMode
                        ? 'text-gray-400 hover:text-gray-300'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                    style={activeTab === 'equipments' ? {borderColor: '#3569b0', color: '#3569b0'} : {}}
                  >
                    Equipamentos ({user.equipmentCount})
                  </button>
                  <button
                    onClick={() => setActiveTab('reviews')}
                    className={`py-4 px-6 font-medium text-sm ${
                      activeTab === 'reviews'
                        ? 'border-b-2 text-blue-600'
                        : isDarkMode
                        ? 'text-gray-400 hover:text-gray-300'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                    style={activeTab === 'reviews' ? {borderColor: '#3569b0', color: '#3569b0'} : {}}
                  >
                    Avaliações ({user.totalRentals})
                  </button>
                </nav>
              </div>

              <div className="p-6">
                {activeTab === 'equipments' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {userEquipments.map((equipment) => (
                      <div
                        key={equipment.id}
                        className={`rounded-lg border overflow-hidden cursor-pointer hover:shadow-md transition-shadow ${
                          isDarkMode ? 'border-gray-700 bg-gray-700' : 'border-gray-200 bg-gray-50'
                        }`}
                        onClick={() => navigate(`/equipment/${equipment.id}`)}
                      >
                        <div className="relative">
                          <img
                            src={equipment.image}
                            alt={equipment.title}
                            className="w-full h-40 object-cover"
                          />
                          <div className={`absolute top-2 left-2 px-2 py-1 rounded text-xs font-medium ${
                            equipment.isAvailable
                              ? 'bg-green-500 text-white'
                              : 'bg-red-500 text-white'
                          }`}>
                            {equipment.isAvailable ? 'Disponível' : 'Indisponível'}
                          </div>
                          <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-sm">
                            {equipment.price.toLocaleString('pt-AO')} Kz/dia
                          </div>
                        </div>
                        <div className="p-3">
                          <h4 className={`font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                            {equipment.title}
                          </h4>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-1">
                              {renderStars(equipment.rating).slice(0, 5).map((star, index) =>
                                React.cloneElement(star, { key: index, className: "w-3 h-3 text-yellow-400 fill-current" })
                              )}
                              <span className="text-xs text-gray-500">({equipment.rating})</span>
                            </div>
                            <span className="text-xs font-medium" style={{color: '#3569b0'}}>
                              {equipment.distance}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {activeTab === 'reviews' && (
                  <div className="space-y-4">
                    <p className={`text-center py-8 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      As avaliações são privadas e só podem ser vistas pelo próprio usuário.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PublicProfile;
