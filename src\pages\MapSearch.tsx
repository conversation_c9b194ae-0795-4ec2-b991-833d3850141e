import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { MapPin, Search, Filter, List, Grid as GridIcon } from 'lucide-react';
import { Button } from '../components/ui/button';
import EquipmentMap from '../components/EquipmentMap';
import Card from '../components/Card';

interface MapSearchProps {
  isDarkMode: boolean;
}

const MapSearch: React.FC<MapSearchProps> = ({ isDarkMode }) => {
  const navigate = useNavigate();
  const [selectedLocation, setSelectedLocation] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'map' | 'list' | 'grid'>('map');
  
  // Dados simulados de equipamentos
  const equipments = [
    {
      id: '1',
      title: 'Retroescavadeira CAT 420F2',
      description: 'Ideal para escavações e movimentação de terra',
      price: 'A partir de 250.000 Kz/dia',
      image: 'https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=600',
      location: 'Luanda, Viana'
    },
    {
      id: '2',
      title: 'Gerador 100kVA Diesel',
      description: 'Gerador diesel silenciado para eventos e obras',
      price: 'A partir de 150.000 Kz/dia',
      image: 'https://images.unsplash.com/photo-1586744666440-fef0dc5d0d18?auto=format&fit=crop&w=600',
      location: 'Luanda, Cacuaco'
    },
    {
      id: '3',
      title: 'Empilhadeira Toyota 8FD',
      description: 'Para movimentação de cargas em armazéns',
      price: 'A partir de 180.000 Kz/dia',
      image: 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?auto=format&fit=crop&w=600',
      location: 'Luanda, Talatona'
    },
    {
      id: '4',
      title: 'Guindaste Liebherr LTM',
      description: 'Guindaste para içamento de cargas pesadas',
      price: 'A partir de 450.000 Kz/dia',
      image: 'https://images.unsplash.com/photo-1541625602330-2277a4c46182?auto=format&fit=crop&w=600',
      location: 'Benguela, Lobito'
    },
    {
      id: '5',
      title: 'Compressor de Ar Atlas Copco',
      description: 'Compressor industrial para aplicações diversas',
      price: 'A partir de 120.000 Kz/dia',
      image: 'https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=600',
      location: 'Huambo, Caála'
    },
    {
      id: '6',
      title: 'Plataforma Elevatória JLG',
      description: 'Para trabalhos em altura com segurança',
      price: 'A partir de 200.000 Kz/dia',
      image: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?auto=format&fit=crop&w=600',
      location: 'Luanda, Belas'
    },
  ];
  
  // Filtrar equipamentos por localização selecionada
  const filteredEquipments = selectedLocation 
    ? equipments.filter(e => e.location.includes(selectedLocation))
    : equipments;
  
  return (
    <div className="container mx-auto px-4 py-8 pt-24">
      <h1 className={`text-2xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
        Pesquisar Equipamentos no Mapa
      </h1>
      
      {/* Search and Filter Bar */}
      <div className={`flex flex-col md:flex-row justify-between items-center mb-6 p-4 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
        <div className="flex items-center w-full md:w-auto mb-4 md:mb-0">
          <div className="relative flex-grow md:w-80">
            <input
              type="text"
              placeholder="Pesquisar equipamentos..."
              className={`w-full pl-10 pr-4 py-2 rounded-md border ${
                isDarkMode 
                  ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' 
                  : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
              } focus:outline-none focus:ring-2 focus:ring-blue-500`}
            />
            <Search className="absolute left-3 top-2.5 text-gray-400 w-5 h-5" />
          </div>
          <Button variant="outline" className="ml-2">
            <Filter className="w-5 h-5 mr-2" />
            Filtros
          </Button>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Visualizar:</span>
          <Button 
            variant={viewMode === 'map' ? 'default' : 'outline'} 
            size="sm"
            onClick={() => setViewMode('map')}
          >
            <MapPin className="w-4 h-4 mr-1" />
            Mapa
          </Button>
          <Button 
            variant={viewMode === 'list' ? 'default' : 'outline'} 
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="w-4 h-4 mr-1" />
            Lista
          </Button>
          <Button 
            variant={viewMode === 'grid' ? 'default' : 'outline'} 
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <GridIcon className="w-4 h-4 mr-1" />
            Grade
          </Button>
        </div>
      </div>
      
      {/* Content based on view mode */}
      {viewMode === 'map' && (
        <div className="mb-8">
          <EquipmentMap isDarkMode={isDarkMode} />
        </div>
      )}
      
      {viewMode === 'list' && (
        <div className="space-y-4 mb-8">
          {filteredEquipments.map((item) => (
            <div 
              key={item.id} 
              className={`flex flex-col md:flex-row border rounded-lg overflow-hidden ${
                isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
              }`}
            >
              <div className="md:w-1/4 h-48 md:h-auto">
                <img 
                  src={item.image} 
                  alt={item.title} 
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="p-4 md:p-6 flex flex-col justify-between flex-grow">
                <div>
                  <h3 className={`text-xl font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                    {item.title}
                  </h3>
                  <p className={`mb-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    {item.description}
                  </p>
                  <div className="flex items-center mb-4">
                    <MapPin className="w-4 h-4 text-gray-400 mr-1" />
                    <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      {item.location}
                    </span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <p className="font-semibold text-cyan-600">{item.price}</p>
                  <div className="flex space-x-2">
                    <Button size="sm" onClick={() => navigate(`/equipment/${item.id}`)}>
                      Ver Detalhes
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      
      {viewMode === 'grid' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {filteredEquipments.map((item) => (
            <Card
              key={item.id}
              title={item.title}
              description={item.description}
              price={item.price}
              image={item.image}
              location={item.location}
              buttonText="Ver Detalhes"
              buttonLink={`/equipment/${item.id}`}
              isDarkMode={isDarkMode}
            />
          ))}
        </div>
      )}
      
      {/* Pagination */}
      <div className="flex justify-center mt-8">
        <div className="flex space-x-1">
          <Button variant="outline" size="sm" disabled>Anterior</Button>
          <Button variant="default" size="sm">1</Button>
          <Button variant="outline" size="sm">2</Button>
          <Button variant="outline" size="sm">3</Button>
          <Button variant="outline" size="sm">Próxima</Button>
        </div>
      </div>
    </div>
  );
};

export default MapSearch;
