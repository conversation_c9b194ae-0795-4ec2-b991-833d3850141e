import React, { useState } from 'react';
import {
  Calendar,
  MapPin,
  Clock,
  DollarSign,
  User,
  Star,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  Download,
  Filter,
  Search,
  TrendingUp,
  TrendingDown,
  BarChart3
} from 'lucide-react';
import { Button } from '../components/ui/button';
import BackButton from '../components/ui/BackButton';

interface HistoryProps {
  isDarkMode: boolean;
}

interface HistoryItem {
  id: string;
  type: 'rental' | 'listing' | 'payment' | 'review';
  title: string;
  description: string;
  date: string;
  amount?: number;
  status: 'success' | 'pending' | 'failed' | 'cancelled';
  equipmentName?: string;
  equipmentImage?: string;
  customerName?: string;
  rating?: number;
  category: 'income' | 'expense' | 'activity';
}

const History: React.FC<HistoryProps> = ({ isDarkMode }) => {
  const [activeFilter, setActiveFilter] = useState<'all' | 'income' | 'expense' | 'activity'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');

  const historyItems: HistoryItem[] = [
    {
      id: '1',
      type: 'rental',
      title: 'Aluguel Concluído',
      description: 'Retroescavadeira CAT 420F2 alugada por João Silva',
      date: '2024-01-20T14:30:00',
      amount: 1250000,
      status: 'success',
      equipmentName: 'Retroescavadeira CAT 420F2',
      equipmentImage: 'https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=600',
      customerName: 'João Silva',
      rating: 5,
      category: 'income'
    },
    {
      id: '2',
      type: 'payment',
      title: 'Pagamento Recebido',
      description: 'Pagamento de 1.250.000 Kz via transferência bancária',
      date: '2024-01-20T10:15:00',
      amount: 1250000,
      status: 'success',
      category: 'income'
    },
    {
      id: '3',
      type: 'review',
      title: 'Nova Avaliação',
      description: 'João Silva avaliou seu equipamento com 5 estrelas',
      date: '2024-01-20T16:45:00',
      status: 'success',
      equipmentName: 'Retroescavadeira CAT 420F2',
      customerName: 'João Silva',
      rating: 5,
      category: 'activity'
    },
    {
      id: '4',
      type: 'rental',
      title: 'Novo Aluguel',
      description: 'Empilhadeira Toyota 8FD alugada por Maria Santos',
      date: '2024-01-18T09:20:00',
      amount: 360000,
      status: 'pending',
      equipmentName: 'Empilhadeira Toyota 8FD',
      equipmentImage: 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?auto=format&fit=crop&w=600',
      customerName: 'Maria Santos',
      category: 'income'
    },
    {
      id: '5',
      type: 'listing',
      title: 'Anúncio Publicado',
      description: 'Guindaste Liebherr LTM foi publicado com sucesso',
      date: '2024-01-15T11:30:00',
      status: 'success',
      equipmentName: 'Guindaste Liebherr LTM',
      equipmentImage: 'https://images.unsplash.com/photo-1541625602330-2277a4c46182?auto=format&fit=crop&w=600',
      category: 'activity'
    },
    {
      id: '6',
      type: 'payment',
      title: 'Taxa da Plataforma',
      description: 'Taxa de 5% sobre aluguel de Retroescavadeira',
      date: '2024-01-20T14:35:00',
      amount: -62500,
      status: 'success',
      category: 'expense'
    },
    {
      id: '7',
      type: 'rental',
      title: 'Aluguel Cancelado',
      description: 'Compressor Atlas Copco - cancelado pelo cliente',
      date: '2024-01-12T08:15:00',
      amount: 240000,
      status: 'cancelled',
      equipmentName: 'Compressor Atlas Copco',
      customerName: 'Ana Ferreira',
      category: 'income'
    },
    {
      id: '8',
      type: 'payment',
      title: 'Reembolso Processado',
      description: 'Reembolso de 240.000 Kz para Ana Ferreira',
      date: '2024-01-12T10:30:00',
      amount: -240000,
      status: 'success',
      category: 'expense'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'failed':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4" />;
      case 'pending':
        return <AlertCircle className="w-4 h-4" />;
      case 'failed':
        return <XCircle className="w-4 h-4" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4" />;
      default:
        return <AlertCircle className="w-4 h-4" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'rental':
        return <Calendar className="w-5 h-5" style={{color: '#3569b0'}} />;
      case 'payment':
        return <DollarSign className="w-5 h-5 text-green-500" />;
      case 'review':
        return <Star className="w-5 h-5 text-yellow-500" />;
      case 'listing':
        return <BarChart3 className="w-5 h-5" style={{color: '#3569b0'}} />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  const filteredItems = historyItems.filter(item => {
    const matchesFilter = activeFilter === 'all' || item.category === activeFilter;
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (item.equipmentName && item.equipmentName.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         (item.customerName && item.customerName.toLowerCase().includes(searchTerm.toLowerCase()));

    // Filtro de data (simplificado para demo)
    const itemDate = new Date(item.date);
    const now = new Date();
    const daysDiff = Math.floor((now.getTime() - itemDate.getTime()) / (1000 * 60 * 60 * 24));

    let matchesDate = true;
    if (dateRange === '7d') matchesDate = daysDiff <= 7;
    else if (dateRange === '30d') matchesDate = daysDiff <= 30;
    else if (dateRange === '90d') matchesDate = daysDiff <= 90;

    return matchesFilter && matchesSearch && matchesDate;
  });

  const totalIncome = historyItems
    .filter(item => item.category === 'income' && item.amount && item.amount > 0)
    .reduce((sum, item) => sum + (item.amount || 0), 0);

  const totalExpenses = Math.abs(historyItems
    .filter(item => item.category === 'expense' && item.amount && item.amount < 0)
    .reduce((sum, item) => sum + (item.amount || 0), 0));

  const netIncome = totalIncome - totalExpenses;

  const filters = [
    { key: 'all', label: 'Todos', count: historyItems.length },
    { key: 'income', label: 'Receitas', count: historyItems.filter(i => i.category === 'income').length },
    { key: 'expense', label: 'Despesas', count: historyItems.filter(i => i.category === 'expense').length },
    { key: 'activity', label: 'Atividades', count: historyItems.filter(i => i.category === 'activity').length }
  ];

  return (
    <div className="min-h-screen max-w-full overflow-x-hidden">
      <div className="container mx-auto px-4 py-8 pt-24 max-w-6xl">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <BackButton isDarkMode={isDarkMode} />
            <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Histórico</h1>
          </div>
        <div className="flex items-center space-x-2">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value as any)}
            className={`px-3 py-2 border rounded-md text-sm ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value="7d">Últimos 7 dias</option>
            <option value="30d">Últimos 30 dias</option>
            <option value="90d">Últimos 90 dias</option>
            <option value="all">Todo período</option>
          </select>
          <select
            className={`px-3 py-2 border rounded-md text-sm ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
            style={{borderColor: '#3569b0'}}
            onChange={(e) => {
              if (e.target.value) {
                console.log('Exportar como:', e.target.value);
                e.target.value = ''; // Reset selection
              }
            }}
          >
            <option value="">Exportar como...</option>
            <option value="pdf">PDF</option>
            <option value="excel">Excel</option>
            <option value="csv">CSV</option>
          </select>
          <Button
            variant="outline"
            size="sm"
            style={{borderColor: '#3569b0', color: '#3569b0'}}
          >
            <Download className="w-4 h-4 mr-1" />
            Baixar
          </Button>
        </div>
      </div>

      {/* Resumo Financeiro */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Receitas</p>
              <p className={`text-2xl font-bold text-green-600`}>
                {totalIncome.toLocaleString('pt-AO')} Kz
              </p>
            </div>
            <div className="p-2 bg-green-500 rounded-full">
              <TrendingUp className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>

        <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Despesas</p>
              <p className={`text-2xl font-bold text-red-600`}>
                {totalExpenses.toLocaleString('pt-AO')} Kz
              </p>
            </div>
            <div className="p-2 bg-red-500 rounded-full">
              <TrendingDown className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>

        <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Lucro Líquido</p>
              <p className={`text-2xl font-bold ${netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {netIncome.toLocaleString('pt-AO')} Kz
              </p>
            </div>
            <div className={`p-2 rounded-full ${netIncome >= 0 ? 'bg-green-500' : 'bg-red-500'}`}>
              <DollarSign className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Filtros e Busca */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 space-y-4 md:space-y-0">
        {/* Filtros */}
        <div className="overflow-x-auto scrollbar-hide">
          <div className="flex space-x-4 min-w-max px-1">
            {filters.map((filter) => (
              <button
                key={filter.key}
                onClick={() => setActiveFilter(filter.key as any)}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors flex-shrink-0 whitespace-nowrap flex items-center space-x-2 ${
                  activeFilter === filter.key
                    ? 'text-white'
                    : isDarkMode
                    ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
                style={activeFilter === filter.key ? {backgroundColor: '#3569b0'} : {}}
              >
                <span>{filter.label}</span>
                <span className={`px-1.5 py-0.5 rounded-full text-xs ${
                  activeFilter === filter.key
                    ? 'bg-white/20 text-white'
                    : isDarkMode
                    ? 'bg-gray-700 text-gray-300'
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {filter.count}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Busca */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Buscar no histórico..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`pl-10 pr-4 py-2 border rounded-md w-full md:w-64 ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
            }`}
          />
        </div>
      </div>

      {/* Lista do Histórico */}
      <div className="space-y-4">
        {filteredItems.length > 0 ? (
          filteredItems.map((item) => (
            <div
              key={item.id}
              className={`rounded-lg shadow-md p-4 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}
            >
              <div className="flex items-start space-x-4">
                {/* Ícone do Tipo */}
                <div className={`p-2 rounded-full ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  {getTypeIcon(item.type)}
                </div>

                {/* Conteúdo Principal */}
                <div className="flex-1">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {item.title}
                        </h3>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                          {getStatusIcon(item.status)}
                          <span className="ml-1 capitalize">
                            {item.status === 'success' ? 'Sucesso' :
                             item.status === 'pending' ? 'Pendente' :
                             item.status === 'failed' ? 'Falhou' : 'Cancelado'}
                          </span>
                        </span>
                      </div>
                      <p className={`text-sm mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                        {item.description}
                      </p>
                      <div className="flex items-center space-x-4 text-xs">
                        <div className="flex items-center space-x-1">
                          <Clock className="w-3 h-3 text-gray-400" />
                          <span className={isDarkMode ? 'text-gray-400' : 'text-gray-500'}>
                            {new Date(item.date).toLocaleString('pt-AO')}
                          </span>
                        </div>
                        {item.customerName && (
                          <div className="flex items-center space-x-1">
                            <User className="w-3 h-3 text-gray-400" />
                            <span className={isDarkMode ? 'text-gray-400' : 'text-gray-500'}>
                              {item.customerName}
                            </span>
                          </div>
                        )}
                        {item.rating && (
                          <div className="flex items-center space-x-1">
                            <Star className="w-3 h-3 text-yellow-400 fill-current" />
                            <span className={isDarkMode ? 'text-gray-400' : 'text-gray-500'}>
                              {item.rating}/5
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Valor e Ações */}
                    <div className="flex flex-col items-end space-y-2">
                      {item.amount && (
                        <div className="text-right">
                          <p className={`font-bold ${
                            item.amount > 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {item.amount > 0 ? '+' : ''}{item.amount.toLocaleString('pt-AO')} Kz
                          </p>
                        </div>
                      )}
                      <div className="flex flex-wrap items-center gap-1">
                        <Button variant="outline" size="sm" className="flex-shrink-0">
                          <Eye className="w-3 h-3 mr-1" />
                          Ver
                        </Button>
                        {(item.type === 'rental' || item.type === 'payment') && (
                          <Button variant="outline" size="sm" className="flex-shrink-0">
                            <Download className="w-3 h-3 mr-1" />
                            PDF
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Imagem do Equipamento (se houver) */}
                  {item.equipmentImage && (
                    <div className="mt-3 flex items-center space-x-3">
                      <img
                        src={item.equipmentImage}
                        alt={item.equipmentName}
                        className="w-12 h-12 rounded-lg object-cover"
                      />
                      <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>
                        {item.equipmentName}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className={`rounded-lg shadow-md p-8 text-center ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <div className="text-gray-400 mb-4">
              <BarChart3 className="w-16 h-16 mx-auto" />
            </div>
            <h3 className={`text-lg font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Nenhum item encontrado
            </h3>
            <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Não há itens no histórico que correspondam aos filtros selecionados.
            </p>
          </div>
        )}
      </div>
      </div>
    </div>
  );
};

export default History;
