import React, { useState, useEffect } from 'react';
import { DayPicker } from 'react-day-picker';
import { format, differenceInDays, addDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  X, Calendar, Clock, CreditCard, Upload, FileText, AlertCircle,
  CheckCircle, Loader2, Info, Calculator
} from 'lucide-react';
import { Button } from './ui/button';
import { useAuth } from '../contexts/AuthContext';
import apiService from '../services/api';
import 'react-day-picker/dist/style.css';

interface RentalBookingModalProps {
  isOpen: boolean;
  onClose: () => void;
  equipment: any;
  isDarkMode?: boolean;
}

export default function RentalBookingModal({ 
  isOpen, 
  onClose, 
  equipment, 
  isDarkMode = false 
}: RentalBookingModalProps) {
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Dados do formulário
  const [selectedRange, setSelectedRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({ from: undefined, to: undefined });
  
  const [startTime, setStartTime] = useState('09:00');
  const [endTime, setEndTime] = useState('17:00');
  const [paymentMethod, setPaymentMethod] = useState('RECEIPT');
  const [paymentReference, setPaymentReference] = useState('');
  const [paymentReceipt, setPaymentReceipt] = useState<File | null>(null);
  const [maxRentalDays, setMaxRentalDays] = useState(30);

  // Cálculos
  const [totalDays, setTotalDays] = useState(0);
  const [totalAmount, setTotalAmount] = useState(0);
  const [dailyRate, setDailyRate] = useState(0);

  useEffect(() => {
    if (equipment) {
      setDailyRate(equipment.price || 0);
      setMaxRentalDays(equipment.maxRentalDays || 30);
    }
  }, [equipment]);

  useEffect(() => {
    if (selectedRange.from && selectedRange.to) {
      const days = differenceInDays(selectedRange.to, selectedRange.from) + 1;
      setTotalDays(days);
      
      // Calcular valor total baseado no período
      let amount = 0;
      switch (equipment?.pricePeriod) {
        case 'HOURLY':
          amount = dailyRate * days * 8; // 8 horas por dia
          break;
        case 'WEEKLY':
          const weeks = Math.ceil(days / 7);
          amount = dailyRate * weeks;
          break;
        case 'MONTHLY':
          const months = Math.ceil(days / 30);
          amount = dailyRate * months;
          break;
        default: // DAILY
          amount = dailyRate * days;
          break;
      }
      setTotalAmount(amount);
    } else {
      setTotalDays(0);
      setTotalAmount(0);
    }
  }, [selectedRange, dailyRate, equipment?.pricePeriod]);

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB
        setError('Arquivo deve ter menos de 5MB');
        return;
      }
      
      if (!file.type.startsWith('image/') && file.type !== 'application/pdf') {
        setError('Apenas imagens e PDFs são aceitos');
        return;
      }

      setPaymentReceipt(file);
      setError('');
    }
  };

  const validateStep = (step: number) => {
    setError('');
    
    switch (step) {
      case 1:
        if (!selectedRange.from || !selectedRange.to) {
          setError('Selecione as datas de início e fim');
          return false;
        }
        
        if (totalDays > maxRentalDays) {
          setError(`Período máximo de aluguel é ${maxRentalDays} dias`);
          return false;
        }
        
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (selectedRange.from < today) {
          setError('Data de início não pode ser no passado');
          return false;
        }
        
        return true;
        
      case 2:
        if (paymentMethod === 'REFERENCE' && !paymentReference) {
          setError('Informe a referência de pagamento');
          return false;
        }
        
        if (paymentMethod === 'RECEIPT' && !paymentReceipt) {
          setError('Faça upload do comprovativo de pagamento');
          return false;
        }
        
        return true;
        
      default:
        return true;
    }
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return;
    
    setIsLoading(true);
    setError('');

    try {
      // Upload do comprovativo se necessário
      let receiptUrl = '';
      if (paymentMethod === 'RECEIPT' && paymentReceipt) {
        const formData = new FormData();
        formData.append('file', paymentReceipt);
        
        const uploadResponse = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:3000'}/upload/document`, {
          method: 'POST',
          body: formData,
        });
        
        if (uploadResponse.ok) {
          const uploadResult = await uploadResponse.json();
          receiptUrl = uploadResult.url;
        }
      }

      // Criar aluguel
      const rentalData = {
        equipmentId: equipment.id,
        startDate: selectedRange.from!.toISOString().split('T')[0],
        endDate: selectedRange.to!.toISOString().split('T')[0],
        startTime,
        endTime,
        totalAmount,
        dailyRate,
        pricePeriod: equipment.pricePeriod,
        maxRentalDays,
        paymentMethod,
        paymentReference: paymentMethod === 'REFERENCE' ? paymentReference : undefined,
      };

      const rental = await apiService.createRental(rentalData);

      // Upload do comprovativo se criado com sucesso
      if (paymentMethod === 'RECEIPT' && receiptUrl) {
        await apiService.uploadPaymentReceipt(rental.id, receiptUrl);
      }

      setSuccess('Aluguel criado com sucesso!');
      setCurrentStep(4); // Tela de sucesso
      
    } catch (error: any) {
      setError(error.message || 'Erro ao criar aluguel');
    } finally {
      setIsLoading(false);
    }
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Selecione as Datas
        </h3>
        <div className={`border rounded-lg p-4 ${isDarkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-300 bg-white'}`}>
          <DayPicker
            mode="range"
            selected={selectedRange}
            onSelect={(range: any) => setSelectedRange(range)}
            disabled={{ before: new Date() }}
            locale={ptBR}
            className="mx-auto"
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Hora de Início
          </label>
          <input
            type="time"
            value={startTime}
            onChange={(e) => setStartTime(e.target.value)}
            className={`w-full px-3 py-2 border rounded-md ${isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'}`}
          />
        </div>
        <div>
          <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Hora de Fim
          </label>
          <input
            type="time"
            value={endTime}
            onChange={(e) => setEndTime(e.target.value)}
            className={`w-full px-3 py-2 border rounded-md ${isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'}`}
          />
        </div>
      </div>

      {totalDays > 0 && (
        <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
          <div className="flex items-center mb-2">
            <Calculator className="w-4 h-4 mr-2 text-blue-500" />
            <span className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Resumo do Aluguel
            </span>
          </div>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>Período:</span>
              <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>{totalDays} dias</span>
            </div>
            <div className="flex justify-between">
              <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>Taxa {equipment?.pricePeriod?.toLowerCase()}:</span>
              <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>{dailyRate.toLocaleString()} Kz</span>
            </div>
            <div className="flex justify-between font-semibold pt-2 border-t border-gray-300">
              <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>Total:</span>
              <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>{totalAmount.toLocaleString()} Kz</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div>
        <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Método de Pagamento
        </h3>
        
        <div className="space-y-3">
          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="radio"
              name="paymentMethod"
              value="REFERENCE"
              checked={paymentMethod === 'REFERENCE'}
              onChange={(e) => setPaymentMethod(e.target.value)}
              className="text-blue-600"
            />
            <div>
              <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Pagamento por Referência
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Pague usando uma referência bancária
              </div>
            </div>
          </label>
          
          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="radio"
              name="paymentMethod"
              value="RECEIPT"
              checked={paymentMethod === 'RECEIPT'}
              onChange={(e) => setPaymentMethod(e.target.value)}
              className="text-blue-600"
            />
            <div>
              <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Comprovativo de Pagamento
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Faça upload do comprovativo (requer validação)
              </div>
            </div>
          </label>
        </div>
      </div>

      {paymentMethod === 'REFERENCE' && (
        <div>
          <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Referência de Pagamento
          </label>
          <input
            type="text"
            value={paymentReference}
            onChange={(e) => setPaymentReference(e.target.value)}
            placeholder="Digite a referência de pagamento"
            className={`w-full px-3 py-2 border rounded-md ${isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'}`}
          />
        </div>
      )}

      {paymentMethod === 'RECEIPT' && (
        <div>
          <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Comprovativo de Pagamento
          </label>
          <div className="flex items-center justify-center w-full">
            <label htmlFor="receipt-upload" className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer ${isDarkMode ? 'border-gray-600 bg-gray-700 hover:bg-gray-600' : 'border-gray-300 bg-gray-50 hover:bg-gray-100'}`}>
              <div className="flex flex-col items-center justify-center pt-5 pb-6">
                <Upload className={`w-8 h-8 mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                <p className={`mb-2 text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  <span className="font-semibold">Clique para enviar</span> comprovativo
                </p>
                <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  PDF ou imagens (máx. 5MB)
                </p>
              </div>
              <input
                id="receipt-upload"
                type="file"
                accept=".pdf,image/*"
                onChange={handleFileUpload}
                className="hidden"
              />
            </label>
          </div>
          
          {paymentReceipt && (
            <div className={`mt-3 p-3 rounded border ${isDarkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-300 bg-gray-50'}`}>
              <div className="flex items-center">
                <FileText className="w-4 h-4 mr-2 text-gray-500" />
                <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  {paymentReceipt.name}
                </span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );

  if (!isOpen) return null;

  // Verificar se o usuário é um locatário (apenas locatários podem alugar)
  if (user?.userType === 'LANDLORD') {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className={`max-w-md w-full rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6`}>
          <div className="text-center">
            <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h3 className={`text-xl font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Acesso Negado
            </h3>
            <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'} mb-4`}>
              Apenas locatários podem alugar equipamentos. Locadores não podem alugar equipamentos.
            </p>
            <Button onClick={onClose} className="bg-red-600 hover:bg-red-700 text-white">
              Fechar
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`max-w-2xl w-full max-h-[90vh] overflow-y-auto rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Alugar Equipamento
            </h2>
            <button
              onClick={onClose}
              className={`p-2 rounded-full hover:bg-gray-100 ${isDarkMode ? 'hover:bg-gray-700' : ''}`}
            >
              <X className={`w-5 h-5 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
            </button>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center mb-8">
            {[1, 2, 3].map((step) => (
              <React.Fragment key={step}>
                <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  currentStep >= step 
                    ? 'bg-blue-600 text-white' 
                    : isDarkMode ? 'bg-gray-600 text-gray-300' : 'bg-gray-200 text-gray-600'
                }`}>
                  {currentStep > step ? <CheckCircle className="w-4 h-4" /> : step}
                </div>
                {step < 3 && (
                  <div className={`flex-1 h-1 mx-2 ${
                    currentStep > step 
                      ? 'bg-blue-600' 
                      : isDarkMode ? 'bg-gray-600' : 'bg-gray-200'
                  }`} />
                )}
              </React.Fragment>
            ))}
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-md">
              <div className="flex items-center">
                <AlertCircle className="w-4 h-4 mr-2" />
                <span className="text-sm">{error}</span>
              </div>
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="mb-4 p-3 bg-green-50 border border-green-200 text-green-700 rounded-md">
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2" />
                <span className="text-sm">{success}</span>
              </div>
            </div>
          )}

          {/* Step Content */}
          {currentStep === 1 && renderStep1()}
          {currentStep === 2 && renderStep2()}
          {currentStep === 3 && (
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h3 className={`text-xl font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Aluguel Criado com Sucesso!
              </h3>
              <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'} mb-4`}>
                Seu pedido de aluguel foi enviado e está aguardando aprovação.
              </p>
              <Button onClick={onClose} className="bg-blue-600 hover:bg-blue-700 text-white">
                Fechar
              </Button>
            </div>
          )}

          {/* Action Buttons */}
          {currentStep < 3 && (
            <div className="flex justify-between mt-8">
              <Button
                variant="outline"
                onClick={currentStep === 1 ? onClose : () => setCurrentStep(currentStep - 1)}
                disabled={isLoading}
              >
                {currentStep === 1 ? 'Cancelar' : 'Voltar'}
              </Button>
              
              <Button
                onClick={currentStep === 2 ? handleSubmit : handleNext}
                disabled={isLoading}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Processando...
                  </>
                ) : currentStep === 2 ? 'Finalizar Aluguel' : 'Próximo'}
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
