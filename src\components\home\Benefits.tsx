import React from 'react';
import { Clock, Star, Users } from 'lucide-react';

interface BenefitsProps {
  isDarkMode: boolean;
}

/**
 * Componente que exibe os benefícios da plataforma
 * Mostra três cards com ícones e descrições dos principais benefícios
 */
const Benefits: React.FC<BenefitsProps> = ({ isDarkMode }) => {
  // Array de benefícios para facilitar manutenção e expansão
  const benefitsList = [
    {
      icon: <Clock className="w-8 h-8 mb-2" style={{color: '#3569b0'}} />,
      title: "<PERSON><PERSON>pid<PERSON> e Fácil",
      description: "Processo de aluguel simplificado"
    },
    {
      icon: <Star className="w-8 h-8 mb-2" style={{color: '#3569b0'}} />,
      title: "Qualidade Garantida",
      description: "Equipamentos certificados"
    },
    {
      icon: <Users className="w-8 h-8 mb-2" style={{color: '#3569b0'}} />,
      title: "Suporte 24/7",
      description: "Assistência técnica disponível"
    }
  ];

  return (
    <section className="mb-16">
      <h2 className={`text-2xl font-bold mb-8 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>Nossos Benefícios</h2>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        {benefitsList.map((benefit, index) => (
          <div
            key={index}
            className={`rounded-lg p-4 shadow-md border transition-all duration-200 flex flex-col items-center text-center hover:shadow-lg ${
              isDarkMode
                ? 'bg-neutral-800 text-white border-gray-700 hover:border-blue-500'
                : 'bg-white text-gray-800 border-gray-100 hover:border-blue-400'
            }`}
            onMouseEnter={(e) => e.currentTarget.style.borderColor = '#3569b0'}
            onMouseLeave={(e) => e.currentTarget.style.borderColor = isDarkMode ? '#374151' : '#f3f4f6'}
          >
            {benefit.icon}
            <h3 className="text-lg font-semibold mb-1">{benefit.title}</h3>
            <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>{benefit.description}</p>
          </div>
        ))}
      </div>
    </section>
  );
};

export default Benefits;
