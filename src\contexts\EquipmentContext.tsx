import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import apiService from '../services/api';

interface Equipment {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  pricePeriod: 'HOURLY' | 'DAILY' | 'WEEKLY' | 'MONTHLY';
  salePrice?: number;
  images: string[];
  videos: string[];
  documents?: string[];
  specifications?: Record<string, string>;
  isAvailable: boolean;
  ownerId: string;
  addressId?: string;
  createdAt: string;
  updatedAt: string;
  owner?: {
    id: string;
    fullName: string;
    profilePicture?: string;
    isEmailVerified: boolean;
    isPhoneVerified: boolean;
    userType: string;
  };
  address?: any;
  reviews?: any[];
}

interface EquipmentFilters {
  category?: string;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  location?: string;
  page?: number;
  limit?: number;
}

interface EquipmentContextType {
  equipment: Equipment[];
  loading: boolean;
  error: string | null;
  totalPages: number;
  currentPage: number;
  total: number;
  categories: string[];
  favorites: string[];
  fetchEquipment: (filters?: EquipmentFilters) => Promise<void>;
  fetchEquipmentById: (id: string) => Promise<Equipment | null>;
  createEquipment: (data: any) => Promise<Equipment>;
  updateEquipment: (id: string, data: any) => Promise<Equipment>;
  deleteEquipment: (id: string) => Promise<void>;
  toggleFavorite: (equipmentId: string) => void;
  isFavorite: (equipmentId: string) => boolean;
  isFavoriteLoading: (equipmentId: string) => boolean;
  fetchCategories: () => Promise<void>;
  fetchMyEquipment: () => Promise<Equipment[]>;
}

const EquipmentContext = createContext<EquipmentContextType | undefined>(undefined);

export const useEquipment = () => {
  const context = useContext(EquipmentContext);
  if (context === undefined) {
    throw new Error('useEquipment must be used within an EquipmentProvider');
  }
  return context;
};

interface EquipmentProviderProps {
  children: ReactNode;
}

export const EquipmentProvider: React.FC<EquipmentProviderProps> = ({ children }) => {
  const [equipment, setEquipment] = useState<Equipment[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [categories, setCategories] = useState<string[]>([]);
  const [favorites, setFavorites] = useState<string[]>([]);
  const [favoriteLoading, setFavoriteLoading] = useState<Set<string>>(new Set());

  // Carregar favoritos do backend (apenas se usuário estiver logado)
  useEffect(() => {
    const token = localStorage.getItem('authToken');
    if (token) {
      loadFavorites();
    } else {
      // Se não estiver logado, carregar do localStorage
      const storedFavorites = localStorage.getItem('equipmentFavorites');
      if (storedFavorites) {
        setFavorites(JSON.parse(storedFavorites));
      }
    }
  }, []);

  const loadFavorites = async () => {
    try {
      const token = localStorage.getItem('authToken');
      if (!token) return;

      const response = await apiService.getFavorites();
      const favoriteIds = response.map((fav: any) => fav.equipmentId);
      setFavorites(favoriteIds);
    } catch (err) {
      // Se falhar, usar localStorage como fallback
      const storedFavorites = localStorage.getItem('equipmentFavorites');
      if (storedFavorites) {
        setFavorites(JSON.parse(storedFavorites));
      }
    }
  };

  const fetchEquipment = async (filters?: EquipmentFilters) => {
    setLoading(true);
    setError(null);
    try {
      const response = await apiService.getEquipments(filters);
      setEquipment(response.data || []);
      setTotalPages(response.totalPages || 1);
      setCurrentPage(response.page || 1);
      setTotal(response.total || 0);
    } catch (err: any) {
      setError(err.message || 'Erro ao carregar equipamentos');
      setEquipment([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  const fetchEquipmentById = async (id: string): Promise<Equipment | null> => {
    try {
      const response = await apiService.getEquipmentById(id);
      return response;
    } catch (err: any) {
      setError(err.message || 'Erro ao carregar equipamento');
      return null;
    }
  };

  const createEquipment = async (data: any): Promise<Equipment> => {
    try {
      const response = await apiService.createEquipment(data);
      // Atualizar lista local
      setEquipment(prev => [response, ...prev]);
      return response;
    } catch (err: any) {
      setError(err.message || 'Erro ao criar equipamento');
      throw err;
    }
  };

  const updateEquipment = async (id: string, data: any): Promise<Equipment> => {
    try {
      const response = await apiService.updateEquipment(id, data);
      // Atualizar lista local
      setEquipment(prev => 
        prev.map(eq => eq.id === id ? { ...eq, ...response } : eq)
      );
      return response;
    } catch (err: any) {
      setError(err.message || 'Erro ao atualizar equipamento');
      throw err;
    }
  };

  const deleteEquipment = async (id: string): Promise<void> => {
    try {
      await apiService.deleteEquipment(id);
      // Remover da lista local
      setEquipment(prev => prev.filter(eq => eq.id !== id));
    } catch (err: any) {
      setError(err.message || 'Erro ao deletar equipamento');
      throw err;
    }
  };

  const toggleFavorite = async (equipmentId: string) => {
    // Verificar se já está processando este equipamento
    if (favoriteLoading.has(equipmentId)) {
      return;
    }

    const token = localStorage.getItem('authToken');

    if (!token) {
      // Se não estiver logado, usar localStorage
      const newFavorites = favorites.includes(equipmentId)
        ? favorites.filter(id => id !== equipmentId)
        : [...favorites, equipmentId];

      setFavorites(newFavorites);
      localStorage.setItem('equipmentFavorites', JSON.stringify(newFavorites));
      return;
    }

    // Marcar como loading
    setFavoriteLoading(prev => new Set(prev).add(equipmentId));

    try {
      const response = await apiService.toggleFavorite(equipmentId);

      // Atualizar estado baseado na resposta do servidor
      if (response.isFavorite) {
        setFavorites(prev => prev.includes(equipmentId) ? prev : [...prev, equipmentId]);
      } else {
        setFavorites(prev => prev.filter(id => id !== equipmentId));
      }

      // Atualizar localStorage
      const newFavorites = response.isFavorite
        ? (favorites.includes(equipmentId) ? favorites : [...favorites, equipmentId])
        : favorites.filter(id => id !== equipmentId);
      localStorage.setItem('equipmentFavorites', JSON.stringify(newFavorites));

    } catch (err: any) {
      console.error('Erro ao atualizar favoritos:', err);

      // Para erros, recarregar favoritos do servidor para sincronizar
      try {
        await loadFavorites();
      } catch (loadErr) {
        // Se falhar ao recarregar, usar fallback localStorage
        const newFavorites = favorites.includes(equipmentId)
          ? favorites.filter(id => id !== equipmentId)
          : [...favorites, equipmentId];

        setFavorites(newFavorites);
        localStorage.setItem('equipmentFavorites', JSON.stringify(newFavorites));
      }
    } finally {
      // Remover do loading
      setFavoriteLoading(prev => {
        const newSet = new Set(prev);
        newSet.delete(equipmentId);
        return newSet;
      });
    }
  };

  const isFavorite = (equipmentId: string): boolean => {
    return favorites.includes(equipmentId);
  };

  const isFavoriteLoading = (equipmentId: string): boolean => {
    return favoriteLoading.has(equipmentId);
  };

  const fetchCategories = async () => {
    try {
      const response = await apiService.getEquipments(); // Usar endpoint de categorias quando disponível
      const uniqueCategories = [...new Set(response.data?.map((eq: Equipment) => eq.category) || [])];
      setCategories(uniqueCategories);
    } catch (err: any) {
      console.error('Erro ao carregar categorias:', err);
    }
  };

  const fetchMyEquipment = async (): Promise<Equipment[]> => {
    try {
      // Implementar endpoint específico para equipamentos do usuário
      const response = await apiService.getEquipments(); // Temporário
      return response.data || [];
    } catch (err: any) {
      setError(err.message || 'Erro ao carregar seus equipamentos');
      return [];
    }
  };

  const value: EquipmentContextType = {
    equipment,
    loading,
    error,
    totalPages,
    currentPage,
    total,
    categories,
    favorites,
    fetchEquipment,
    fetchEquipmentById,
    createEquipment,
    updateEquipment,
    deleteEquipment,
    toggleFavorite,
    isFavorite,
    isFavoriteLoading,
    fetchCategories,
    fetchMyEquipment,
  };

  return (
    <EquipmentContext.Provider value={value}>
      {children}
    </EquipmentContext.Provider>
  );
};
