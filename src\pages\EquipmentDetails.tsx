import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { DayPicker } from 'react-day-picker';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  Star,
  MapPin,
  Calendar,
  Clock,
  Info,
  Shield,
  Phone,
  Heart,
  Share2,
  User,
  MessageSquare,
  ArrowLeft
} from 'lucide-react';
import { Button } from '../components/ui/button';
import BackButton from '../components/ui/BackButton';
import 'react-day-picker/dist/style.css';

interface EquipmentDetailsProps {
  isDarkMode: boolean;
}

export default function EquipmentDetails({ isDarkMode }: EquipmentDetailsProps) {
  const navigate = useNavigate();
  const [selectedRange, setSelectedRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined
  });

  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string>('');
  const timeSlots = [
    '08:00 - 12:00',
    '13:00 - 17:00',
    'Dia Inteiro'
  ];

  const disabledDays = [
    new Date(2024, 2, 20),
    new Date(2024, 2, 21),
    { from: new Date(2024, 2, 25), to: new Date(2024, 2, 28) }
  ];

  return (
    <div className="container mx-auto px-4 py-8 pt-24">
      {/* Botão de Voltar */}
      <div className="mb-6">
        <BackButton isDarkMode={isDarkMode} />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Column - Equipment Images and Info */}
        <div>
          <div className="relative">
            <img
              src="https://images.unsplash.com/photo-1504307651254-35b0e6e6921f?auto=format&fit=crop&w=800"
              alt="Retroescavadeira CAT"
              className="w-full h-96 object-cover rounded-lg"
            />
            <div className="absolute top-4 right-4 flex space-x-2">
              <Button
                size="icon"
                variant="outline"
                className={`${isDarkMode ? 'bg-gray-800/90 hover:bg-gray-700 border-gray-600' : 'bg-white/90 hover:bg-white'}`}
              >
                <Heart className="w-4 h-4" />
              </Button>
              <Button
                size="icon"
                variant="outline"
                className={`${isDarkMode ? 'bg-gray-800/90 hover:bg-gray-700 border-gray-600' : 'bg-white/90 hover:bg-white'}`}
              >
                <Share2 className="w-4 h-4" />
              </Button>
            </div>
          </div>

          <div className="mt-8">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h1 className={`text-3xl font-bold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Retroescavadeira CAT 420F2</h1>
                <div className={`flex items-center space-x-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  <MapPin className="w-4 h-4" />
                  <span>Luanda, Talatona</span>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold" style={{color: '#3569b0'}}>250.000 Kz</div>
                <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>por dia</div>
              </div>
            </div>

            <div className="flex items-center space-x-2 mb-6">
              <div className="flex items-center">
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <Star className="w-4 h-4 text-gray-300 fill-current" />
              </div>
              <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>(4.2 - 48 avaliações)</span>
            </div>

            <div className="space-y-6">
              <div>
                <h2 className={`text-xl font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Descrição</h2>
                <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  Retroescavadeira CAT 420F2 em excelente estado. Ideal para projetos de construção,
                  terraplanagem e escavações. Equipada com cabine climatizada, sistema hidráulico de alta
                  performance e baixo consumo de combustível.
                </p>
              </div>

              <div>
                <h2 className={`text-xl font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Especificações</h2>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Info className="w-4 h-4 text-gray-400" />
                    <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>Ano: 2020</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-gray-400" />
                    <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>Horímetro: 2.500h</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Shield className="w-4 h-4 text-gray-400" />
                    <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>Seguro incluso</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Phone className="w-4 h-4 text-gray-400" />
                    <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>Suporte 24/7</span>
                  </div>
                </div>
              </div>

              <div>
                <h2 className={`text-xl font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Proprietário</h2>
                <div className={`flex items-center space-x-4 p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                  <img
                    src="https://randomuser.me/api/portraits/men/32.jpg"
                    alt="Carlos Mendes"
                    className="w-16 h-16 rounded-full"
                  />
                  <div>
                    <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Carlos Mendes</h3>
                    <div className="flex items-center mt-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className={`text-sm ml-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>4.7 (23 avaliações)</span>
                    </div>
                    <div className="flex mt-2 space-x-2">
                      <Button variant="outline" size="sm" className="flex items-center">
                        <MessageSquare className="w-4 h-4 mr-1" />
                        Mensagem
                      </Button>
                      <Button variant="outline" size="sm" className="flex items-center">
                        <Phone className="w-4 h-4 mr-1" />
                        Contato
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Booking Calendar */}
        <div className={`rounded-lg shadow-lg p-6 sticky top-24 h-fit ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <div className="flex items-center justify-between mb-6">
            <h2 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Agendar Aluguel</h2>
            <div className="flex items-center">
              <img
                src="https://randomuser.me/api/portraits/men/32.jpg"
                alt="Carlos Mendes"
                className="w-8 h-8 rounded-full mr-2"
              />
              <div className="text-sm">
                <p className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Carlos Mendes</p>
                <div className="flex items-center">
                  <Star className="w-3 h-3 text-yellow-400 fill-current" />
                  <span className={`text-xs ml-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>4.7</span>
                </div>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Selecione as Datas
            </label>
            <div className={`border rounded-lg p-4 ${isDarkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-300 bg-white'}`}>
              <DayPicker
                mode="range"
                selected={selectedRange}
                onSelect={(range: any) => setSelectedRange(range)}
                disabled={disabledDays}
                locale={ptBR}
                className="mx-auto"
                styles={{
                  caption: { color: '#333' },
                  head_cell: { color: '#666' }
                }}
              />
            </div>
          </div>

          <div className="mb-6">
            <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Horário
            </label>
            <div className="grid grid-cols-3 gap-2">
              {timeSlots.map((slot) => (
                <Button
                  key={slot}
                  variant={selectedTimeSlot === slot ? 'default' : 'outline'}
                  onClick={() => setSelectedTimeSlot(slot)}
                  className="w-full"
                >
                  {slot}
                </Button>
              ))}
            </div>
          </div>

          <div className="space-y-4 mb-6">
            <div className="flex justify-between text-sm">
              <span className={isDarkMode ? 'text-gray-400' : 'text-gray-600'}>Valor diário</span>
              <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>250.000 Kz</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className={isDarkMode ? 'text-gray-400' : 'text-gray-600'}>Taxa de serviço</span>
              <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>25.000 Kz</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className={isDarkMode ? 'text-gray-400' : 'text-gray-600'}>Seguro</span>
              <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>Incluso</span>
            </div>
            <hr className={isDarkMode ? 'border-gray-600' : 'border-gray-200'} />
            <div className="flex justify-between font-semibold">
              <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>Total</span>
              <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>275.000 Kz</span>
            </div>
          </div>

          <Button
            className="w-full text-white"
            size="lg"
            style={{backgroundColor: '#3569b0'}}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2a5490'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3569b0'}
          >
            Concluir pedido de reserva
          </Button>

          <p className={`text-xs text-center mt-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            Você não será cobrado ainda. O pagamento será processado após a confirmação da disponibilidade.
          </p>
        </div>
      </div>
    </div>
  );
}