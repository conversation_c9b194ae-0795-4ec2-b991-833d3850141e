import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { DayPicker } from 'react-day-picker';
import { format, addDays, differenceInDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  Star,
  MapPin,
  Calendar,
  Clock,
  Info,
  Shield,
  Phone,
  Heart,
  Share2,
  User,
  MessageSquare,
  ArrowLeft,
  Loader2,
  CreditCard,
  Upload,
  FileText,
  AlertCircle
} from 'lucide-react';
import { Button } from '../components/ui/button';
import BackButton from '../components/ui/BackButton';
import RentalBookingModal from '../components/RentalBookingModal';
import { useAuth } from '../contexts/AuthContext';
import apiService from '../services/api';
import 'react-day-picker/dist/style.css';

interface EquipmentDetailsProps {
  isDarkMode: boolean;
}

export default function EquipmentDetails({ isDarkMode }: EquipmentDetailsProps) {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { user, isAuthenticated } = useAuth();
  const [equipment, setEquipment] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showBookingModal, setShowBookingModal] = useState(false);

  useEffect(() => {
    const loadEquipment = async () => {
      if (!id) {
        setError('ID do equipamento não fornecido');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const response = await apiService.getEquipmentById(id);
        setEquipment(response);
      } catch (err) {
        console.error('Erro ao carregar equipamento:', err);
        setError('Não foi possível carregar os detalhes do equipamento.');
      } finally {
        setLoading(false);
      }
    };

    loadEquipment();
  }, [id]);

  const handleBookingClick = () => {
    if (!isAuthenticated) {
      navigate('/auth');
      return;
    }

    // Verificar se o usuário é um locador (locadores não podem alugar)
    if (user?.userType === 'LANDLORD') {
      alert('Locadores não podem alugar equipamentos. Apenas locatários podem alugar.');
      return;
    }

    if (equipment?.ownerId === user?.id) {
      alert('Você não pode alugar seu próprio equipamento');
      return;
    }

    setShowBookingModal(true);
  };

  return (
    <div className="container mx-auto px-4 py-8 pt-24">
      {/* Botão de Voltar */}
      <div className="mb-6">
        <BackButton isDarkMode={isDarkMode} />
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center items-center py-20">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
          <span className={`ml-2 ${isDarkMode ? 'text-white' : 'text-gray-700'}`}>
            Carregando detalhes do equipamento...
          </span>
        </div>
      )}

      {/* Error State */}
      {error && !loading && (
        <div className="text-center py-20">
          <div className={`text-lg font-medium mb-2 ${isDarkMode ? 'text-red-400' : 'text-red-600'}`}>
            Ops! Algo deu errado
          </div>
          <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
            {error}
          </p>
          <Button
            onClick={() => navigate('/equipment')}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            Voltar aos Equipamentos
          </Button>
        </div>
      )}

      {/* Equipment Details */}
      {!loading && !error && equipment && (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Column - Equipment Images and Info */}
        <div>
          <div className="relative">
            <img
              src={equipment.images?.[0] || 'https://via.placeholder.com/800x400?text=' + encodeURIComponent(equipment.name)}
              alt={equipment.name}
              className="w-full h-96 object-cover rounded-lg"
            />
            <div className="absolute top-4 right-4 flex space-x-2">
              <Button
                size="icon"
                variant="outline"
                className={`${isDarkMode ? 'bg-gray-800/90 hover:bg-gray-700 border-gray-600' : 'bg-white/90 hover:bg-white'}`}
              >
                <Heart className="w-4 h-4" />
              </Button>
              <Button
                size="icon"
                variant="outline"
                className={`${isDarkMode ? 'bg-gray-800/90 hover:bg-gray-700 border-gray-600' : 'bg-white/90 hover:bg-white'}`}
              >
                <Share2 className="w-4 h-4" />
              </Button>
            </div>
          </div>

          <div className="mt-8">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h1 className={`text-3xl font-bold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{equipment.name}</h1>
                <div className={`flex items-center space-x-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  <MapPin className="w-4 h-4" />
                  <span>{equipment.Address?.city || 'Localização não informada'}</span>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold" style={{color: '#3569b0'}}>
                  {equipment.price ? `${equipment.price.toLocaleString()} Kz` : 'Preço sob consulta'}
                </div>
                <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {equipment.pricePeriod === 'DAILY' ? 'por dia' :
                   equipment.pricePeriod === 'HOURLY' ? 'por hora' :
                   equipment.pricePeriod === 'WEEKLY' ? 'por semana' :
                   equipment.pricePeriod === 'MONTHLY' ? 'por mês' : ''}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2 mb-6">
              <div className="flex items-center">
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <Star className="w-4 h-4 text-gray-300 fill-current" />
              </div>
              <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>(4.2 - 48 avaliações)</span>
            </div>

            <div className="space-y-6">
              <div>
                <h2 className={`text-xl font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Descrição</h2>
                <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  {equipment.description || 'Descrição não disponível para este equipamento.'}
                </p>
              </div>

              <div>
                <h2 className={`text-xl font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Especificações</h2>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Info className="w-4 h-4 text-gray-400" />
                    <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>Ano: 2020</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-gray-400" />
                    <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>Horímetro: 2.500h</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Shield className="w-4 h-4 text-gray-400" />
                    <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>Seguro incluso</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Phone className="w-4 h-4 text-gray-400" />
                    <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>Suporte 24/7</span>
                  </div>
                </div>
              </div>

              <div>
                <h2 className={`text-xl font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Proprietário</h2>
                <div className={`flex items-center space-x-4 p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                  <img
                    src="https://randomuser.me/api/portraits/men/32.jpg"
                    alt="Carlos Mendes"
                    className="w-16 h-16 rounded-full"
                  />
                  <div>
                    <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Carlos Mendes</h3>
                    <div className="flex items-center mt-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className={`text-sm ml-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>4.7 (23 avaliações)</span>
                    </div>
                    <div className="flex mt-2 space-x-2">
                      <Button variant="outline" size="sm" className="flex items-center">
                        <MessageSquare className="w-4 h-4 mr-1" />
                        Mensagem
                      </Button>
                      <Button variant="outline" size="sm" className="flex items-center">
                        <Phone className="w-4 h-4 mr-1" />
                        Contato
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Booking Panel */}
        <div className={`rounded-lg shadow-lg p-6 sticky top-24 h-fit ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <div className="flex items-center justify-between mb-6">
            <h2 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Alugar Equipamento</h2>
            <div className="flex items-center">
              <img
                src={equipment.owner?.profilePicture || "https://randomuser.me/api/portraits/men/32.jpg"}
                alt={equipment.owner?.fullName || "Proprietário"}
                className="w-8 h-8 rounded-full mr-2"
              />
              <div className="text-sm">
                <p className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {equipment.owner?.fullName || "Proprietário"}
                </p>
                <div className="flex items-center">
                  <Star className="w-3 h-3 text-yellow-400 fill-current" />
                  <span className={`text-xs ml-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>4.7</span>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-4 mb-6">
            <div className="flex justify-between text-sm">
              <span className={isDarkMode ? 'text-gray-400' : 'text-gray-600'}>
                Preço {equipment.pricePeriod === 'DAILY' ? 'diário' :
                       equipment.pricePeriod === 'HOURLY' ? 'por hora' :
                       equipment.pricePeriod === 'WEEKLY' ? 'semanal' :
                       equipment.pricePeriod === 'MONTHLY' ? 'mensal' : ''}
              </span>
              <span className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {equipment.price ? `${equipment.price.toLocaleString()} Kz` : 'Sob consulta'}
              </span>
            </div>

            {equipment.isAvailable ? (
              <div className="flex items-center text-green-600">
                <div className="w-2 h-2 bg-green-600 rounded-full mr-2"></div>
                <span className="text-sm">Disponível</span>
              </div>
            ) : (
              <div className="flex items-center text-red-600">
                <div className="w-2 h-2 bg-red-600 rounded-full mr-2"></div>
                <span className="text-sm">Indisponível</span>
              </div>
            )}
          </div>

          <Button
            onClick={handleBookingClick}
            disabled={!equipment.isAvailable || (user?.userType === 'LANDLORD')}
            className="w-full text-white mb-4"
            size="lg"
            style={{
              backgroundColor: (!equipment.isAvailable || user?.userType === 'LANDLORD') ? '#6b7280' : '#3569b0'
            }}
          >
            {!equipment.isAvailable ? 'Indisponível' :
             user?.userType === 'LANDLORD' ? 'Locadores não podem alugar' :
             'Alugar Agora'}
          </Button>

          <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
            <div className="flex items-center mb-2">
              <Info className="w-4 h-4 mr-2 text-blue-500" />
              <span className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Informações do Aluguel
              </span>
            </div>
            <ul className={`text-xs space-y-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              <li>• Pagamento por referência ou comprovativo</li>
              <li>• Comprovativo requer validação de moderador</li>
              <li>• Notificação de devolução automática</li>
              <li>• Suporte 24/7 durante o aluguel</li>
            </ul>
          </div>
        </div>
      </div>
      )}

      {/* Rental Booking Modal */}
      <RentalBookingModal
        isOpen={showBookingModal}
        onClose={() => setShowBookingModal(false)}
        equipment={equipment}
        isDarkMode={isDarkMode}
      />
    </div>
  );
}