import React from 'react';
import { Link } from 'react-router-dom';
import {
  Home,
  Package,
  MessageSquare,
  Heart,
  History,
  Settings,
  User,
  ShoppingCart,
  Bell,
  HelpCircle,
  FileText,
  Shield,
  LogOut,
  PlusCircle,
  BookOpen,
  Briefcase
} from 'lucide-react';

interface MenuProps {
  isDarkMode: boolean;
}

const Menu: React.FC<MenuProps> = ({ isDarkMode }) => {
  const menuSections = [
    {
      title: 'Navegação',
      items: [
        { icon: <Home className="w-5 h-5" />, label: 'Página Inicial', link: '/' },
        { icon: <Package className="w-5 h-5" />, label: 'Equipamentos', link: '/equipment' },
        { icon: <PlusCircle className="w-5 h-5" />, label: '<PERSON><PERSON><PERSON>', link: '/create-listing' },
        { icon: <ShoppingCart className="w-5 h-5" />, label: 'Carrin<PERSON>', link: '/cart', badge: '2' }
      ]
    },
    {
      title: 'Minh<PERSON> Conta',
      items: [
        { icon: <User className="w-5 h-5" />, label: 'Meu Perfil', link: '/profile' },
        { icon: <Bell className="w-5 h-5" />, label: 'Notificações', link: '/notifications', badge: '3' },
        { icon: <MessageSquare className="w-5 h-5" />, label: 'Mensagens', link: '/messages', badge: '5' },
        { icon: <Heart className="w-5 h-5" />, label: 'Favoritos', link: '/favorites' },
        { icon: <History className="w-5 h-5" />, label: 'Histórico', link: '/history' },
        { icon: <Package className="w-5 h-5" />, label: 'Minhas Rendas', link: '/my-rentals' }
      ]
    },
    {
      title: 'Empresa',
      items: [
        { icon: <BookOpen className="w-5 h-5" />, label: 'Blog', link: '/blog' },
        { icon: <Briefcase className="w-5 h-5" />, label: 'Carreiras', link: '/careers' }
      ]
    },
    {
      title: 'Configurações',
      items: [
        { icon: <Settings className="w-5 h-5" />, label: 'Configurações', link: '/settings' },
        { icon: <Shield className="w-5 h-5" />, label: 'Privacidade', link: '/privacy' },
        { icon: <HelpCircle className="w-5 h-5" />, label: 'Ajuda', link: '/help' },
        { icon: <FileText className="w-5 h-5" />, label: 'Termos de Uso', link: '/terms' }
      ]
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className={`text-2xl font-bold mb-8 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>Menu</h1>

      <div className="space-y-8">
        {menuSections.map((section, index) => (
          <div key={index} className="space-y-4">
            <h2 className={`text-lg font-semibold ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>{section.title}</h2>
            <div className={`rounded-lg overflow-hidden border ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              {section.items.map((item, itemIndex) => (
                <Link
                  key={itemIndex}
                  to={item.link}
                  className={`flex items-center justify-between p-4 ${isDarkMode ? 'text-white hover:bg-gray-800' : 'text-gray-700 hover:bg-gray-50'} ${
                    itemIndex !== section.items.length - 1 ? isDarkMode ? 'border-b border-gray-700' : 'border-b border-gray-200' : ''
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`${isDarkMode ? 'text-cyan-400' : 'text-cyan-500'}`}>
                      {item.icon}
                    </div>
                    <span>{item.label}</span>
                  </div>
                  {item.badge && (
                    <span className="bg-cyan-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                      {item.badge}
                    </span>
                  )}
                </Link>
              ))}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button className="flex items-center space-x-3 text-red-500 p-4 w-full rounded-lg hover:bg-red-50 dark:hover:bg-red-900/10">
          <LogOut className="w-5 h-5" />
          <span>Sair</span>
        </button>
      </div>
    </div>
  );
};

export default Menu;
