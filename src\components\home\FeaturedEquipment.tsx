import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronRight, ShoppingCart } from 'lucide-react';
import { Button } from '../ui/button';
import Card from '../Card';

interface Equipment {
  id: string;
  title: string;
  description: string;
  price: string;
  image: string;
}

interface FeaturedEquipmentProps {
  isDarkMode: boolean;
  equipment: Equipment[];
}

/**
 * Componente que exibe os equipamentos em destaque
 * Mostra uma grade de cards com equipamentos selecionados
 */
const FeaturedEquipment: React.FC<FeaturedEquipmentProps> = ({ isDarkMode, equipment }) => {
  return (
    <section>
      <div className="flex justify-between items-center mb-8">
        <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>Equipamentos em Destaque</h2>
        <Link to="/equipment">
          <Button
            variant="ghost"
            className="flex items-center text-blue-600 hover:text-blue-700"
            style={{color: '#3569b0'}}
          >
            Ver todos equipamentos <ChevronRight className="ml-2" />
          </Button>
        </Link>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {equipment.map((item) => (
          <Card
            key={item.id}
            title={item.title}
            description={item.description}
            price={item.price}
            image={item.image}
            buttonText="Alugar Agora"
            buttonLink={`/equipment/${item.id}`}
            secondaryButtonIcon={<ShoppingCart className="w-4 h-4" />}
            secondaryButtonLink="/cart"
            isDarkMode={isDarkMode}
          />
        ))}
      </div>
    </section>
  );
};

export default FeaturedEquipment;
