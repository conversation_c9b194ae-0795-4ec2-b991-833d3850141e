import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ChevronRight, ShoppingCart } from 'lucide-react';
import { Button } from '../ui/button';
import Card from '../Card';
import { useEquipment } from '../../contexts/EquipmentContext';
import { useAuth } from '../../contexts/AuthContext';

interface Equipment {
  id: string;
  title: string;
  description: string;
  price: string;
  image: string;
}

interface FeaturedEquipmentProps {
  isDarkMode: boolean;
  equipment: Equipment[];
}

/**
 * Componente que exibe os equipamentos em destaque
 * Mostra uma grade de cards com equipamentos selecionados
 */
const FeaturedEquipment: React.FC<FeaturedEquipmentProps> = ({ isDarkMode, equipment }) => {
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuth();
  const { toggleFavorite, isFavorite, isFavoriteLoading } = useEquipment();

  // Função para adicionar ao carrinho (protegida)
  const handleAddToCart = (equipmentId: string) => {
    if (!isAuthenticated) {
      navigate('/auth');
      return;
    }

    // Verificar se o usuário é um locador (locadores não podem adicionar ao carrinho)
    if (user?.userType === 'LANDLORD') {
      alert('Locadores não podem adicionar equipamentos ao carrinho. Apenas locatários podem alugar.');
      return;
    }

    // Encontrar o equipamento
    const equipmentItem = equipment.find(eq => eq.id === equipmentId);
    if (!equipmentItem) return;

    // Adicionar ao carrinho local (localStorage)
    const cart = JSON.parse(localStorage.getItem('cart') || '[]');
    const existingItem = cart.find((item: any) => item.id === equipmentId);

    if (existingItem) {
      existingItem.quantity += 1;
    } else {
      cart.push({
        id: equipmentId,
        name: equipmentItem.title,
        price: parseFloat(equipmentItem.price.replace(/[^\d,]/g, '').replace(',', '.')),
        image: equipmentItem.image,
        quantity: 1,
        period: 'daily'
      });
    }

    localStorage.setItem('cart', JSON.stringify(cart));
    alert(`${equipmentItem.title} adicionado ao carrinho!`);
  };

  // Função para favoritar (protegida)
  const handleToggleFavorite = (equipmentId: string) => {
    if (!isAuthenticated) {
      navigate('/auth');
      return;
    }
    toggleFavorite(equipmentId);
  };

  // Função para compartilhar (protegida)
  const handleShare = (equipment: any) => {
    if (!isAuthenticated) {
      navigate('/auth');
      return;
    }

    if (navigator.share) {
      navigator.share({
        title: equipment.title,
        text: equipment.description,
        url: `${window.location.origin}/equipment/${equipment.id}`
      });
    } else {
      navigator.clipboard.writeText(`${window.location.origin}/equipment/${equipment.id}`);
      alert('Link copiado para a área de transferência!');
    }
  };
  return (
    <section>
      <div className="flex justify-between items-center mb-8">
        <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>Equipamentos em Destaque</h2>
        <Link to="/equipment">
          <Button
            variant="ghost"
            className="flex items-center text-blue-600 hover:text-blue-700"
            style={{color: '#3569b0'}}
          >
            Ver todos equipamentos <ChevronRight className="ml-2" />
          </Button>
        </Link>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {equipment.map((item) => (
          <Card
            key={item.id}
            title={item.title}
            description={item.description}
            price={item.price}
            image={item.image}
            buttonText="Alugar Agora"
            buttonLink={`/equipment/${item.id}`}
            secondaryButtonIcon={<ShoppingCart className="w-4 h-4" />}
            isDarkMode={isDarkMode}
            requireAuth={true} // Alugar requer autenticação
            secondaryRequireAuth={true} // Adicionar ao carrinho requer autenticação
            equipmentId={item.id}
            onAddToCart={handleAddToCart}
            onToggleFavorite={handleToggleFavorite}
            onShare={handleShare}
            isFavorite={isFavorite(item.id)}
            isFavoriteLoading={isFavoriteLoading(item.id)}
            showQuickActions={true}
          />
        ))}
      </div>
    </section>
  );
};

export default FeaturedEquipment;
